# coding=utf-8
from ExecuteHana import ExecuteHana
from datetime import datetime, timedelta
import math
import random


def time_diff(start_time, end_time):
    """
    计算两个时间之间的时间差
    :param start_time: 开始时间，格式为 '%Y-%m-%d %H:%M:%S'
    :param end_time: 结束时间，格式为 '%Y-%m-%d %H:%M:%S'
    :return: 时间差，单位为秒
    """
    # start = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    # end = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    diff = (end_time - start_time).total_seconds()
    return diff


def get_base_data(crew_id, rq, type_class):
    sql = f"SELECT distinct RQ, CREW_ID, CREW_NAME, TYPE_CLASS_C, TYPE_CLASS,EXTEND_1, BEGIN_TIME, END_TIME FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = {crew_id} and rq like '%{rq}%' and TYPE_CLASS like '%{type_class}%'order by rq,BEGIN_TIME;"
    # print(sql)
    data_list = ExecuteHana().excute_sql(sql)

    return data_list


def cal_SJFXCQTS(crew_id, rq):
    return ExecuteHana().excute_sql(
        f"SELECT  count( distinct rq) FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = '{crew_id}' and rq like '%{rq}%' and TYPE_CLASS = '执飞航班'")[
        0][0]


def cal_SJ48XSXXQTS(crew_id, rq):
    return ExecuteHana().excute_sql(
        f"SELECT  count( distinct rq) FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = '{crew_id}' and rq like '%{rq}%' and TYPE_CLASS = '休息期'")[
        0][0]


# 计算实际飞行小时数
def cal_SJFXXS(crew_id, rq):
    sql = f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and type_class = '执飞航班' order by a.rq,a.begin_time"
    mxb = ExecuteHana().excute_sql(sql)
    time_count = 0
    for x in mxb:
        mxb_begin_time = x[4]
        mxb_end_time = x[5]
        tmp = time_diff(mxb_begin_time, mxb_end_time)
        time_count = time_count + tmp
    return round(time_count / 3600, 2)


def cal_SJFXCQTS(crew_id, rq):
    return ExecuteHana().excute_sql(
        f"SELECT  count( distinct rq) FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = '{crew_id}' and rq like '%{rq}%' and TYPE_CLASS = '执飞航班'")[
        0][0]


def cal_MNJJJSC(crew_id, rq):
    mxb = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and a.type_class like '%模拟%'  order by a.rq,a.begin_time")
    time_count = 0
    mnjdjts_list = []
    mnjcxts_list = []
    if len(mxb) > 0:
        for x in mxb:
            tc = x[3]
            rq = x[0]
            if tc == '模拟机-参训':
                mnjcxts_list.append(rq)
            else:
                mnjdjts_list.append(rq)
            mxb_begin_time = x[4]
            mxb_end_time = x[5]
            tmp = time_diff(mxb_begin_time, mxb_end_time)
            if tmp < 3600 * 4:
                tmp1 = tmp
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
            elif 3600 * 4 <= tmp < 3600 * 6:
                tmp1 = tmp - 15 * 60
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
            elif 3600 * 6 <= tmp < 3600 * 8:
                tmp1 = tmp - 30 * 60
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
            elif tmp >= 3600 * 8:
                tmp1 = tmp - 45 * 60
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
    mnjdjts = set(mnjdjts_list)
    mnjcxts = set(mnjcxts_list)
    len_mnjdjts = 0 if mnjdjts is None else len(mnjdjts)
    len_mnjcxts = 0 if mnjcxts is None else len(mnjcxts)

    return round(time_count / 3600, 2), len_mnjdjts, len_mnjcxts


def cal_MNJKHTS(crew_id, rq):
    mxb = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%'  order by a.rq,a.begin_time")
    time_count = 0
    if len(mxb) > 0:
        for x in range(len(mxb)):
            print(mxb[x])
            # if x + 1 < len(mxb):
            #     start = mxb[x + 1][4]
            #     end = mxb[x][5]
            #     tmp = time_diff(start, end)
            #     time_count = time_count + tmp

    # return round(time_count / 3600, 2)


def cal_MNJJJSC_plan(crew_id, rq):
    mxb = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL_plan a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and a.type_class like '%模拟%'  order by a.rq,a.begin_time")
    jhmnjdjts_list = []
    jhmnjcxts_list = []
    time_count = 0
    if len(mxb) > 0:
        for x in mxb:
            tc = x[3]
            rq = x[0]
            if tc == '模拟机-参训':
                jhmnjcxts_list.append(rq)
            else:
                jhmnjdjts_list.append(rq)
            # mxb_begin_time = datetime.strptime(x[4], "%Y-%m-%d %H:%M:%S")
            # mxb_end_time = datetime.strptime(x[5], "%Y-%m-%d %H:%M:%S")
            mxb_begin_time = x[4]
            mxb_end_time = x[5]
            tmp = time_diff(mxb_begin_time, mxb_end_time)
            if tmp < 3600 * 4:
                tmp1 = tmp
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
            elif 3600 * 4 <= tmp < 3600 * 6:
                tmp1 = tmp - 15 * 60
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
            elif 3600 * 6 <= tmp < 3600 * 8:
                tmp1 = tmp - 30 * 60
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
            elif tmp >= 3600 * 8:
                tmp1 = tmp - 45 * 60
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    # print(rq, tmp1)
                    time_count = time_count + tmp1
    jhmnjdjts = set(jhmnjdjts_list)
    jhmnjcxts = set(jhmnjcxts_list)
    len_jhmnjdjts = 0 if jhmnjdjts is None else len(jhmnjdjts)
    len_jhmnjcxts = 0 if jhmnjcxts is None else len(jhmnjcxts)
    return round(time_count / 3600, 2), len_jhmnjdjts, len_jhmnjcxts


# 数据清洗
def clean_data(crew_id, rq):
    sql = f"SELECT DISTINCT rq, crew_id, crew_name, type_class,TYPE_CLASS_B,TYPE_CLASS_C,EXTEND_1 FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE rq like '%{rq}%'  AND crew_id = {crew_id} ORDER BY rq, CASE WHEN type_class = '执飞航班' THEN 1 WHEN type_class = '备份' THEN 2 WHEN type_class = '模拟机-检查员' THEN 3 WHEN type_class = '模拟机-教员' THEN 4 WHEN type_class = '模拟机-参训' THEN 5 WHEN type_class = '外围训练' THEN 6 WHEN type_class = '体检' THEN 7 WHEN type_class = '理论培训' THEN 8 WHEN type_class = '考试' THEN 9 WHEN type_class = '值班' THEN 10 WHEN type_class = '技评会' THEN 11 WHEN type_class = '置位' THEN 12 WHEN type_class = '出差' THEN 13 WHEN type_class = '其他' THEN 14 WHEN type_class = '休假' THEN 15 WHEN type_class = '停飞' THEN 16 WHEN type_class = '休息期' THEN 17 ELSE 18 END, CASE WHEN TYPE_CLASS_B LIKE '%身体原因停飞%' OR TYPE_CLASS_B LIKE '%体检合格证到期%' THEN 1 WHEN TYPE_CLASS_B LIKE '%处罚%' THEN 2 WHEN TYPE_CLASS_B NOT LIKE '%身体原因停飞%' AND TYPE_CLASS_B NOT LIKE '%体检合格证到期%' AND TYPE_CLASS_B NOT LIKE '%处罚%' AND TYPE_CLASS_B not in ('停飞', '(模)停飞') THEN 3 WHEN TYPE_CLASS_B in ('停飞', '(模)停飞') THEN 4 ELSE 5 END, CASE WHEN EXTEND_1 LIKE '%领导%' THEN 1 WHEN EXTEND_1 LIKE '%飞行部%' THEN 2 ELSE 3 END ,CASE WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%理论-年度复训理论%' THEN 1 WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%生存%' THEN 2 WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%危险品%' THEN 3 WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%安保%' THEN 4 WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%安全%' THEN 5 WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%换季%' THEN 6 WHEN TYPE_CLASS = '理论培训' AND ((TYPE_CLASS_C NOT LIKE '%生存%' AND TYPE_CLASS_C NOT LIKE '%危险品%' AND TYPE_CLASS_C NOT LIKE '%安保%' AND TYPE_CLASS_C NOT LIKE '%安全%' AND TYPE_CLASS_C NOT LIKE '%换季%' AND TYPE_CLASS_C NOT LIKE '%理论-年度复训理论%') OR TYPE_CLASS_C IS NULL) THEN 7 ELSE 8 END"
    # print(sql)
    mxb = ExecuteHana().excute_sql(sql)

    # 保留的日期和数据
    reserved_dates = set()
    reserved_data = []
    # 统计每个 type_class 类型的个数
    type_class_counts = {}
    # 处理查询结果
    for row in mxb:
        rq, crew_id, crew_name, type_class, type_class_b, type_class_c, EXTEND_1 = row

        # 检查日期是否已保留
        if rq in reserved_dates:
            continue

        # 添加当前日期的数据
        reserved_data.append(row)
        reserved_dates.add(rq)
        # 统计 type_class 类型的个数
        if type_class in type_class_counts:
            type_class_counts[type_class] += 1
        else:
            type_class_counts[type_class] = 1

    # 输出结果
    # for row in reserved_data:
    #     print(row)
    # 输出每个 type_class 类型的个数
    # for type_class, count in type_class_counts.items():
    #     print(f"{type_class}: {count}")
    # print(type_class_counts)
    return reserved_data


def sy_result(crew_id, rq, field):
    sql = f"select yf,crew_id,crew_name,{field} from BIDM.DM_FXSLTS_REPLAY where yf = '{rq}'and crew_id = {crew_id}"
    # print(sql)
    pre_res = ExecuteHana().excute_sql(sql)
    if len(pre_res) > 0:
        res = pre_res[0]
        end_dict = {}
        end_dict['yf'] = 0 if res[0] is None else res[0]
        end_dict['crew_id'] = 0 if res[1] is None else res[1]
        end_dict['crew_name'] = 0 if res[2] is None else res[2]
        end_dict[field] = 0 if res[3] is None else res[3]

        return end_dict.get(field)


def tx_result(crew_id, rq, field):
    final_dict = {}
    if field in ['MNJJJSC', 'SJFXCQTS', 'GBGZRFXCQTS', 'SJCQRRJFXXS', 'LL48XSXXQTS', 'SJ48XSXXQTS', 'XXQPC',
                 'SJFXRRJFXXS']:
        MNJJJSC = cal_MNJJJSC(crew_id, rq)
        SJFXCQTS = cal_SJFXCQTS(crew_id, rq)
        SJFXXS = cal_SJFXXS(crew_id, rq)
        SJ48XSXXQTS = cal_SJ48XSXXQTS(crew_id, rq)
        # 模拟机实际带教检时间 MNJJJSC
        final_dict["MNJJJSC"] = MNJJJSC
        # 实际飞行出勤天数 SJFXCQTS
        final_dict["SJFXCQTS"] = SJFXCQTS
        # 干部工作日飞行出勤天数--GBGZRFXCQTS  未完成
        final_dict["GBGZRFXCQTS"] = 0
        # 实际出勤日的日均飞行小时 - -SJCQRRJFXXS
        final_dict["SJCQRRJFXXS"] = 0 if SJFXXS == 0 else round(SJFXXS / SJFXCQTS, 2)
        # 理论48小时休息期天数 - -LL48XSXXQTS
        final_dict["LL48XSXXQTS"] = math.ceil(SJFXCQTS * 0.4)
        # 实际48小时休息期天数 - -SJ48XSXXQTS
        final_dict["SJ48XSXXQTS"] = SJ48XSXXQTS
        # 休息期偏差 - -XXQPC
        final_dict["XXQPC"] = math.ceil(SJFXCQTS * 0.4) - SJ48XSXXQTS
        # 实际飞行日的日均飞行小时 - -SJFXRRJFXXS
        final_dict["SJFXRRJFXXS"] = 0 if SJFXXS == 0 else round(SJFXXS / SJFXCQTS, 2)
    elif field in ["LLPX_TOT", "YJSC", "WXPPX", "AB", "AQJY", "HJJY", "MNJFXLL", "LLPXQT", "JHLLPXSH", "LLPX_SH_TOT",
                   "YJSC_PXSH", "WXPPX_PXSH", "AB_PXSH", "AQJY_PXSH", "HJJY_PXSH", "MNJFXLL_PXSH", "LLPXQT_PXSH",
                   "LLPXSH_PC"]:
        sql = f"SELECT  rq,crew_id,crew_name,CASE WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%生存%' THEN '应急生存' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%危险品%' THEN '危险品培训' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%安保%' THEN '安保' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%安全%' THEN '安全教育' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%换季%' THEN '换季教育' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '理论-年度复训理论%' THEN '模拟机复训理论' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C NOT LIKE '%生存%' AND TYPE_CLASS_C NOT LIKE '%危险品%' AND TYPE_CLASS_C NOT LIKE '%安保%' AND TYPE_CLASS_C NOT LIKE '%安全%' AND TYPE_CLASS_C NOT LIKE '%换季%' AND TYPE_CLASS_C NOT LIKE '理论-年度复训理论%' THEN '理论培训其他' ELSE '' end as LLPX_TYPE FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = {crew_id} and rq like '%{rq}%' and TYPE_CLASS = '理论培训'"
        LLPX = ExecuteHana().excute_sql(sql)
        YJSC = 0
        WXPPX = 0
        AB = 0
        AQJY = 0
        HJJY = 0
        MNJFXLL = 0
        LLPXQT = 0
        for x in LLPX:
            if '应急生存' in x[3]:
                YJSC = YJSC + 1
            elif '危险品培训' in x[3]:
                WXPPX = WXPPX + 1
            elif '安保' in x[3]:
                AB = AB + 1
            elif '安全教育' in x[3]:
                AQJY = AQJY + 1
            elif '换季教育' in x[3]:
                HJJY = HJJY + 1
            elif '模拟机复训理论' in x[3]:
                MNJFXLL = MNJFXLL + 1
            elif '理论培训其他' in x[3]:
                LLPXQT = LLPXQT + 1
        LLPX_SH = clean_data(crew_id, rq)
        YJSC_PXSH = 0
        WXPPX_PXSH = 0
        AB_PXSH = 0
        AQJY_PXSH = 0
        HJJY_PXSH = 0
        MNJFXLL_PXSH = 0
        LLPXQT_PXSH = 0
        LLPX_SH_TOT = 0
        if len(LLPX_SH) > 0:
            for sh in LLPX_SH:
                if sh[3] == '理论培训':
                    LLPX_SH_TOT = LLPX_SH_TOT + 1
                    if sh[5] is None:
                        sh[5] = '0'
                    if '生存' in sh[5]:
                        YJSC_PXSH = YJSC_PXSH + 1
                    elif '危险品' in sh[5]:
                        WXPPX_PXSH = WXPPX_PXSH + 1
                    elif '安保' in sh[5]:
                        AB_PXSH = AB_PXSH + 1
                    elif '安全' in sh[5]:
                        AQJY_PXSH = AQJY_PXSH + 1
                    elif '换季' in sh[5]:
                        HJJY_PXSH = HJJY_PXSH + 1
                    elif '年度复训理论' in sh[5]:
                        MNJFXLL_PXSH = MNJFXLL_PXSH + 1
                    else:
                        LLPXQT_PXSH = LLPXQT_PXSH + 1

        LLPX_TOT = len(LLPX)
        # 实际理论培训损耗偏差 - -LLPXSH_PC
        LLPXSH_PC = LLPX_SH_TOT - LLPX_TOT
        final_dict["LLPX_TOT"] = LLPX_TOT
        final_dict["YJSC"] = YJSC
        final_dict["WXPPX"] = WXPPX
        final_dict["AB"] = AB
        final_dict["AQJY"] = AQJY
        final_dict["HJJY"] = HJJY
        final_dict["MNJFXLL"] = MNJFXLL
        final_dict["LLPXQT"] = LLPXQT
        final_dict["JHLLPXSH"] = 2.1
        final_dict["LLPX_SH_TOT"] = LLPX_SH_TOT
        final_dict["YJSC_PXSH"] = YJSC_PXSH
        final_dict["WXPPX_PXSH"] = WXPPX_PXSH
        final_dict["AB_PXSH"] = AB_PXSH
        final_dict["AQJY_PXSH"] = AQJY_PXSH
        final_dict["HJJY_PXSH"] = HJJY_PXSH
        final_dict["MNJFXLL_PXSH"] = MNJFXLL_PXSH
        final_dict["LLPXQT_PXSH"] = LLPXQT_PXSH
        final_dict["LLPXSH_PC"] = LLPXSH_PC
    elif field in ["JHXJTS", "JHXJTS_BWG", "TYXJTS", "SJXJTS", "XJ_PC"]:
        xjts = get_base_data(crew_id, rq, '休假')
        TYXJTS = len(xjts)
        SJXJTS = 0
        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '休假':
                SJXJTS = SJXJTS + 1

        final_dict["JHXJTS"] = 2
        final_dict["JHXJTS_BWG"] = 2
        final_dict["TYXJTS"] = TYXJTS
        final_dict["SJXJTS"] = SJXJTS
        final_dict["XJ_PC"] = SJXJTS - TYXJTS
    elif field in ["CC_TS", "CC_PXSH"]:
        ccts = get_base_data(crew_id, rq, '出差')
        CC_TS = len(ccts)
        CC_PXSH = 0
        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '出差':
                CC_PXSH = CC_PXSH + 1
        final_dict["CC_TS"] = CC_TS
        final_dict["CC_PXSH"] = CC_PXSH
    elif field in ["TJ_TS", "TJ_PXSH", "TJ_PC"]:
        tjts = get_base_data(crew_id, rq, '体检')
        TJ_TS = len(tjts)
        TJ_PXSH = 0
        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '体检':
                TJ_PXSH = TJ_PXSH + 1
        final_dict["TJ_TS"] = TJ_TS
        final_dict["TJ_PXSH"] = TJ_PXSH
        final_dict["TJ_PC"] = TJ_PXSH - TJ_TS
    elif field in ["JPHCS", "JPHCS_PXSH"]:
        jphts = get_base_data(crew_id, rq, '技评会')
        JPHCS = len(jphts)
        JPHCS_PXSH = 0
        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '技评会':
                JPHCS_PXSH = JPHCS_PXSH + 1
        final_dict["JPHCS"] = JPHCS
        final_dict["JPHCS_PXSH"] = JPHCS_PXSH
    elif field in ["KS", "KS_PXSH"]:
        ksts = get_base_data(crew_id, rq, '考试')
        KS = len(ksts)
        KS_PXSH = 0
        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '考试':
                KS_PXSH = KS_PXSH + 1
        final_dict["KS"] = KS
        final_dict["KS_PXSH"] = KS_PXSH
    elif field in ["QT", "QT_PXSH"]:
        qtts = get_base_data(crew_id, rq, '其他')
        if len(qtts) > 0:
            QT = len(qtts)
            QT_PXSH = 0
            qxh = clean_data(crew_id, rq)
            for x in qxh:
                if x[3] == '其他':
                    QT_PXSH = QT_PXSH + 1
            final_dict["QT"] = QT
            final_dict["QT_PXSH"] = QT_PXSH
    elif field in ["JHZBTS", "ZB_TOT_TJ", "ZB_FXB_TJ", "ZB_GS_TJ", "ZB_QTBM_TJ", "ZB_TOT", "ZB_FXB", "ZB_GS", "ZB_QTBM",
                   "ZB_PC"]:
        zbts = get_base_data(crew_id, rq, '值班')
        if len(zbts) > 0:
            JHZBTS = 0
            ZB_TOT_TJ = len(zbts)
            ZB_FXB_TJ = 0
            ZB_GS_TJ = 0
            ZB_QTBM_TJ = 0
            for y in zbts:
                if y[5] is None:
                    ZB_QTBM_TJ = ZB_QTBM_TJ + 1
                elif '飞行部' in y[5]:
                    ZB_FXB_TJ = ZB_FXB_TJ + 1
                elif '公司领导' in y[5]:
                    ZB_GS_TJ = ZB_GS_TJ + 1
                else:
                    ZB_QTBM_TJ = ZB_QTBM_TJ + 1
            ZB_TOT = 0
            ZB_FXB = 0
            ZB_GS = 0
            ZB_QTBM = 0
            qxh = clean_data(crew_id, rq)
            for x in qxh:
                if x[3] == '值班':
                    ZB_TOT = ZB_TOT + 1
                    if x[6] is None:
                        ZB_QTBM = ZB_QTBM + 1
                    elif '飞行部' in x[6]:
                        ZB_FXB = ZB_FXB + 1
                    elif '公司领导' in x[6]:
                        ZB_GS = ZB_GS + 1
                    else:
                        ZB_QTBM = ZB_QTBM + 1
            ZB_PC = ZB_TOT - ZB_TOT_TJ
            final_dict["JHZBTS"] = JHZBTS
            final_dict["ZB_TOT_TJ"] = ZB_TOT_TJ
            final_dict["ZB_FXB_TJ"] = ZB_FXB_TJ
            final_dict["ZB_GS_TJ"] = ZB_GS_TJ
            final_dict["ZB_QTBM_TJ"] = ZB_QTBM_TJ
            final_dict["ZB_TOT"] = ZB_TOT
            final_dict["ZB_FXB"] = ZB_FXB
            final_dict["ZB_GS"] = ZB_GS
            final_dict["ZB_QTBM"] = ZB_QTBM
            final_dict["ZB_PC"] = ZB_PC
    elif field in ["JHTFTS", "SJTFTS", "TF_TS", "TF_PC", "TF_STSH", "TF_AQSH", "TF_ZZSH", "TF_QTSH"]:
        tfts = get_base_data(crew_id, rq, '停飞')
        JHTFTS = 0
        SJTFTS = 0
        TF_TS = len(tfts)
        TF_STSH = 0
        TF_AQSH = 0
        TF_ZZSH = 0
        TF_QTSH = 0
        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '停飞':
                SJTFTS = SJTFTS + 1
                if ('身体原因停飞' in x[4]) or ('体检合格证到期' in x[4]):
                    TF_STSH = TF_STSH + 1
                elif '处罚' in x[4]:
                    TF_AQSH = TF_AQSH + 1
                elif x[4] == '停飞' or x[4] == '(模)停飞':
                    TF_QTSH = TF_QTSH + 1
                else:
                    TF_ZZSH = TF_ZZSH + 1
        final_dict["JHTFTS"] = JHTFTS
        final_dict["SJTFTS"] = SJTFTS
        final_dict["TF_TS"] = TF_TS
        final_dict["TF_PC"] = SJTFTS - TF_TS
        final_dict["TF_STSH"] = TF_STSH
        final_dict["TF_AQSH"] = TF_AQSH
        final_dict["TF_ZZSH"] = TF_ZZSH
        final_dict["TF_QTSH"] = TF_QTSH
    elif field in ["JHMNJDJJSC", "MNJSJDJJSC", "JHMNJDJJTS", "MNJSJDJJTS", "DRMNJDFXS", "MNJKHTS", "DRMNJDJSC",
                   "MNJZYTS", "JHMNJCXTS", "MNJCXTS", "MNJCX_PC", "MNJSHTS"]:
        qxh = clean_data(crew_id, rq)
        MNJSHTS = 0
        for x in qxh:
            if '模拟' in x[3]:
                MNJSHTS = MNJSHTS + 1
        JH = cal_MNJJJSC_plan(crew_id, rq)
        JT = cal_MNJJJSC(crew_id, rq)
        final_dict["JHMNJDJJSC"] = JH[0]
        final_dict["MNJSJDJJSC"] = JT[0]
        final_dict["JHMNJDJJTS"] = JH[1]
        final_dict["MNJSJDJJTS"] = JT[1]
        final_dict["DRMNJDFXS"] = 0 if JT[0] == 0 else round(JT[0] / JT[1], 2)
        # final_dict["MNJKHTS"] = MNJKHTS
        # final_dict["DRMNJDJSC"] = DRMNJDJSC
        final_dict["MNJZYTS"] = JT[2] + JT[1]
        final_dict["JHMNJCXTS"] = JH[2]
        final_dict["MNJCXTS"] = JT[2]
        final_dict["MNJCX_PC"] = JT[2] - JH[2]
        final_dict["MNJSHTS"] = MNJSHTS
    elif field in ["BE_QY_CUT", "BF_KH_CUT", "BF_PC"]:
        BE_QY_CUT = 0
        BF_KH_CUT = 0
        bf = get_base_data(crew_id, rq, '')
        end_list = []
        for y in bf:
            # print(y)
            if y[4] == '备份':
                for z in bf:
                    if y[0] == z[0] and z[4] == '执飞航班':
                        end_list.append(y[0])
                        # BE_QY_CUT = BE_QY_CUT +1

        qxh = clean_data(crew_id, rq)
        for x in qxh:
            if x[3] == '备份':
                BF_KH_CUT = BF_KH_CUT + 1

        final_dict["BE_QY_CUT"] = len(set(end_list))
        final_dict["BF_KH_CUT"] = BF_KH_CUT
        final_dict["BF_PC"] = BF_KH_CUT - len(set(end_list))
    elif field in ["ZWSH_TOT"]:
        qxh = clean_data(crew_id, rq)
        ZWSH_TOT = 0
        for x in qxh:
            if x[3] == '置位':
                ZWSH_TOT = ZWSH_TOT + 1
        final_dict["ZWSH_TOT"] = ZWSH_TOT

    return final_dict.get(field)


def get_crew_id(rq, field, desc):
    mxb = ExecuteHana().excute_sql(
        f"SELECT crew_id FROM BIDM.DM_FXSLTS_REPLAY WHERE yf = '{rq}' ORDER BY {field} {desc} limit 30")
    crew_list = []
    for x in mxb:
        crew_list.append(x[0])
    return crew_list


if __name__ == '__main__':
    yf_list = ['2022-01', '2022-02', '2022-03', '2022-04', '2022-05', '2022-06', '2022-07', '2022-08', '2022-09',
               '2022-10',
               '2022-11', '2022-12', '2023-01', '2023-02', '2023-03', '2023-04']
    # yf_list = ['2022-10']
    field = 'MNJSHTS'
    for yf in yf_list:
        crew_id_list = get_crew_id(yf, field, 'desc')
        for crew_id in crew_id_list:
            print(crew_id, yf, field)
            sy = sy_result(crew_id, yf, field)
            tx = tx_result(crew_id, yf, field)
            if sy != tx:
                print('================================tx：{}与sy：{}不一致'.format(tx, sy))
            else:
                print(tx, sy)

    # yz_crew_id = 604
    # yz_rq = '2022-01'
    # for x in clean_data(yz_crew_id, yz_rq):
    #     print(x)
    # print(tx_result(yz_crew_id, yz_rq, field))
    # print(sy_result(yz_crew_id, yz_rq, "BE_QY_CUT"))
    # print(sy_result(yz_crew_id, yz_rq, "BF_KH_CUT"))
    # print(sy_result(yz_crew_id, yz_rq, "BF_PC"))



    # print(cal_MNJKHTS(yz_crew_id, yz_rq))
