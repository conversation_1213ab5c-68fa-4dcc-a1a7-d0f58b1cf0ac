from ExecuteTest import ExecuteTest


def find_airline(start, end):
    # end_list = []
    airline = ExecuteTest().excute_sql(
        f'SELECT flight_no,act_no,dep_iata,arr_iata,air_line,std,sta FROM csds_flight WHERE flight_date = CURRENT_DATE and flight_status != "CNL" and deleted = 0 and dep_iata = "{start}"')
    # for x in airline:
    #     flight_no, act_no, dep_iata, arr_iata, air_line, std, sta = x
    #     if arr_iata == end:
    #         end_list.append(x)

    return airline


if __name__ == '__main__':
    a = find_airline("CKG", "ACX")
    print(a)
