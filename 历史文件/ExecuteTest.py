from pddb import db
import pandas as pd


class ExecuteTest:

    def __init__(self):
        self.db = db()

    def excute_sql(self, sql_str):
        return self.db.select(f"{sql_str}")


if __name__ == '__main__':
    a = ExecuteTest().excute_sql(
        'SELECT flight_no,act_no,dep_iata,arr_iata,air_line,std,sta FROM csds_flight WHERE flight_date = CURRENT_DATE and flight_status != "CNL" and deleted = 0')
    for x in range(len(a)):
        print(a[x])
