# coding=utf-8
from ExecuteHana import ExecuteHana
from datetime import datetime, timedelta
import math
import random


def time_diff(start_time, end_time):
    """
    计算两个时间之间的时间差
    :param start_time: 开始时间，格式为 '%Y-%m-%d %H:%M:%S'
    :param end_time: 结束时间，格式为 '%Y-%m-%d %H:%M:%S'
    :return: 时间差，单位为秒
    """
    # start = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
    # end = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
    diff = (end_time - start_time).total_seconds()
    return diff


def day_dif(date_str1, date_str2):
    # 将日期字符串转换为 datetime 对象
    date1 = datetime.strptime(date_str1, '%Y-%m-%d')
    date2 = datetime.strptime(date_str2, '%Y-%m-%d')
    # 计算天数差异
    diff = (date2 - date1).days
    return diff - 1


def all_crew_id():
    mxb = ExecuteHana().excute_sql(
        f"SELECT distinct crew_id FROM BIDM.DM_F_CREW_TRIP_DETAIL where rq >='2023-01-01'")
    end_list = []
    for x in mxb:
        end_list.append(x[0])
    return end_list


def get_base_data(crew_id, rq, type_class):
    sql = f"SELECT distinct RQ, CREW_ID, CREW_NAME, TYPE_CLASS_C, TYPE_CLASS,EXTEND_1, BEGIN_TIME, END_TIME FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = {crew_id} and rq like '%{rq}%' and TYPE_CLASS like '%{type_class}%'order by rq,BEGIN_TIME;"
    # print(sql)
    data_list = ExecuteHana().excute_sql(sql)

    return data_list


# 计算实际飞行小时数
def cal_SJFXXS(crew_id, rq):
    sql = f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and type_class = '执飞航班' order by a.rq,a.begin_time"
    # print(sql)
    mxb = ExecuteHana().excute_sql(sql)
    time_count = 0
    for x in mxb:
        mxb_rq = x[0]
        mxb_crew_id = x[1]
        mxb_crew_name = x[2]
        mxb_type_class = x[3]
        mxb_begin_time = x[4]
        mxb_end_time = x[5]
        tmp = time_diff(mxb_begin_time, mxb_end_time)
        time_count = time_count + tmp
    return round(time_count / 3600, 2)


# 模拟机实际带教检时间
def cal_MNJJJSC(crew_id, rq):
    mxb = ExecuteHana().excute_sql(
        f"SELECT a.rq,a.crew_id,a.crew_name,a.type_class,a.begin_time,a.end_time FROM BIDM.DM_F_CREW_TRIP_DETAIL a WHERE  a.crew_id = {crew_id}  and a.rq like '%{rq}%' and a.type_class like '%模拟%'  order by a.rq,a.begin_time")
    time_count = 0
    if len(mxb) > 0:
        for x in mxb:
            tc = x[3]
            rq = x[0]
            mxb_begin_time = x[4]
            mxb_end_time = x[5]
            tmp = time_diff(mxb_begin_time, mxb_end_time)
            if tmp < 3600 * 4:
                tmp1 = tmp
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    time_count = time_count + tmp1
            if 3600 * 4 <= tmp < 3600 * 6:
                tmp1 = tmp - 15 * 60
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    time_count = time_count + tmp1
            if 3600 * 6 <= tmp < 3600 * 8:
                tmp1 = tmp - 30 * 60
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    time_count = time_count + tmp1
            if tmp >= 3600 * 8:
                tmp1 = tmp - 45 * 60
                # print(rq, tmp1)
                if tc != '模拟机-参训':
                    time_count = time_count + tmp1

    return round(time_count / 3600, 2)


def cal_SJFXCQTS(crew_id, rq):
    return ExecuteHana().excute_sql(
        f"SELECT  count( distinct rq) FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = '{crew_id}' and rq like '%{rq}%' and TYPE_CLASS = '执飞航班'")[
        0][0]


def cal_LLPX(crew_id, rq):
    sql = f"SELECT  rq,crew_id,crew_name,CASE WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%生存%' THEN '应急生存' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%危险品%' THEN '危险品培训' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%安保%' THEN '安保' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%安全%' THEN '安全教育' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '%换季%' THEN '换季教育' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C LIKE '理论-年度复训理论%' THEN '模拟机复训理论' WHEN TYPE_CLASS = '理论培训' AND TYPE_CLASS_C NOT LIKE '%生存%' AND TYPE_CLASS_C NOT LIKE '%危险品%' AND TYPE_CLASS_C NOT LIKE '%安保%' AND TYPE_CLASS_C NOT LIKE '%安全%' AND TYPE_CLASS_C NOT LIKE '%换季%' AND TYPE_CLASS_C NOT LIKE '理论-年度复训理论%' THEN '理论培训其他' ELSE '' end as LLPX_TYPE FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = {crew_id} and rq like '%{rq}%' and TYPE_CLASS = '理论培训'"
    LLPX = ExecuteHana().excute_sql(sql)
    YJSC = 0
    WXPPX = 0
    AB = 0
    AQJY = 0
    HJJY = 0
    MNJFXLL = 0
    LLPXQT = 0

    for x in LLPX:
        if '应急生存' in x[3]:
            YJSC = YJSC + 1
        if '危险品培训' in x[3]:
            WXPPX = WXPPX + 1
        if '安保' in x[3]:
            AB = AB + 1
        if '安全教育' in x[3]:
            AQJY = AQJY + 1
        if '换季教育' in x[3]:
            HJJY = HJJY + 1
        if '模拟机复训理论' in x[3]:
            MNJFXLL = MNJFXLL + 1
        if '理论培训其他' in x[3]:
            LLPXQT = LLPXQT + 1
    LLPX_SH = cal_yxj(crew_id, rq)
    YJSC_PXSH = 0
    WXPPX_PXSH = 0
    AB_PXSH = 0
    AQJY_PXSH = 0
    HJJY_PXSH = 0
    MNJFXLL_PXSH = 0
    LLPXQT_PXSH = 0
    LLPX_SH_TOT = len(LLPX_SH)
    for sh in LLPX_SH:
        if '生存' in sh[4]:
            YJSC_PXSH = YJSC_PXSH + 1
        if '危险品' in sh[4]:
            WXPPX_PXSH = WXPPX_PXSH + 1
        if '安保' in sh[4]:
            AB_PXSH = AB_PXSH + 1
        if '安全' in sh[4]:
            AQJY_PXSH = AQJY_PXSH + 1
        if '换季' in sh[4]:
            HJJY_PXSH = HJJY_PXSH + 1
        if '年度复训理论' in sh[4]:
            MNJFXLL_PXSH = MNJFXLL_PXSH + 1
        else:
            LLPXQT_PXSH = LLPXQT_PXSH + 1
    LLPX_TOT = len(LLPX)
    # 实际理论培训损耗偏差 - -LLPXSH_PC
    LLPXSH_PC = LLPX_SH_TOT - LLPX_TOT
    LLPX_dict = {}
    LLPX_dict["LLPX_TOT"] = LLPX_TOT
    LLPX_dict["YJSC"] = YJSC
    LLPX_dict["WXPPX"] = WXPPX
    LLPX_dict["AB"] = AB
    LLPX_dict["AQJY"] = AQJY
    LLPX_dict["HJJY"] = HJJY
    LLPX_dict["MNJFXLL"] = MNJFXLL
    LLPX_dict["LLPXQT"] = LLPXQT
    LLPX_dict["JHLLPXSH"] = 2.1
    LLPX_dict["LLPX_SH_TOT"] = LLPX_SH_TOT
    LLPX_dict["YJSC_PXSH"] = YJSC_PXSH
    LLPX_dict["WXPPX_PXSH"] = WXPPX_PXSH
    LLPX_dict["AB_PXSH"] = AB_PXSH
    LLPX_dict["AQJY_PXSH"] = AQJY_PXSH
    LLPX_dict["HJJY_PXSH"] = HJJY_PXSH
    LLPX_dict["MNJFXLL_PXSH"] = MNJFXLL_PXSH
    LLPX_dict["LLPXQT_PXSH"] = LLPXQT_PXSH
    LLPX_dict["LLPXSH_PC"] = LLPXSH_PC

    return LLPX_dict


def cal_SJ48XSXXQTS(crew_id, rq):
    return ExecuteHana().excute_sql(
        f"SELECT  count( distinct rq) FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = '{crew_id}' and rq like '%{rq}%' and TYPE_CLASS = '休息期'")[
        0][0]


# 数据清洗
def cal_yxj(crew_id, rq):
    sql = f"SELECT DISTINCT rq, crew_id, crew_name, type_class,TYPE_CLASS_B,TYPE_CLASS_C,EXTEND_1 FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE rq like '%{rq}%'  AND crew_id = {crew_id} ORDER BY rq, CASE WHEN type_class = '执飞航班' THEN 1 WHEN type_class = '备份' THEN 2 WHEN type_class = '模拟机-检查员' THEN 3 WHEN type_class = '模拟机-教员' THEN 4 WHEN type_class = '模拟机-参训' THEN 5 WHEN type_class = '外围训练' THEN 6 WHEN type_class = '体检' THEN 7 WHEN type_class = '理论培训' THEN 8 WHEN type_class = '考试' THEN 9 WHEN type_class = '值班' THEN 10 WHEN type_class = '技评会' THEN 11 WHEN type_class = '置位' THEN 12 WHEN type_class = '出差' THEN 13 WHEN type_class = '其他' THEN 14 WHEN type_class = '休假' THEN 15 WHEN type_class = '停飞' THEN 16 WHEN type_class = '休息期' THEN 17 ELSE 18 END, CASE WHEN TYPE_CLASS_B LIKE '%身体原因停飞%' OR TYPE_CLASS_B LIKE '%体检合格证到期%' THEN 1 WHEN TYPE_CLASS_B LIKE '%处罚%' THEN 2 WHEN TYPE_CLASS_B NOT LIKE '%身体原因停飞%' AND TYPE_CLASS_B NOT LIKE '%体检合格证到期%' AND TYPE_CLASS_B NOT LIKE '%处罚%' AND TYPE_CLASS_B not in ('停飞', '(模)停飞') THEN 3 WHEN TYPE_CLASS_B in ('停飞', '(模)停飞') THEN 4 ELSE 5 END, CASE WHEN EXTEND_1 LIKE '%领导%' THEN 1 WHEN EXTEND_1 LIKE '%飞行部%' THEN 2 ELSE 3 END"
    # print(sql)
    mxb = ExecuteHana().excute_sql(sql)

    # 保留的日期和数据
    reserved_dates = set()
    reserved_data = []
    # 统计每个 type_class 类型的个数
    type_class_counts = {}
    # 处理查询结果
    for row in mxb:
        rq, crew_id, crew_name, type_class, type_class_b, type_class_c, EXTEND_1 = row

        # 检查日期是否已保留
        if rq in reserved_dates:
            continue

        # 添加当前日期的数据
        reserved_data.append(row)
        reserved_dates.add(rq)
        # 统计 type_class 类型的个数
        if type_class in type_class_counts:
            type_class_counts[type_class] += 1
        else:
            type_class_counts[type_class] = 1

    # 输出结果
    # for row in reserved_data:
    #     print(row)
    # 输出每个 type_class 类型的个数
    # for type_class, count in type_class_counts.items():
    #     print(f"{type_class}: {count}")
    # print(type_class_counts)
    return reserved_data


# 置位
def cal_ZW(crew_id, rq):
    # zwts = get_base_data(crew_id, rq, '')
    sql = f"SELECT distinct RQ, CREW_ID, CREW_NAME, TYPE_CLASS_C, TYPE_CLASS,EXTEND_1, BEGIN_TIME, END_TIME FROM BIDM.DM_F_CREW_TRIP_DETAIL WHERE crew_id = {crew_id} AND rq >= ADD_MONTHS('{rq}', -1) AND rq <= ADD_MONTHS('{rq}', 2) order by rq,BEGIN_TIME;"
    zwts = ExecuteHana().excute_sql(sql)
    zwts_dict = {}
    ZW_HBSC = 0
    ZW_XL = 0
    ZW_FHSD = 0
    ZW_TJ = 0
    ZW_MS = 0
    ZW_ZB = 0
    ZW_CC = 0
    ZW_QT = 0
    for x in range(len(zwts)):
        detail = zwts[x]
        if detail[4] == '置位' and rq in detail[0]:
            print(detail)
            if x + 1 < len(zwts):
                if zwts[x + 1][4] == '执飞航班':
                    ZW_HBSC = ZW_HBSC + 1
                elif zwts[x + 1][4] == '外围训练':
                    ZW_XL = ZW_XL + 1
                elif zwts[x + 1][4] == '返回属地':
                    ZW_FHSD = ZW_FHSD + 1
                elif zwts[x + 1][4] == '体检':
                    ZW_TJ = ZW_TJ + 1
                elif zwts[x + 1][4] == '面试':
                    ZW_MS = ZW_MS + 1
                elif zwts[x + 1][4] == '值班':
                    ZW_ZB = ZW_ZB + 1
                elif zwts[x + 1][4] == '出差':
                    ZW_CC = ZW_CC + 1
                else:
                    ZW_QT = ZW_QT + 1

    # qxh = cal_yxj(crew_id, rq)
    # for x in qxh:
    #     if x[3] == '置位':
    #         ZW_XL = ZW_XL + 1

    zwts_dict["ZW_HBSC"] = ZW_HBSC
    zwts_dict["ZW_XL"] = ZW_XL
    zwts_dict["ZW_FHSD"] = ZW_FHSD
    zwts_dict["ZW_TJ"] = ZW_TJ
    zwts_dict["ZW_MS"] = ZW_MS
    zwts_dict["ZW_ZB"] = ZW_ZB
    zwts_dict["ZW_CC"] = ZW_CC
    zwts_dict["ZW_QT"] = ZW_QT

    return zwts_dict


# 休假
def cal_XJTS(crew_id, rq):
    xjts = get_base_data(crew_id, rq, '休假')
    xjts_dict = {}
    TYXJTS = len(xjts)
    SJXJTS = 0
    qxh = cal_yxj(crew_id, rq)
    for x in qxh:
        if x[3] == '休假':
            SJXJTS = SJXJTS + 1

    xjts_dict["JHXJTS"] = 2
    xjts_dict["JHXJTS_BWG"] = 2
    xjts_dict["TYXJTS"] = TYXJTS
    xjts_dict["SJXJTS"] = SJXJTS
    xjts_dict["XJ_PC"] = SJXJTS - TYXJTS

    return xjts_dict


# 出差
def cal_CC(crew_id, rq):
    ccts = get_base_data(crew_id, rq, '出差')
    ccts_dict = {}
    CC_TS = len(ccts)
    CC_PXSH = 0
    qxh = cal_yxj(crew_id, rq)
    for x in qxh:
        if x[3] == '出差':
            CC_PXSH = CC_PXSH + 1

    ccts_dict["CC_TS"] = CC_TS
    ccts_dict["CC_PXSH"] = CC_PXSH

    return ccts_dict


# 体检
def cal_TJ(crew_id, rq):
    tjts = get_base_data(crew_id, rq, '体检')
    tjts_dict = {}
    TJ_TS = len(tjts)
    TJ_PXSH = 0
    qxh = cal_yxj(crew_id, rq)
    for x in qxh:
        if x[3] == '体检':
            TJ_PXSH = TJ_PXSH + 1

    tjts_dict["TJ_TS"] = TJ_TS
    tjts_dict["TJ_PXSH"] = TJ_PXSH
    tjts_dict["TJ_PC"] = TJ_PXSH - TJ_TS

    return tjts_dict


# 技评会
def cal_JPH(crew_id, rq):
    jphts = get_base_data(crew_id, rq, '技评会')
    jphts_dict = {}
    JPHCS = len(jphts)
    JPHCS_PXSH = 0
    qxh = cal_yxj(crew_id, rq)
    for x in qxh:
        if x[3] == '技评会':
            JPHCS_PXSH = JPHCS_PXSH + 1

    jphts_dict["JPHCS"] = JPHCS
    jphts_dict["JPHCS_PXSH"] = JPHCS_PXSH

    return jphts_dict


# 考试
def cal_KS(crew_id, rq):
    ksts = get_base_data(crew_id, rq, '考试')
    ksts_dict = {}
    KS = len(ksts)
    KS_PXSH = 0
    qxh = cal_yxj(crew_id, rq)
    for x in qxh:
        if x[3] == '考试':
            KS_PXSH = KS_PXSH + 1

    ksts_dict["KS"] = KS
    ksts_dict["KS_PXSH"] = KS_PXSH

    return ksts_dict


# 其他
def cal_QT(crew_id, rq):
    qtts = get_base_data(crew_id, rq, '其他')
    qtts_dict = {}
    if len(qtts) > 0:
        QT = len(qtts)
        QT_PXSH = 0
        qxh = cal_yxj(crew_id, rq)
        for x in qxh:
            if x[3] == '其他':
                QT_PXSH = QT_PXSH + 1

        qtts_dict["QT"] = QT
        qtts_dict["QT_PXSH"] = QT_PXSH

        return qtts_dict


# 值班
def cal_ZB(crew_id, rq):
    zbts = get_base_data(crew_id, rq, '值班')
    # print(zbts)
    if len(zbts) > 0:
        zbts_dict = {}
        JHZBTS = 0
        ZB_TOT_TJ = len(zbts)
        ZB_FXB_TJ = 0
        ZB_GS_TJ = 0
        ZB_QTBM_TJ = 0
        for y in zbts:
            if y[5] is None:
                ZB_QTBM_TJ = ZB_QTBM_TJ + 1
            elif '飞行部' in y[5]:
                ZB_FXB_TJ = ZB_FXB_TJ + 1
            elif '公司领导' in y[5]:
                ZB_GS_TJ = ZB_GS_TJ + 1
            else:
                ZB_QTBM_TJ = ZB_QTBM_TJ + 1

        ZB_TOT = 0
        ZB_FXB = 0
        ZB_GS = 0
        ZB_QTBM = 0

        qxh = cal_yxj(crew_id, rq)
        for x in qxh:
            if x[3] == '值班':
                ZB_TOT = ZB_TOT + 1
                if x[6] is None:
                    ZB_QTBM = ZB_QTBM + 1
                elif '飞行部' in x[6]:
                    ZB_FXB = ZB_FXB + 1
                elif '公司领导' in x[6]:
                    ZB_GS = ZB_GS + 1
                else:
                    ZB_QTBM = ZB_QTBM + 1
        ZB_PC = ZB_TOT - ZB_TOT_TJ
        zbts_dict["JHZBTS"] = JHZBTS
        zbts_dict["ZB_TOT_TJ"] = ZB_TOT_TJ
        zbts_dict["ZB_FXB_TJ"] = ZB_FXB_TJ
        zbts_dict["ZB_GS_TJ"] = ZB_GS_TJ
        zbts_dict["ZB_QTBM_TJ"] = ZB_QTBM_TJ
        zbts_dict["ZB_TOT"] = ZB_TOT
        zbts_dict["ZB_FXB"] = ZB_FXB
        zbts_dict["ZB_GS"] = ZB_GS
        zbts_dict["ZB_QTBM"] = ZB_QTBM
        zbts_dict["ZB_PC"] = ZB_PC

        return zbts_dict


# 停飞
def cal_TFTS(crew_id, rq):
    tfts = get_base_data(crew_id, rq, '停飞')
    tfts_dict = {}
    JHTFTS = 0
    SJTFTS = 0
    TF_TS = len(tfts)
    TF_STSH = 0
    TF_AQSH = 0
    TF_ZZSH = 0
    TF_QTSH = 0

    qxh = cal_yxj(crew_id, rq)
    for x in qxh:
        if x[3] == '停飞':
            SJTFTS = SJTFTS + 1
            if ('身体原因停飞' in x[4]) or ('体检合格证到期' in x[4]):
                TF_STSH = TF_STSH + 1
            elif '处罚' in x[4]:
                TF_AQSH = TF_AQSH + 1
            elif x[4] == '停飞' or x[4] == '(模)停飞':
                TF_QTSH = TF_QTSH + 1
            else:
                TF_ZZSH = TF_ZZSH + 1

    tfts_dict["JHTFTS"] = JHTFTS
    tfts_dict["SJTFTS"] = SJTFTS
    tfts_dict["TF_TS"] = TF_TS
    tfts_dict["TF_PC"] = SJTFTS - TF_TS
    tfts_dict["TF_STSH"] = TF_STSH
    tfts_dict["TF_AQSH"] = TF_AQSH
    tfts_dict["TF_ZZSH"] = TF_ZZSH
    tfts_dict["TF_QTSH"] = TF_QTSH

    return tfts_dict


def cal_CQ(crew_id, rq):
    final_dict = {}
    MNJJJSC = cal_MNJJJSC(crew_id, rq)
    SJFXCQTS = cal_SJFXCQTS(crew_id, rq)
    SJFXXS = cal_SJFXXS(crew_id, rq)
    SJ48XSXXQTS = cal_SJ48XSXXQTS(crew_id, rq)
    # 模拟机实际带教检时间 MNJJJSC
    final_dict["MNJJJSC"] = MNJJJSC
    # 实际飞行出勤天数 SJFXCQTS
    final_dict["SJFXCQTS"] = SJFXCQTS
    # 干部工作日飞行出勤天数--GBGZRFXCQTS  未完成
    final_dict["GBGZRFXCQTS"] = 0
    # 实际出勤日的日均飞行小时 - -SJCQRRJFXXS
    final_dict["SJCQRRJFXXS"] = 0 if SJFXXS == 0 else round(SJFXXS / SJFXCQTS, 2)
    # 理论48小时休息期天数 - -LL48XSXXQTS
    final_dict["LL48XSXXQTS"] = math.ceil(SJFXCQTS * 0.4)
    # 实际48小时休息期天数 - -SJ48XSXXQTS
    final_dict["SJ48XSXXQTS"] = SJ48XSXXQTS
    # 休息期偏差 - -XXQPC
    final_dict["XXQPC"] = math.ceil(SJFXCQTS * 0.4) - SJ48XSXXQTS
    # 实际飞行日的日均飞行小时 - -SJFXRRJFXXS
    final_dict["SJFXRRJFXXS"] = 0 if SJFXXS == 0 else round(SJFXXS / SJFXCQTS, 2)

    return final_dict


def final_reslult(crew_id, rq):
    final_dict = {}
    CQ = cal_CQ(crew_id, rq)
    final_dict.update(CQ)
    # 实际理论培训小计（人 / 天）--LLPX
    LLPX_dict = cal_LLPX(crew_id, rq)
    final_dict.update(LLPX_dict)
    # 休假天数
    XJTS = cal_XJTS(crew_id, rq)
    final_dict.update(XJTS)
    # 停飞
    TFTS = cal_TFTS(crew_id, rq)
    final_dict.update(TFTS)
    # 出差
    CC = cal_CC(crew_id, rq)
    final_dict.update(CC)
    # 值班
    ZB = cal_ZB(crew_id, rq)
    final_dict.update(ZB)
    # 体检
    TJ = cal_TJ(crew_id, rq)
    final_dict.update(TJ)
    # 技评会
    JPH = cal_JPH(crew_id, rq)
    final_dict.update(JPH)
    # 考试
    KS = cal_KS(crew_id, rq)
    final_dict.update(KS)
    # 其他
    QT = cal_QT(crew_id, rq)
    final_dict.update(QT)

    return final_dict


def yanzheg(crew_id, rq):
    sql = f"select yf,crew_id,crew_name,MNJJJSC,SJFXCQTS,GBGZRFXCQTS,SJCQRRJFXXS,LL48XSXXQTS,SJ48XSXXQTS,XXQPC,SJFXRRJFXXS,ZW_HBSC,ZW_XL,ZW_FHSD,ZW_TJ,ZW_MS,ZW_ZB,ZW_CC,ZW_QT,ZWSH_HBSC,ZWSH_XL,ZWSH_FHSD,ZWSH_TJ,ZWSH_MS,ZWSH_ZB,ZWSH_CC,ZWSH_QT,JHZWTS,JHZWTS_TBWG,ZWSH_TOT,JHBFTS_YF,JHBFTS,BF_CUT,BE_QY_CUT,BF_KH_CUT,BF_PC,MNJSJDJJSC,MNJSJDJJTS,MNJSJDJJTS_PXSH,DRMNJCXXS,MNJKHTS,DRMNJDJSC,MNJZYTS,MNJCXTS,MNJCXTS,MNJCX_PC,LLPX_TOT,YJSC,WXPPX,AB,AQJY,HJJY,MNJFXLL,LLPXQT,JHLLPXSH,LLPX_SH_TOT,YJSC_PXSH,WXPPX_PXSH,AB_PXSH,AQJY_PXSH,HJJY_PXSH,MNJFXLL_PXSH,LLPXQT_PXSH,LLPXSH_PC,JHXJTS,JHXJTS_BWG,TYXJTS,SJXJTS,XJ_PC,SJQJ_WTBPM,JHTFTS,SJTFTS,TF_TS,TF_PC,TF_STSH,TF_AQSH,TF_ZZSH,TF_QTSH,CC_TS,CC_PXSH,JHZBTS,ZB_TOT_TJ,ZB_FXB_TJ,ZB_GS_TJ,ZB_QTBM_TJ,ZB_TOT,ZB_FXB,ZB_GS,ZB_QTBM,ZB_PC,TJ_TS,TJ_PXSH,TJ_PC,JPHCS,JPH_PXSH,KS,KS_PXSH,QT,QT_PXSH from BIDM.DM_FXSLTS_REPLAY where yf = '{rq}'and crew_id = {crew_id}"
    pre_res = ExecuteHana().excute_sql(sql)
    if len(pre_res) > 0:
        res = pre_res[0]
        end_dict = {}
        end_dict['yf'] = res[0]
        end_dict['crew_id'] = res[1]
        end_dict['crew_name'] = res[2]
        end_dict['MNJJJSC'] = res[3]
        end_dict['SJFXCQTS'] = res[4]
        end_dict['GBGZRFXCQTS'] = res[5]
        end_dict['SJCQRRJFXXS'] = res[6]
        end_dict['LL48XSXXQTS'] = res[7]
        end_dict['SJ48XSXXQTS'] = res[8]
        end_dict['XXQPC'] = res[9]
        end_dict['SJFXRRJFXXS'] = res[10]
        end_dict['ZW_HBSC'] = res[11]
        end_dict['ZW_XL'] = res[12]
        end_dict['ZW_FHSD'] = res[13]
        end_dict['ZW_TJ'] = res[14]
        end_dict['ZW_MS'] = res[15]
        end_dict['ZW_ZB'] = res[16]
        end_dict['ZW_CC'] = res[17]
        end_dict['ZW_QT'] = res[18]
        end_dict['ZWSH_HBSC'] = res[19]
        end_dict['ZWSH_XL'] = res[20]
        end_dict['ZWSH_FHSD'] = res[21]
        end_dict['ZWSH_TJ'] = res[22]
        end_dict['ZWSH_MS'] = res[23]
        end_dict['ZWSH_ZB'] = res[24]
        end_dict['ZWSH_CC'] = res[25]
        end_dict['ZWSH_QT'] = res[26]
        end_dict['JHZWTS'] = res[27]
        end_dict['JHZWTS_TBWG'] = res[28]
        end_dict['ZWSH_TOT'] = res[29]
        end_dict['JHBFTS_YF'] = res[30]
        end_dict['JHBFTS'] = res[31]
        end_dict['BF_CUT'] = res[32]
        end_dict['BE_QY_CUT'] = res[33]
        end_dict['BF_KH_CUT'] = res[34]
        end_dict['BF_PC'] = res[35]
        end_dict['MNJSJDJJSC'] = res[36]
        end_dict['MNJSJDJJTS'] = res[37]
        end_dict['MNJSJDJJTS_PXSH'] = res[38]
        end_dict['DRMNJCXXS'] = res[39]
        end_dict['MNJKHTS'] = res[40]
        end_dict['DRMNJDJSC'] = res[41]
        end_dict['MNJZYTS'] = res[42]
        end_dict['MNJCXTS'] = res[43]
        end_dict['MNJCXTS'] = res[44]
        end_dict['MNJCX_PC'] = res[45]
        end_dict['LLPX_TOT'] = res[46]
        end_dict['YJSC'] = res[47]
        end_dict['WXPPX'] = res[48]
        end_dict['AB'] = res[49]
        end_dict['AQJY'] = res[50]
        end_dict['HJJY'] = res[51]
        end_dict['MNJFXLL'] = res[52]
        end_dict['LLPXQT'] = res[53]
        end_dict['JHLLPXSH'] = res[54]
        end_dict['LLPX_SH_TOT'] = res[55]
        end_dict['YJSC_PXSH'] = res[56]
        end_dict['WXPPX_PXSH'] = res[57]
        end_dict['AB_PXSH'] = res[58]
        end_dict['AQJY_PXSH'] = res[59]
        end_dict['HJJY_PXSH'] = res[60]
        end_dict['MNJFXLL_PXSH'] = res[61]
        end_dict['LLPXQT_PXSH'] = res[62]
        end_dict['LLPXSH_PC'] = res[63]
        end_dict['JHXJTS'] = res[64]
        end_dict['JHXJTS_BWG'] = res[65]
        end_dict['TYXJTS'] = res[66]
        end_dict['SJXJTS'] = res[67]
        end_dict['XJ_PC'] = res[68]
        end_dict['SJQJ_WTBPM'] = res[69]
        end_dict['JHTFTS'] = res[70]
        end_dict['SJTFTS'] = res[71]
        end_dict['TF_TS'] = res[72]
        end_dict['TF_PC'] = res[73]
        end_dict['TF_STSH'] = res[74]
        end_dict['TF_AQSH'] = res[75]
        end_dict['TF_ZZSH'] = res[76]
        end_dict['TF_QTSH'] = res[77]
        end_dict['CC_TS'] = res[78]
        end_dict['CC_PXSH'] = res[79]
        end_dict['JHZBTS'] = res[80]
        end_dict['ZB_TOT_TJ'] = res[81]
        end_dict['ZB_FXB_TJ'] = res[82]
        end_dict['ZB_GS_TJ'] = res[83]
        end_dict['ZB_QTBM_TJ'] = res[84]
        end_dict['ZB_TOT'] = res[85]
        end_dict['ZB_FXB'] = res[86]
        end_dict['ZB_GS'] = res[87]
        end_dict['ZB_QTBM'] = res[88]
        end_dict['ZB_PC'] = res[89]
        end_dict['TJ_TS'] = res[90]
        end_dict['TJ_PXSH'] = res[91]
        end_dict['TJ_PC'] = res[92]
        end_dict['JPHCS'] = res[93]
        end_dict['JPH_PXSH'] = res[94]
        end_dict['KS'] = res[95]
        end_dict['KS_PXSH'] = res[96]
        end_dict['QT'] = res[97]
        end_dict['QT_PXSH'] = res[98]
        return end_dict


if __name__ == '__main__':
    # 计算实际飞行小时数
    # print(cal_SJFXXS(220, "2020-08"))
    # print(get_base_data(220, '2020-08', '休假'))
    # 模拟机实际带教检时间
    # print(cal_MNJJJSC(708, "2022-05"))
    # 数据清洗
    # print(cal_yxj(220, '2020-08'))
    # 理论培训
    # print(cal_LLPX(220, '2020-08'))
    # 休假天数
    # print(cal_XJTS(452, '2020-08'))
    # 停飞天数
    # print(cal_TFTS(527, '2019-08'))
    # 出差天数
    # print(cal_CC(595, "2022-06"))
    # 值班天数
    # print(cal_ZB(723, "2019-10"))
    # 体检
    # print(cal_KS(931, "2021-09"))
    # 结果表
    # print(final_reslult(452, "2020-08"))
    # print(cal_ZW(509, "2018-08"))
    crew_id_list = all_crew_id()
    random_numbers = random.sample(crew_id_list, 300)
    # crew_id_list = ['745']
    rq_list = ["2023-01", "2023-02", "2023-03", "2023-04"]
    ziduan = "YJSC"
    # for crew_id in random_numbers:
    #     print(crew_id)
    #     for rq in rq_list:
    #         sytj = yanzheg(crew_id, rq)
    #         wdtj = cal_LLPX(crew_id, rq)
    #         # print(sytj)
    #         if sytj is not None:
    #             if wdtj is not None:
    #                 sy = 0 if sytj.get(ziduan) is None else sytj.get(ziduan)
    #                 wd = 0 if wdtj.get(ziduan) is None else wdtj.get(ziduan)
    #                 if float(sy) != float(wd):
    #                     print('{} {}  {}数据不一致施宇：{}  我的：{}'.format(crew_id, rq, ziduan, sytj.get(ziduan),
    #                                                               wdtj.get(ziduan)))
    #                 else:
    #                     print('{} {}  {} = {}'.format(crew_id, rq, sytj.get(ziduan), wdtj.get(ziduan)))

    print(cal_yxj(63, '2022-05'))
















