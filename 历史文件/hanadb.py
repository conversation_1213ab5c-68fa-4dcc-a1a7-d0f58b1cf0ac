# coding=utf-8
import pymysql
from hdbcli import dbapi


class db:
    def __init__(self, link: str = "172.31.86.232:30515 hx_bidata C7zBn$w@@5 BIDW"):
        """数据库处理类
        :param link:  格式见默认 地址:端口号 用户名 密码 数据库名
        """
        link_li = link.split(" ")
        self.link = link_li[0].split(":") + link_li[1:]

    def get_db(self):
        """【非公开方法==>外部不要调用】 获取数据库的方法
        :return: 返回数据库对象，注意返回的对象用完之后要及时的close一下
        """
        return dbapi.connect(
            address=self.link[0],
            port=int(self.link[1]),
            user=self.link[2],
            password=self.link[3],
            database=self.link[4],
            charset="utf8mb4",
        )

    def do(self, sql: str, args=()) -> bool:
        """对数据库进行插入，删除，更新之类的操作
        :param args:  %s 表示的参数，用于图片的上传
        :param sql:  sql语句
        :return: 返回值为操作是否成功
        """
        ret = True
        db = self.get_db()
        cursor = db.cursor()
        try:
            cursor.execute(sql, args=args)
            db.commit()
        except Exception as e:
            db.rollback()
            ret = False
            print("数据库异常", e, sql)
        cursor.close()
        db.close()
        return ret

    def select(self, sql: str) -> list:
        """对数据库查询的方法
        :param sql: sql查询语句
        :return: 返回为二位列表，若为空，则返回为空列表
        """
        ret = []
        db = self.get_db()
        cursor = db.cursor()
        try:
            cursor.execute(sql)
            ret = [list(row) for row in cursor.fetchall()]
        except Exception as e:
            print("数据库报错", e, sql)
        cursor.close()
        db.close()
        return ret

    def title_select(self, title: str, sql: str) -> list:
        """ 查询后，将表格按照title进行转化
        :param title: 标题[字符串类型]通过逗号分割
        :param sql: 查询的sql语句
        :return: 返回值为一个列表，列表中是字典元素 类似于这样 [{"id": 1}, {"id", 2}]
        """
        ret = []
        db = self.get_db()
        cursor = db.cursor()
        try:
            cursor.execute(sql)
            tab = [list(row) for row in cursor.fetchall()]
            title_list = title.split(",")
            for row_li in tab:
                i = min(len(row_li), len(title_list))
                if i:
                    ret.append(
                        {key: val for key, val in zip(title_list[:i], row_li[:i])}
                    )
        except Exception as e:
            print("数据库报错", e, sql)
        cursor.close()
        db.close()
        return ret

    def insert(self, sql: str, args=()):
        """数据库进行插入
        :param args: %s代表，用于存储文件类型的数据
        :param sql:  插入的语句
        :return:  返回值为主键！
        """
        ret = -1
        db = self.get_db()
        cursor = db.cursor()
        try:
            if args:
                cursor.execute(sql, args=args)
            else:
                cursor.execute(sql)
            ret = db.insert_id()
            db.commit()
        except Exception as e:
            db.rollback()
            print("数据库异常", e, sql)
        cursor.close()
        db.close()
        return ret


if __name__ == "__main__":
    a = db()
    b = a.select("select * from BIDM.DM_FXSLTS_REPLAY where crew_id = 6")
    print(b)
    # insert = a.insert(
    #     f"INSERT INTO data_base_day(`stock_date`, `code`, `open`, `high`, `low`, `close`, `volume`) VALUES ('{mytime}', 'sh000001', '3216', '3300','11','22','33')"
    # )
    # for x in range(len(b)):
    #     # print(b[x][0])
    #     if str(b[x][0]) == "2023-04-13":
    #         print(b[x][1])

    # print(d[9].strftime('%Y-%m-%d %H:%M'))
