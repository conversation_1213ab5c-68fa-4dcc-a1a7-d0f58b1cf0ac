from datetime import datetime


def get_value(in_num, n):
    # 计算除以间隔数字n后的整数部分和余数
    quotient = int(in_num / n)
    remainder = in_num % n

    # 如果余数大于或等于间隔数字n的一半，则向上取整
    if remainder >= n / 2:
        return (quotient + 1) * n
    else:
        # 否则向下取整
        return quotient * n


if __name__ == '__main__':
    print(get_value(73.22, 0.25))
    timestamp = 1713744000000

    # 将时间戳转换为datetime对象
    # 注意：时间戳是以微秒为单位的，所以需要除以1000000来转换为秒
    dt_object = datetime.fromtimestamp(timestamp / 1000000)

    # 格式化日期时间对象为字符串
    formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
    formatted_time_ms = dt_object.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
    print(formatted_time)
    print(formatted_time_ms)
