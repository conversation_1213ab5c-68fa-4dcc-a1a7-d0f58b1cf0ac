import requests
import uuid
import time
import logging
import random
import datetime
from typing import List, Dict

# 全局计数器，用于生成顺序编号
telegram_counter = 1

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('telegram_send.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 配置错误日志专用处理器
error_logger = logging.getLogger('error_logger')
error_logger.setLevel(logging.ERROR)
error_handler = logging.FileHandler('telegram_error.log')
error_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
error_handler.setFormatter(error_formatter)
error_logger.addHandler(error_handler)

# 接口配置
API_URL = 'http://192.168.1.14:10106/nfoc/tgsp/telegram/send'
# 随机选择系统代码 'FOC' 或 'NFOC'
# DEFAULT_SYSTEM_CODE = random.choice(['FOC', 'NFOC'])
DEFAULT_SYSTEM_CODE = 'FOC'

print(DEFAULT_SYSTEM_CODE)
# 生成有序UUID (基于时间戳+随机数)
def generate_ordered_uuid() -> str:
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')[:-4]  # 截取16位时间戳
    random_part = ''.join(random.choices('0123456789abcdef', k=16))
    return f'{timestamp}{random_part}'

# 生成随机电报内容
def generate_telegram_content() -> str:
    global telegram_counter
    # 随机生成一些航班信息
    flights = []
    aircraft_types = ['CR9', 'B737', 'A320', 'E190']
    routes = [
        ('HET', 'ERL'), ('ERL', 'HET'), ('ERL', 'XIL'),
        ('XIL', 'ERL'), ('PEK', 'SHA'), ('SHA', 'PEK')
    ]
    dates = ['20JUN', '21JUN', '22JUN']
    
    # 生成3-6个航班
    num_flights = random.randint(3, 6)
    for i in range(1, num_flights + 1):
        flight_num = f'G5{random.randint(1000, 9999)}'
        ac_type = random.choice(aircraft_types)
        ac_reg = f'{ac_type}BB{random.randint(1000, 9999)}'
        dep, arr = random.choice(routes)
        date = random.choice(dates)
        time_str = f'{random.randint(0, 23):02d}{random.randint(0, 59):02d}'
        
        flight_line = f'{i:02d} {flight_num} {ac_reg} {dep}{time_str}({date}) {arr}'
        flights.append(flight_line)
    
    # 构建电报内容
    # 使用全局计数器生成顺序编号，从HXC0001开始
    zczc_line = f'ZCZC HXC{telegram_counter:04d} {datetime.datetime.now().strftime("%H%M%S")}'
    qu_line = 'QU ' + ' '.join(random.choices(['ERLZP8X', 'ERLTZ8X', 'PEKFP8X', 'SHAFP8X', 'SHAZPCA', 'PEKZBCA'], k=4))
    ckg_line = f'CKGUOG5 {datetime.datetime.now().strftime("%H%M%S")}'
    pln_line = f'PLN {random.choice(dates)}'
    
    telegram_lines = [
        zczc_line,
        qu_line,
        ckg_line,
        pln_line,
        *flights,
        'NNNN'
    ]
    
    # 递增计数器
    telegram_counter += 1
    
    return '\n'.join(telegram_lines)

# 发送请求
def send_telegram(error_type: str = None) -> Dict:
    # 生成正常的参数
    biz_no = generate_ordered_uuid()
    content = generate_telegram_content()
    system_code = DEFAULT_SYSTEM_CODE
    api_url = API_URL
    timeout = 10
    
    # 根据错误类型修改参数
    if error_type == 'long_bizNo':
        # 1. bizNo过长 (正常是32位，这里生成64位)
        biz_no = generate_ordered_uuid() * 2
    elif error_type == 'empty_bizNo':
        # 2. bizNo为空
        biz_no = ''
    elif error_type == 'empty_systemCode':
        # 3. systemCode为空
        system_code = ''
    elif error_type == 'long_systemCode':
        # 4. systemCode过长 (生成50个字符)
        system_code = 'A' * 5000
    elif error_type == 'empty_fullContent':
        # 5. fullContent为空
        content = ''
    elif error_type == 'long_fullContent':
        # 6. fullContent超长 (生成10000个字符)
        content = 'A' * 10000
    elif error_type == 'timeout':
        # 7. 模拟超时 (使用一个不存在的URL和短超时)
        api_url = 'http://192.168.1.14:9999/nonexistent/api'
        timeout = 0.001
    elif error_type == 'duplicate_bizNo':
        # 8. bizNo重复 (使用一个固定的bizNo)
        biz_no = 'fixedbizno1234567890abcdef'
    elif error_type == 'long_qu_line':
        # 9. qu_line超过30个元素
        # 生成一个包含31个元素的列表
        qu_codes = [f'CODE{i:03d}' for i in range(1, 32)]
        # 先生成正常的电报内容
        temp_content = generate_telegram_content()
        # 拆分内容行
        lines = temp_content.split('\n')
        # 生成包含31个元素的qu_line
        qu_line = 'QU ' + ' '.join(random.choices(qu_codes, k=31))
        # 替换第二行（即qu_line）
        lines[1] = qu_line
        # 重新组合内容
        content = '\n'.join(lines)
    elif error_type == 'many_flights':
        # 10. flight_line超过30行
        # 重新生成content，包含31个航班
        flights = []
        aircraft_types = ['CR9', 'B737', 'A320', 'E190']
        routes = [
            ('HET', 'ERL'), ('ERL', 'HET'), ('ERL', 'XIL'),
            ('XIL', 'ERL'), ('PEK', 'SHA'), ('SHA', 'PEK')
        ]
        dates = ['20JUN', '21JUN', '22JUN']
        
        # 生成31个航班
        for i in range(1, 32):
            flight_num = f'G5{random.randint(1000, 9999)}'
            ac_type = random.choice(aircraft_types)
            ac_reg = f'{ac_type}BB{random.randint(1000, 9999)}'
            dep, arr = random.choice(routes)
            date = random.choice(dates)
            time_str = f'{random.randint(0, 23):02d}{random.randint(0, 59):02d}'
            
            flight_line = f'{i:02d} {flight_num} {ac_reg} {dep}{time_str}({date}) {arr}'
            flights.append(flight_line)
        
            # 重新构建电报内容
            global telegram_counter
            zczc_line = f'ZCZC HXC{telegram_counter:04d} {datetime.datetime.now().strftime("%H%M%S")}'
            qu_line = 'QU ' + ' '.join(random.choices(['ERLZP8X', 'ERLTZ8X', 'PEKFP8X', 'SHAFP8X', 'SHAZPCA', 'PEKZBCA'], k=4))
            ckg_line = f'CKGUOG5 {datetime.datetime.now().strftime("%H%M%S")}'
            pln_line = f'PLN {random.choice(dates)}'
            
            telegram_lines = [
                zczc_line,
                qu_line,
                ckg_line,
                pln_line,
                *flights,
                'NNNN'
            ]
            
            content = '\n'.join(telegram_lines)
    
    payload = {
        'bizNo': biz_no,
        'systemCode': system_code,
        'fullContent': content
    }
    
    try:
        response = requests.post(api_url, json=payload, timeout=timeout)
        response.raise_for_status()
        
        # 记录发送内容
        logger.info(f'Sent telegram with bizNo: {biz_no} (error_type: {error_type})')
        logger.debug(f'Telegram content: {content}')

        # 格式化请求参数
        formatted_payload = str(payload).replace(',', ',\n')
        
        try:
            response_data = response.json()
            # 格式化响应数据以便更好地显示
            formatted_response = str(response_data).replace(',', ',\n')
            
            # 无论是否为错误类型，都记录参数和结果
            if error_type is not None:
                error_logger.error(f'Sent telegram with error type: {error_type}, bizNo: {biz_no}, status: success, response: {formatted_response}, payload: {formatted_payload}')
            else:
                logger.info(f'Sent normal telegram, bizNo: {biz_no}, response: {formatted_response}, payload: {formatted_payload}')
        except Exception as json_e:
            if error_type is not None:
                error_logger.error(f'Sent telegram with error type: {error_type}, bizNo: {biz_no}, status: success, but failed to parse response: {str(json_e)}, raw response: {response.text}, payload: {formatted_payload}')
            else:
                logger.error(f'Sent normal telegram, bizNo: {biz_no}, but failed to parse response: {str(json_e)}, raw response: {response.text}, payload: {formatted_payload}')
        return {
            'success': True,
            'bizNo': biz_no,
            'error_type': error_type,
            'response': response.json()
        }
    except Exception as e:
        error_message = f'''Failed to send telegram (error_type: {error_type}): {str(e)}
Payload: {payload}'''
        logger.error(error_message)
        error_logger.error(error_message)
        return {
            'success': False,
            'bizNo': biz_no,
            'error_type': error_type,
            'error': str(e),
            'payload': payload
        }

# 主函数
def main():
    try:
        # 获取用户选择
        print("请选择发送模式：")
        print("1. 全部发送正常的电报")
        print("2. 发送包含错误情况的电报")
        choice = input("请输入选择 (1/2): ")
        
        # 根据选择设置错误率
        if choice == '1':
            error_rate = 0  # 不发送错误电报
            logger.info("已选择全部发送正常的电报")
        elif choice == '2':
            error_rate = 0.2  # 20%的概率发送错误请求
            logger.info("已选择发送包含错误情况的电报")
        else:
            print("无效的选择，默认发送正常电报")
            error_rate = 0
            logger.warning("无效的选择，默认发送正常电报")
        
        # 获取其他参数
        interval = 0.01  # 发送间隔(秒)
        count = 1000  # 发送数量
        run_time = 0  # 运行时间(秒)，0表示不限制

        logger.info(f'Starting to send telegrams with interval {interval} seconds')
        
        # 错误类型列表
        error_types = [
            'long_bizNo', 'empty_bizNo', 'empty_systemCode', 
            'long_systemCode', 'empty_fullContent', 'long_fullContent', 
            'timeout', 'duplicate_bizNo', 'long_qu_line', 'many_flights'
        ]
        
        # 初始化统计变量
        success_count = 0
        failure_count = 0
        error_type_counts = {error_type: 0 for error_type in error_types}
        error_type_counts['normal'] = 0  # 正常请求
        
        # 发送电报
        start_time = time.time()
        sent_count = 0
        
        while True:
            # 检查是否达到发送数量或运行时间
            if count > 0 and sent_count >= count:
                break
            if run_time > 0 and (time.time() - start_time) >= run_time:
                break
            
            # 随机决定是否发送错误请求
            error_type = None
            if random.random() < error_rate:
                error_type = random.choice(error_types)
                logger.info(f'Sending telegram {sent_count+1} with error type: {error_type}')
            else:
                logger.info(f'Sending telegram {sent_count+1} (normal)')
            
            result = send_telegram(error_type)
            sent_count += 1
            
            # 更新统计信息
            if result['success']:
                success_count += 1
                # 记录正常请求或错误类型请求的成功
                if error_type is None:
                    error_type_counts['normal'] += 1
                else:
                    error_type_counts[error_type] += 1
            else:
                failure_count += 1
                # 记录失败的错误类型
                if error_type is not None:
                    error_type_counts[error_type] += 1
                else:
                    error_type_counts['normal'] += 1
            
            # 如果不是最后一次发送，等待间隔时间
            if (count == 0 or sent_count < count) and (run_time == 0 or (time.time() - start_time + interval) < run_time):
                time.sleep(interval)
        
        # 打印统计结果
        end_time = time.time()
        logger.info(f'Telegram sending completed. Total sent: {sent_count}, Success: {success_count}, Failure: {failure_count}')
        logger.info('Error type statistics:')
        for error_type, count in error_type_counts.items():
            logger.info(f'  {error_type}: {count}')
        logger.info(f'Total time used: {end_time - start_time:.2f} seconds')
    except KeyboardInterrupt:
        logger.info('Program stopped by user')
    except Exception as e:
        logger.error(f'Error in main function: {str(e)}')

if __name__ == '__main__':
    main()