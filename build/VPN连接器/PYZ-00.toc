('/Users/<USER>/PycharmProjects/workPro/build/VPN连接器/PYZ-00.pyz',
 [('__future__',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compression.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ast.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/base64.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/calendar.py',
   'PYMODULE'),
  ('certifi',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/certifi/core.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/assets/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/copy.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/csv.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/decimal.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dis.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gettext.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hmac.py',
   'PYMODULE'),
  ('http',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/cookies.py',
   'PYMODULE'),
  ('idna',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.core',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ipaddress.py',
   'PYMODULE'),
  ('json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/mimetypes.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/opcode.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pathlib.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/py_compile.py',
   'PYMODULE'),
  ('queue',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py',
   'PYMODULE'),
  ('requests',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('requests.api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.auth',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests.certs',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/certs.py',
   'PYMODULE'),
  ('requests.compat',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/compat.py',
   'PYMODULE'),
  ('requests.cookies',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.models',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/models.py',
   'PYMODULE'),
  ('requests.packages',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/packages.py',
   'PYMODULE'),
  ('requests.sessions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/requests/utils.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/selectors.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/signal.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/socket.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py',
   'PYMODULE'),
  ('tkinter',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/messagebox.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/typing.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/response.py',
   'PYMODULE'),
  ('urllib3',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/contrib/_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/packages/__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/packages/backports/__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/packages/backports/makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/packages/six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/request.py',
   'PYMODULE'),
  ('urllib3.response',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipfile.py',
   'PYMODULE')])
