# -*- coding: utf-8 -*-
import os
import openpyxl
import pandas as pd
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH  # 导入居中对齐常量
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

# 获取当前目录
current_directory = os.getcwd()

from datetime import datetime


def set_cell_borders(cell, color="A9A9A9", width="4"):
    """设置单元格的边框样式"""
    tc_pr = cell._element.get_or_add_tcPr()
    borders = OxmlElement("w:tcBorders")

    for border_name in ["top", "left", "bottom", "right"]:
        border = OxmlElement(f"w:{border_name}")
        border.set(qn("w:val"), "single")  # 实线
        border.set(qn("w:sz"), width)  # 宽度
        border.set(qn("w:space"), "0")  # 无间距
        border.set(qn("w:color"), color)  # 边框颜色
        borders.append(border)

    tc_pr.append(borders)


def get_current_and_next_month(today=None):
    # 如果 today 为 None，则默认为上个月的当天
    if today is None:
        today = datetime.today()
        # 计算上个月的日期
        if today.month == 1:
            today = today.replace(year=today.year - 1, month=12)
        else:
            today = today.replace(month=today.month - 1)

    # 获取当前年月（格式为 "2024 年 12 月"）
    current_year_month = today.strftime("%Y 年 %m 月")

    # 计算下月的年月
    # 如果当前是12月，下一月为下一年的1月
    next_month_date = today.replace(month=today.month % 12 + 1, year=today.year + (today.month // 12))
    next_year_month = next_month_date.strftime("%Y 年 %m 月")
    current_year_month_fomart = today.strftime("%Y年%m月")

    return current_year_month, next_year_month, current_year_month_fomart


# 示例：调用函数
current, next_month, current_fomart = get_current_and_next_month()

# 遍历当前目录中的所有文件
for filename in os.listdir(current_directory):
    # 只处理 .xlsx 文件
    if filename.endswith('.xlsx'):
        file_path = os.path.join(current_directory, filename)

        # 打开 Excel 文件
        workbook = openpyxl.load_workbook(file_path, data_only=True)  # data_only=True 获取公式的计算结果

        # 检查是否有名为 "实际计算单汇总表" 的工作表
        if "实际结算单汇总表" in workbook.sheetnames:
            sheet = workbook["实际结算单汇总表"]
            # 将工作表内容转换为 pandas DataFrame
            data = sheet.values
            columns = next(data)  # 获取第一行作为列名
            df = pd.DataFrame(data, columns=columns)

            # 读取"当月人员清单"sheet的数据
            if "当月人员清单" in workbook.sheetnames:
                personnel_sheet = workbook["当月人员清单"]
                personnel_data = personnel_sheet.values
                personnel_columns = next(personnel_data)  # 获取第一行作为列名
                personnel_df = pd.DataFrame(personnel_data, columns=personnel_columns)

                # 确保数据框包含所需的列
                if "姓名" in personnel_df.columns and "岗位等级" in personnel_df.columns:
                    # 使用merge更新主表的岗位等级
                    df = df.merge(personnel_df[["姓名", "岗位等级"]],
                                  on="姓名",
                                  how="left",
                                  suffixes=("", "_new"))

                    # 更新岗位等级：如果新的岗位等级存在，则使用新的，否则保留原来的
                    df["岗位等级"] = df["岗位等级_new"].fillna(df["岗位等级"])

                    # 删除多余的列
                    if "岗位等级_new" in df.columns:
                        df = df.drop(columns=["岗位等级_new"])
                else:
                    print("警告：当月人员清单中缺少必要的列（姓名、岗位等级）")
            else:
                print("警告：未找到'当月人员清单'工作表")

            # 定义岗位等级对应的单价
            price_mapping = {
                "中级UI工程师": 1000,
                "中级测试工程师": 1000,
                "高级测试工程师": 1400,
                "中级后端开发工程师": 1050,
                "高级后端开发工程师": 1400,
                "中级前端开发工程师": 1050,
                "高级前端开发工程师": 1400,
                "运维工程师": 1400,
                "中级算法工程师": 1500,
                "高级算法工程师": 1800,
                "中级产品经理": 1300,
                "高级产品经理": 1500
            }

            # 根据岗位等级更新单价
            df["单价"] = df["岗位等级"].map(price_mapping)
            # 重新计算结算金额
            df["华夏航空结算金额（元）"] = df["华夏航空结算人天"] * df["单价"]
            print(df.to_string())
            df_cleaned = df.dropna(
                subset=["项目名称", "岗位等级", "华夏航空结算人天", "华夏航空结算金额（元）", "使用部门", "项目经理"])
            df_unique = df[['项目名称', '使用部门', '项目经理']].drop_duplicates()
            excel_name = f'{current_fomart}结算清单'
            df1 = df[["姓名", "岗位等级", "单价", "华夏航空结算人天", "考勤数据人天", "华夏航空结算金额（元）"]]
            # 重命名列
            df1 = df1.rename(columns={"华夏航空结算人天": "结算人天", "华夏航空结算金额（元）": "结算金额（元）"})
            df1['考勤数据人天'] = df1['考勤数据人天'].round(1)
            df1 = df1.dropna(subset=["姓名"])
            # 合并相同人员的数据
            df1 = df1.groupby(["姓名", "岗位等级", "单价"]).agg({
                "结算人天": "sum",
                "考勤数据人天": "sum",
                "结算金额（元）": "sum"
            }).reset_index()

            # 按姓名排序
            df1 = df1.sort_values(by=["姓名"])
            # 计算总计行
            total_row = pd.DataFrame({
                "姓名": [""],
                "岗位等级": [""],
                "单价": [""],
                "结算人天": [df1["结算人天"].sum()],
                "考勤数据人天": [df1["考勤数据人天"].sum()],
                "结算金额（元）": [df1["结算金额（元）"].sum()]
            })

            # 将总计行添加到df1
            df1 = pd.concat([df1, total_row], ignore_index=True)

            # 保存为Excel文件
            # output_path = os.path.join(current_directory, f"{excel_name}.xlsx")
            output_path = f"./结算单/{excel_name}.xlsx"
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df1.to_excel(writer, sheet_name=excel_name, index=False, startrow=1)  # 从第2行开始写入数据

                # 获取工作簿和工作表
                workbook = writer.book
                worksheet = writer.sheets[excel_name]

                # 添加标题并合并单元格
                worksheet.merge_cells(f'A1:{openpyxl.utils.get_column_letter(len(df1.columns))}1')
                title_cell = worksheet['A1']
                title_cell.value = excel_name
                title_cell.font = openpyxl.styles.Font(bold=True, size=14)
                title_cell.alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')

                # 设置列宽 - 增加宽度确保不会缩在一起
                for i, col in enumerate(df1.columns):
                    column_width = max(len(str(col)), df1[col].astype(str).map(len).max()) + 12  # 增加宽度
                    worksheet.column_dimensions[openpyxl.utils.get_column_letter(i + 1)].width = column_width

                # 设置标题行格式（现在是第2行）
                for cell in worksheet[2]:
                    cell.font = openpyxl.styles.Font(bold=True)
                    cell.alignment = openpyxl.styles.Alignment(horizontal='center')

                # 设置数值格式和边框（需要调整行号，因为添加了标题）
                thin_border = openpyxl.styles.Border(
                    left=openpyxl.styles.Side(style='thin'),
                    right=openpyxl.styles.Side(style='thin'),
                    top=openpyxl.styles.Side(style='thin'),
                    bottom=openpyxl.styles.Side(style='thin')
                )

                # 为标题行添加边框
                for cell in worksheet[2]:
                    cell.border = thin_border

                # 为标题单元格添加边框
                title_cell.border = thin_border

                # 为数据行添加边框和格式
                for row in range(3, len(df1) + 3):  # 数据从第3行开始
                    for col_idx, col_name in enumerate(df1.columns):
                        cell = worksheet.cell(row=row, column=col_idx + 1)
                        if col_name in ["结算人天", "考勤数据人天"]:
                            cell.number_format = '0.0'
                        elif col_name == "结算金额（元）":
                            cell.number_format = '#,##0'

                        # 居中对齐
                        cell.alignment = openpyxl.styles.Alignment(horizontal='center')
                        # 添加边框
                        cell.border = thin_border

                # 设置最后一行（总计行）的格式
                last_row = len(df1) + 2  # 因为添加了标题行，所以要加2
                for col_idx, col_name in enumerate(df1.columns):
                    cell = worksheet.cell(row=last_row, column=col_idx + 1)
                    if col_name in ["结算人天", "考勤数据人天", "结算金额（元）"]:
                        cell.font = openpyxl.styles.Font(bold=True)
                    # 添加边框
                    cell.border = thin_border

                # 调整行高
                worksheet.row_dimensions[1].height = 30  # 标题行高度
                for row in range(2, len(df1) + 3):
                    worksheet.row_dimensions[row].height = 20  # 数据行高度

            print(f"已将结算清单保存为: {output_path}")
            # 查看结果
            for row in df_unique.itertuples(index=False):
                doc = Document('template.docx')
                项目名称 = row.项目名称
                使用部门 = row.使用部门
                项目经理 = row.项目经理
                if 项目名称 is not None:
                    filtered_df = df[df['项目名称'] == 项目名称]

                    # 查看结果
                    # print(filtered_df.to_string())
                    结算单 = (
                        filtered_df.groupby(["项目名称", "岗位等级"])["华夏航空结算人天"]
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 按项目名称和人数排序
                    )
                    # print(结算单)
                    jsd_list = []
                    for jsd in 结算单.itertuples(index=False):
                        gwdj = jsd.岗位等级
                        jsrt = jsd.华夏航空结算人天
                        jsd_list.append(["岗位", gwdj, "工作量", f"{jsrt}人天"])

                    # 定位到目标表格
                    table_with_titles = None
                    # 遍历文档中的所有表格
                    for table in doc.tables:
                        # 遍历表格中的所有行
                        for i, row in enumerate(table.rows):
                            # 检查每一行是否包含“时间范围”
                            for cell in row.cells:
                                if '时间范围' in cell.text:
                                    table_with_titles = table
                                    break
                    if not table_with_titles:
                        raise ValueError("未找到包含指定标题的表格！")


                    gzzl = (
                        filtered_df.groupby(["项目名称", "岗位等级", "姓名"])["华夏航空结算人天"]
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 按项目名称和人数排序
                    )

                    gzzl_list = []
                    for gzzl in gzzl.itertuples(index=False):
                        gwdj = gzzl.岗位等级
                        xm = gzzl.姓名
                        jsrt = gzzl.华夏航空结算人天
                        gzzl_list.append([gwdj, xm, str(jsrt)])

                    work_summary_df = (
                        filtered_df.groupby(["项目名称", "岗位等级"])[
                            ["华夏航空结算人天", "华夏航空结算金额（元）"]]  # 使用列表
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 排序
                    )
                    work_summary_list = []
                    work_summary_count = 0
                    work_summary_jsrt = 0
                    for work_summary in work_summary_df.itertuples(index=False):
                        gwdj = work_summary.岗位等级
                        jsrt = work_summary.华夏航空结算人天
                        jsje = work_summary[-1]
                        work_summary_count = work_summary_count + jsje
                        work_summary_jsrt = work_summary_jsrt + jsrt

                        # print(jsje)
                        work_summary_list.append([gwdj, str(jsrt), str(jsje)])

                    填充数据 = {
                        "{项目名称}": 项目名称,
                        "{项目经理}": 项目经理,
                        "{使用部门}": 使用部门,
                        "{本月}": current,
                        "{下月}": next_month,
                        "{标题月份}": current_fomart,
                        "{总计}": work_summary_jsrt
                    }
                    # 遍历文档中的所有段落
                    for paragraph in doc.paragraphs:
                        # 遍历字典中的每个标签，进行替换
                        for 标签, 数据 in 填充数据.items():
                            if 标签 in paragraph.text:
                                # 替换标签为对应的数据
                                paragraph.text = paragraph.text.replace(标签, str(数据))
                                # 设置字体为宋体，字号为 10号
                                for run in paragraph.runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(16)
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # 遍历文档中的所有表格
                    for table in doc.tables:
                        # 遍历每一行
                        for row in table.rows:
                            # 遍历每一行中的每个单元格
                            for cell in row.cells:
                                # 遍历字典中的每个标签，进行替换
                                for 标签, 数据 in 填充数据.items():
                                    if 标签 in cell.text:
                                        # 替换标签为对应的数据
                                        cell.text = cell.text.replace(标签, str(数据))
                                        # 设置字体为宋体，字号为 10号
                                        for paragraph in cell.paragraphs:
                                            for run in paragraph.runs:
                                                run.font.name = '宋体'
                                                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                                run.font.size = Pt(10)
                                            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.LEFT

                    # 设置标题格式：寻找包含"项目服务结算单"的段落
                    for paragraph in doc.paragraphs:
                        if "结算单" in paragraph.text:
                            # 设置该段落格式
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                            for run in paragraph.runs:
                                run.font.name = '宋体'
                                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                run.font.size = Pt(15)
                            # 找到后即可退出循环
                            break

                    doc.save(f"./结算单/{项目名称}{current_fomart}结算单.docx")



        else:
            print(f"文件: {filename} 没有名为 '实际计算单汇总表' 的工作表.")
