# -*- coding: utf-8 -*-
import os
import openpyxl
import pandas as pd
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH  # 导入居中对齐常量
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

# 获取当前目录
current_directory = os.getcwd()

from datetime import datetime


def set_cell_borders(cell, color="A9A9A9", width="4"):
    """设置单元格的边框样式"""
    tc_pr = cell._element.get_or_add_tcPr()
    borders = OxmlElement("w:tcBorders")

    for border_name in ["top", "left", "bottom", "right"]:
        border = OxmlElement(f"w:{border_name}")
        border.set(qn("w:val"), "single")  # 实线
        border.set(qn("w:sz"), width)  # 宽度
        border.set(qn("w:space"), "0")  # 无间距
        border.set(qn("w:color"), color)  # 边框颜色
        borders.append(border)

    tc_pr.append(borders)


#
# def get_current_and_next_month():
#     # 获取当前日期
#     today = datetime.today()
#
#     # 获取当前年月（格式为 "2024 年 12 月"）
#     current_year_month = today.strftime("%Y 年 %m 月")
#
#     # 计算下月的年月
#     # 如果当前是12月，下一月为下一年的1月
#     next_month_date = today.replace(month=today.month % 12 + 1, year=today.year + (today.month // 12))
#     next_year_month = next_month_date.strftime("%Y 年 %m 月")
#     current_year_month_fomart = today.strftime("%Y年%m月")
#
#     return current_year_month, next_year_month, current_year_month_fomart

def get_current_and_next_month(today=None):
    # 如果 today 为 None，则默认为上个月的当天
    if today is None:
        today = datetime.today()
        # 计算上个月的日期
        if today.month == 1:
            today = today.replace(year=today.year - 1, month=12)
        else:
            today = today.replace(month=today.month - 1)

    # 获取当前年月（格式为 "2024 年 12 月"）
    current_year_month = today.strftime("%Y 年 %m 月")

    # 计算下月的年月
    # 如果当前是12月，下一月为下一年的1月
    next_month_date = today.replace(month=today.month % 12 + 1, year=today.year + (today.month // 12))
    next_year_month = next_month_date.strftime("%Y 年 %m 月")
    current_year_month_fomart = today.strftime("%Y年%m月")

    return current_year_month, next_year_month, current_year_month_fomart


# 示例：调用函数
current, next_month, current_fomart = get_current_and_next_month()

# 遍历当前目录中的所有文件
for filename in os.listdir(current_directory):
    # 只处理 .xlsx 文件
    if filename.endswith('.xlsx'):
        file_path = os.path.join(current_directory, filename)

        # 打开 Excel 文件
        workbook = openpyxl.load_workbook(file_path, data_only=True)  # data_only=True 获取公式的计算结果

        # 检查是否有名为 "实际计算单汇总表" 的工作表
        if "实际结算单汇总表" in workbook.sheetnames:
            sheet = workbook["实际结算单汇总表"]
            # 将工作表内容转换为 pandas DataFrame
            data = sheet.values
            columns = next(data)  # 获取第一行作为列名
            df = pd.DataFrame(data, columns=columns)

            df_cleaned = df.dropna(
                subset=["项目名称", "岗位等级", "华夏航空结算人天", "华夏航空结算金额（元）", "使用部门", "项目经理"])
            df_unique = df[['项目名称', '使用部门', '项目经理']].drop_duplicates()

            # 查看结果
            for row in df_unique.itertuples(index=False):
                doc = Document('template.docx')
                项目名称 = row.项目名称
                使用部门 = row.使用部门
                项目经理 = row.项目经理
                if 项目名称 is not None:
                    filtered_df = df[df['项目名称'] == 项目名称]

                    # 查看结果
                    # print(filtered_df)
                    结算单 = (
                        filtered_df.groupby(["项目名称", "岗位等级"])["华夏航空结算人天"]
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 按项目名称和人数排序
                    )
                    # print(结算单)
                    jsd_list = []
                    for jsd in 结算单.itertuples(index=False):
                        gwdj = jsd.岗位等级
                        jsrt = jsd.华夏航空结算人天
                        jsd_list.append(["岗位", gwdj, "工作量", f"{jsrt}人天"])

                    # 定位到目标表格
                    table_with_titles = None
                    # 遍历文档中的所有表格
                    for table in doc.tables:
                        # 遍历表格中的所有行
                        for i, row in enumerate(table.rows):
                            # 检查每一行是否包含“时间范围”
                            for cell in row.cells:
                                if '时间范围' in cell.text:
                                    table_with_titles = table
                                    break
                    if not table_with_titles:
                        raise ValueError("未找到包含指定标题的表格！")

                    # 确定插入行的位置（例如在第2行和第3行之间插入，索引从0开始）
                    insert_position = 5  # 表示在第2行之后插入新行（第3行之前）

                    # # 创建新行
                    new_row_data = jsd_list
                    for data_row in new_row_data:
                        # 创建新行
                        new_row = table_with_titles.add_row()
                        for i, cell in enumerate(new_row.cells):
                            if i < len(data_row):  # 防止超出数据范围
                                cell.text = data_row[i]
                                # 设置字体样式为宋体、字号为11号，内容居中
                                for run in cell.paragraphs[0].runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(10)
                                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                                set_cell_borders(cell)
                        # 调整新行到目标位置
                        tbl = table_with_titles._tbl  # 获取底层 XML 表格对象
                        rows = tbl.xpath("./w:tr")  # 获取所有行
                        new_row_xml = rows[-1]  # 新插入的行为最后一行
                        tbl.remove(new_row_xml)  # 删除新行
                        tbl.insert(insert_position + 1, new_row_xml)  # 插入到目标位置之后

                        # 插入完成后，更新目标位置索引
                        insert_position += 1
                    gzzl = (
                        filtered_df.groupby(["项目名称", "岗位等级", "姓名"])["华夏航空结算人天"]
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 按项目名称和人数排序
                    )

                    gzzl_list = []
                    for gzzl in gzzl.itertuples(index=False):
                        gwdj = gzzl.岗位等级
                        xm = gzzl.姓名
                        jsrt = gzzl.华夏航空结算人天
                        gzzl_list.append([gwdj, xm, str(jsrt)])
                    # print(gzzl_list)
                    table_with_titles_1 = None
                    for table in doc.tables:
                        first_row_text = [cell.text.strip() for cell in table.rows[0].cells]
                        # print(first_row_text)
                        if "岗位" in first_row_text and "工作人员" in first_row_text and "工作量（人/天）" in first_row_text:
                            table_with_titles_1 = table
                            break

                    if not table_with_titles_1:
                        raise ValueError("未找到包含指定标题的表格！")
                    # 确定插入行的位置（例如在第2行和第3行之间插入，索引从0开始）
                    insert_position_1 = 2  # 表示在第2行之后插入新行（第3行之前）

                    # # 创建新行
                    new_row_data = gzzl_list
                    for data_row in new_row_data:
                        # 创建新行
                        new_row = table_with_titles_1.add_row()
                        for i, cell in enumerate(new_row.cells):
                            if i < len(data_row):  # 防止超出数据范围
                                cell.text = data_row[i]
                                # 设置字体样式为宋体、字号为11号，内容居中
                                for run in cell.paragraphs[0].runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(10)
                                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                                set_cell_borders(cell)
                            # 调整新行到目标位置
                        tbl = table_with_titles_1._tbl  # 获取底层 XML 表格对象
                        rows = tbl.xpath("./w:tr")  # 获取所有行
                        new_row_xml = rows[-1]  # 新插入的行为最后一行
                        tbl.remove(new_row_xml)  # 删除新行
                        tbl.insert(insert_position_1 + 1, new_row_xml)  # 插入到目标位置之后

                        # 插入完成后，更新目标位置索引
                        insert_position_1 += 1
                    work_summary_df = (
                        filtered_df.groupby(["项目名称", "岗位等级"])[
                            ["华夏航空结算人天", "华夏航空结算金额（元）"]]  # 使用列表
                        .sum()
                        .reset_index()
                        .sort_values(by=["项目名称", "华夏航空结算人天"], ascending=[True, False])  # 排序
                    )
                    # print(work_summary)
                    work_summary_list = []
                    work_summary_count = 0
                    for work_summary in work_summary_df.itertuples(index=False):
                        gwdj = work_summary.岗位等级
                        jsrt = work_summary.华夏航空结算人天
                        jsje = work_summary[-1]
                        work_summary_count = work_summary_count + jsje
                        # print(jsje)
                        work_summary_list.append([gwdj, str(jsrt), str(jsje)])
                    # print(gzzl_list)
                    table_with_titles_2 = None
                    for table in doc.tables:
                        first_row_text = [cell.text.strip() for cell in table.rows[0].cells]
                        # print(first_row_text)
                        if "岗位" in first_row_text and "预付服务金额（元）" in first_row_text and "工作量（人/天）" in first_row_text:
                            table_with_titles_2 = table
                            break

                    if not table_with_titles_2:
                        raise ValueError("未找到包含指定标题的表格！")
                    # 确定插入行的位置（例如在第2行和第3行之间插入，索引从0开始）
                    insert_position_2 = 2  # 表示在第2行之后插入新行（第3行之前）

                    # # 创建新行
                    new_row_data = work_summary_list
                    # print(new_row_data)
                    for data_row in new_row_data:
                        # 创建新行
                        new_row = table_with_titles_2.add_row()
                        for i, cell in enumerate(new_row.cells):
                            if i < len(data_row):  # 防止超出数据范围
                                cell.text = data_row[i]
                                # 设置字体样式为宋体、字号为11号，内容居中
                                for run in cell.paragraphs[0].runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(10)
                                cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
                                set_cell_borders(cell)
                            # 调整新行到目标位置
                        tbl = table_with_titles_2._tbl  # 获取底层 XML 表格对象
                        rows = tbl.xpath("./w:tr")  # 获取所有行
                        new_row_xml = rows[-1]  # 新插入的行为最后一行
                        tbl.remove(new_row_xml)  # 删除新行
                        tbl.insert(insert_position_2 + 1, new_row_xml)  # 插入到目标位置之后

                        # 插入完成后，更新目标位置索引
                        insert_position_2 += 1
                    填充数据 = {
                        "{项目名称}": 项目名称,
                        "{项目经理}": 项目经理,
                        "{使用部门}": 使用部门,
                        "{本月}": current,
                        "{下月}": next_month,
                        "{标题月份}": current_fomart,
                        "{总计}": work_summary_count
                    }
                    # 遍历文档中的所有段落
                    for paragraph in doc.paragraphs:
                        # 遍历字典中的每个标签，进行替换
                        for 标签, 数据 in 填充数据.items():
                            if 标签 in paragraph.text:
                                # 替换标签为对应的数据
                                paragraph.text = paragraph.text.replace(标签, str(数据))
                                # 设置字体为宋体，字号为 10号
                                for run in paragraph.runs:
                                    run.font.name = '宋体'
                                    run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                    run.font.size = Pt(16)
                                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # 遍历文档中的所有表格
                    for table in doc.tables:
                        # 遍历每一行
                        for row in table.rows:
                            # 遍历每一行中的每个单元格
                            for cell in row.cells:
                                # 遍历字典中的每个标签，进行替换
                                for 标签, 数据 in 填充数据.items():
                                    if 标签 in cell.text:
                                        # 替换标签为对应的数据
                                        cell.text = cell.text.replace(标签, str(数据))
                                        # 设置字体为宋体，字号为 10号
                                        for paragraph in cell.paragraphs:
                                            for run in paragraph.runs:
                                                run.font.name = '宋体'
                                                run._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
                                                run.font.size = Pt(10)
                                            cell.paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER

                    doc.save(f"./结算单/{项目名称}{current_fomart}结算单.docx")



        else:
            print(f"文件: {filename} 没有名为 '实际计算单汇总表' 的工作表.")
