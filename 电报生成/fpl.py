#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航班数据查询与AFTN FPL电报生成模块

功能：
1. 根据foc_flight_id查询航班数据
2. 生成符合AFTN格式的FPL电报
3. 支持连接不同的数据库
"""

import pymysql
from pymysql.cursors import DictCursor
import logging
import traceback
import random
from datetime import datetime
from typing import Optional, Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('flight_query')

# AFTN电报常量
AFTN_HEADER = 'ZCZC'
AFTN_FOOTER = 'KKKK'
FPL_TYPE = 'FPL'

class DatabaseConfig:
    """数据库配置类"""
    HOST = '************'
    PORT = 3306
    USER = 'hxyr_test'
    PASSWORD = 'yU6)RLLqa2b'
    CHARSET = 'utf8mb4'

class FlightDataManager:
    """航班数据管理类"""
    
    @staticmethod
    def get_wake_turbulence_category(icao_ac_type: str) -> str:
        """查询机型的尾流等级"""
        connection = None
        try:
            # 从g5air_nfoc_platform_test_db.ac_type表中查询
            connection = FlightDataManager.get_connection('g5air_nfoc_platform_test_db')
            if not connection:
                return 'L'  # 默认值

            with connection.cursor() as cursor:
                query = """
                SELECT wake_turbulence_category 
                FROM ac_type 
                WHERE icao_ac_type = %s
                LIMIT 1
                """
                cursor.execute(query, (icao_ac_type,))
                result = cursor.fetchone()
                return result.get('wake_turbulence_category', 'L') if result else 'L'
        except Exception as e:
            logger.error(f"查询尾流等级失败: {str(e)}")
            return 'L'
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")

    @staticmethod
    def get_fbl_items(ac_no: str) -> Dict[str, str]:
        """查询飞机的fbl_item_ten和fbl_item_eighteen"""
        connection = None
        try:
            connection = FlightDataManager.get_connection('g5air_nfoc_platform_test_db')
            if not connection:
                return {'fbl_item_ten': 'SDFGIRW', 'fbl_item_eighteen': 'LB1'}  # 默认值

            with connection.cursor() as cursor:
                query = """
                SELECT fbl_item_ten, fbl_item_eighteen
                FROM ac
                WHERE ac_no = %s AND deleted = 0 AND effective_status = 'PUBLISHED'
                ORDER BY id DESC
                LIMIT 1
                """
                cursor.execute(query, (ac_no,))
                result = cursor.fetchone()
                if result:
                    return {
                        'fbl_item_ten': result.get('fbl_item_ten', 'SDFGIRW'),
                        'fbl_item_eighteen': result.get('fbl_item_eighteen', 'LB1')
                    }
                else:
                    return {'fbl_item_ten': 'SDFGIRW', 'fbl_item_eighteen': 'LB1'}
        except Exception as e:
            logger.error(f"查询fbl_items失败: {str(e)}")
            return {'fbl_item_ten': 'SDFGIRW', 'fbl_item_eighteen': 'LB1'}
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")

    @staticmethod
    def get_tas_data(foc_flight_id: int) -> str:
        """查询并处理TAS数据"""
        connection = None
        try:
            connection = FlightDataManager.get_connection('g5air_nfoc_dispatch_test_db')
            if not connection:
                return 'K0400'  # 默认值

            with connection.cursor() as cursor:
                query = """
                SELECT cfp_content
                FROM dispatch_plan_cfp_flight_plan
                WHERE foc_flight_id = %s AND use_status = 1 AND deleted = 0
                LIMIT 1
                """
                cursor.execute(query, (foc_flight_id,))
                result = cursor.fetchone()
                
                if result and result.get('cfp_content'):
                    cfp_content = result['cfp_content']
                    # 查找以"TAS "开头的行
                    lines = cfp_content.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line.startswith('TAS '):
                            # 提取TAS后面的数据
                            tas_str = line[4:].strip().split()[0]
                            try:
                                tas_nm = float(tas_str)
                                # 当TAS<400时，按400nm/h计算
                                if tas_nm < 400:
                                    tas_nm = 400
                                # 转换为KM/H (1 nm = 1.852 km)
                                tas_km = round(tas_nm * 1.852)
                                # 格式化为4位数，K开头
                                return f'K{tas_km:04d}'
                            except (ValueError, IndexError):
                                logger.warning(f"TAS数据解析失败: {line}")
                                return 'K0400'
                
                return 'K0400'  # 默认值
        except Exception as e:
            logger.error(f"查询TAS数据失败: {str(e)}")
            return 'K0400'
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")

    @staticmethod
    def get_level_data(dep_icao: str, arr_icao: str) -> str:
        """查询ais_airline_produce表的LEVEL和FPL数据，单位：10米 -> K+4位数"""
        connection = None
        try:
            connection = FlightDataManager.get_connection('hx_ais_prev_db', 'hx_ais_prev', 'A^vkDYB8cyU', 'hxmysqltest1.g5air.com', 3308)
            if not connection:
                print("⚠️ 未找到LEVEL数据，使用默认值: K0500")
                return 'K0500'  # 默认值

            with connection.cursor() as cursor:
                # 构建line_name
                line_name = f"{dep_icao}{arr_icao}"
                
                query = """
                SELECT line_id, air_type, air_alias, LEVEL, FPL
                FROM ais_airline_produce 
                WHERE line_id = (
                    SELECT id 
                    FROM ais_airline_base 
                    WHERE line_name = %s 
                    ORDER BY id DESC 
                    LIMIT 1
                )
                """
                cursor.execute(query, (line_name,))
                result = cursor.fetchone()
                
                if result and result.get('LEVEL') is not None:
                    level_value = int(result['LEVEL'])
                    # 处理单位转换：数据库值6000 -> 实际600米 -> 格式化为K0600
                    # 假设数据库值除以10得到实际米数，然后取整百
                    actual_level = level_value // 10  # 6000 -> 600
                    formatted_level = f'K{actual_level:04d}'  # 600 -> K0600
                    fpl_value = result.get('FPL', 'FPL')
                    print(f"✅ 成功获取LEVEL数据: {level_value} -> {formatted_level}, FPL: {fpl_value}")
                    return formatted_level, fpl_value
                else:
                    print(f"⚠️ 未找到LEVEL数据，使用默认值: K0500 (航线: {line_name})")
                    return 'K0500', 'FPL'  # 默认值
        except Exception as e:
            print(f"❌ 获取LEVEL数据失败: {e}，使用默认值: K0500")
            logger.error(f"查询LEVEL数据失败: {str(e)}")
            return 'K0500', 'FPL'
        finally:
            if connection:
                try:
                    connection.close()
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")

    @staticmethod
    def get_connection(database: str, username: str = None, password: str = None, host: str = None, port: int = None) -> pymysql.Connection:
        """
        获取数据库连接
        :param database: 数据库名称
        :return: 数据库连接对象
        """
        try:
            # 根据数据库名称使用不同的连接参数
            if database == 'hx_ais_prev_db':
                config = {
                    'host': host or 'hxmysqltest1.g5air.com',
                    'port': port or 3308,
                    'user': username or 'hx_ais_prev',
                    'password': password or 'A^vkDYB8cyU',
                    'database': database,
                    'charset': DatabaseConfig.CHARSET,
                    'cursorclass': DictCursor,
                    'connect_timeout': 10
                }
            else:
                config = {
                    'host': DatabaseConfig.HOST,
                    'port': DatabaseConfig.PORT,
                    'user': DatabaseConfig.USER,
                    'password': DatabaseConfig.PASSWORD,
                    'database': database,
                    'charset': DatabaseConfig.CHARSET,
                    'cursorclass': DictCursor,
                    'connect_timeout': 10
                }
            
            logger.info(f"连接数据库: {database}")
            connection = pymysql.connect(**config)
            logger.info(f"成功连接到数据库: {database}")
            return connection
            
        except Exception as e:
            logger.error(f"数据库连接失败: {database}, 错误: {str(e)}")
            logger.debug(traceback.format_exc())
            raise
    
    @classmethod
    def get_flight_data(cls, foc_flight_id: int, database: str = 'g5air_nfoc_adjust_test_db') -> Optional[Dict[str, Any]]:
        """
        根据foc_flight_id查询航班数据
        :param foc_flight_id: 航班ID
        :param database: 数据库名称
        :return: 航班数据字典或None
        """
        connection = None
        try:
            connection = cls.get_connection(database)
            
            with connection.cursor() as cursor:
                sql = """
                    SELECT 
                        foc_flight_id,
                        icao_ac_type,
                        ac_no,
                        flight_no,
                        flight_date,
                        dep_icao,
                        arr_icao,
                        std,
                        sta,
                        stc,
                        flight_status
                    FROM flight
                    WHERE deleted = 0 AND foc_flight_id = %s
                    LIMIT 1
                """
                
                logger.info(f"查询航班数据: foc_flight_id={foc_flight_id}")
                cursor.execute(sql, (foc_flight_id,))
                result = cursor.fetchone()
                
                if result:
                    logger.info(f"查询成功: {result}")
                else:
                    logger.warning(f"未找到航班数据: foc_flight_id={foc_flight_id}")
                
                return result
                
        except Exception as e:
            logger.error(f"查询失败: {str(e)}")
            logger.debug(traceback.format_exc())
            return None
        finally:
            if connection:
                try:
                    connection.close()
                    logger.info("数据库连接已关闭")
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")

class AirportTelegramManager:
    """机场电报地址管理器"""
    
    @classmethod
    def get_airport_telegram_info(cls, icao: str, database: str = 'g5air_nfoc_platform_test_db') -> Optional[Dict[str, Any]]:
        """
        查询机场电报地址信息
        :param icao: 机场ICAO代码
        :param database: 数据库名称
        :return: 机场电报地址信息字典
        """
        connection = None
        try:
            connection = FlightDataManager.get_connection(database)
            
            with connection.cursor() as cursor:
                sql = """
                    SELECT 
                        icao, 
                        sita_address_pln, 
                        aftn_address_pln, 
                        sita_address_rls, 
                        aftn_address_rls, 
                        sita_address_fpl, 
                        aftn_address_fpl, 
                        aftn_address_fpl_checked, 
                        aftn_address_pln_checked, 
                        aftn_address_rls_checked 
                    FROM contact_airport_telegram 
                    WHERE deleted = 0 AND icao = %s
                    LIMIT 1
                """
                
                logger.info(f"查询机场电报信息: icao={icao}")
                cursor.execute(sql, (icao,))
                result = cursor.fetchone()
                
                if result:
                    logger.info(f"查询成功: {result}")
                else:
                    logger.warning(f"未找到机场电报信息: icao={icao}")
                
                return result
                
        except Exception as e:
            logger.error(f"查询机场电报信息失败: {str(e)}")
            logger.debug(traceback.format_exc())
            return None
        finally:
            if connection:
                try:
                    connection.close()
                    logger.info("机场电报数据库连接已关闭")
                except Exception as e:
                    logger.error(f"关闭连接失败: {str(e)}")

class AFTNTelegramGenerator:
    """AFTN/SITA电报生成器"""
    
    @staticmethod
    def format_time(time_str: str) -> str:
        """格式化时间字符串为HHMM格式"""
        if not time_str:
            return '0000'
        
        try:
            # 处理完整日期时间格式，如 '2025-08-19 13:50:00'
            if isinstance(time_str, str) and len(time_str) >= 19:
                dt = datetime.strptime(time_str[:19], '%Y-%m-%d %H:%M:%S')
                return dt.strftime('%H%M')
            else:
                # 处理纯时间格式，如 '13:50'
                time_str = str(time_str).replace(':', '').replace('.', '').strip()
                return time_str[:4] if len(time_str) >= 4 else time_str.ljust(4, '0')
        except (ValueError, TypeError):
            # 如果解析失败，使用默认值
            return '0000'
    
    @staticmethod
    def format_date(date_obj) -> str:
        """格式化日期为AFTN格式"""
        if isinstance(date_obj, str):
            try:
                return datetime.strptime(date_obj, '%Y-%m-%d').strftime('%d%m%y')
            except ValueError:
                try:
                    return datetime.strptime(date_obj, '%Y/%m/%d').strftime('%d%m%y')
                except ValueError:
                    return datetime.now().strftime('%d%m%y')
        elif isinstance(date_obj, datetime):
            return date_obj.strftime('%d%m%y')
        else:
            return datetime.now().strftime('%d%m%y')
    
    @classmethod
    def generate_fpl(cls, flight_data: Dict[str, Any], dep_icao: str = None) -> Optional[str]:
        """
        生成AFTN/SITA FPL电报
        :param flight_data: 航班数据
        :param dep_icao: 起飞机场ICAO代码，用于查询电报地址
        :return: AFTN/SITA格式电报字符串，多个电报用\n---\n分隔
        """
        if not flight_data:
            logger.error("航班数据为空")
            return None
        
        try:
            # 提取数据
            flight_no = str(flight_data.get('flight_no', '')).strip().upper()
            icao_ac_type = str(flight_data.get('icao_ac_type', '')).strip().upper()
            ac_no = str(flight_data.get('ac_no', '')).strip().upper()
            dep_icao = dep_icao or str(flight_data.get('dep_icao', '')).strip().upper()
            arr_icao = str(flight_data.get('arr_icao', '')).strip().upper()
            flight_date = flight_data.get('flight_date')
            std = str(flight_data.get('std', ''))
            wake_turbulence_category = FlightDataManager.get_wake_turbulence_category(icao_ac_type)
            
            # 获取fbl_items数据
            fbl_items = FlightDataManager.get_fbl_items(ac_no)
            fbl_item_ten = fbl_items.get('fbl_item_ten', 'SDFGIRW')
            fbl_item_eighteen = fbl_items.get('fbl_item_eighteen', 'LB1')
            
            # 获取TAS数据
            tas_data = FlightDataManager.get_tas_data(flight_data.get('foc_flight_id'))
            # 获取LEVEL和FPL数据
            level_data, fpl_data = FlightDataManager.get_level_data(dep_icao, arr_icao)
            
            # 验证必要字段
            required_fields = [flight_no, icao_ac_type, dep_icao, arr_icao]
            if not all(required_fields):
                logger.error(f"必要字段缺失: {required_fields}")
                return None
            
            # 获取机场电报信息
            airport_info = AirportTelegramManager.get_airport_telegram_info(dep_icao)
            if not airport_info:
                logger.error(f"未找到机场{dep_icao}的电报信息")
                return None
            
            # 获取当前时间
            now = datetime.now()
            current_ddhhmm = now.strftime('%d%H%M')
            
            # 格式化数据
            date_str = cls.format_date(flight_date)
            time_str = cls.format_time(std)
            
            # 判断发送链路类型
            aftn_checked = airport_info.get('aftn_address_fpl_checked', 0)
            telegrams = []
            
            # 始终生成SITA电报
            sita_address = airport_info.get('sita_address_fpl', '')
            if not sita_address:
                logger.error(f"机场{dep_icao}缺少SITA地址")
                return None
            
            # 处理航班号格式：将G5开头的转换为HXA
            callsign = flight_no
            if callsign.upper().startswith('G5'):
                callsign = 'HXA' + callsign[2:]
            
            # 确定飞行种类：如果stc为J则为S，否则为N
            flight_type = 'S' if str(flight_data.get('stc', '')).upper() == 'J' else 'N'
            
            sita_telegram = f"""============= [SITA电报] =============
ZCZC HXC{random.randint(1, 999):03d} {current_ddhhmm}
QU {sita_address}
.CKGUOG5 {current_ddhhmm}
(FPL-{callsign}-I{flight_type}
-{icao_ac_type}/{wake_turbulence_category}-{fbl_item_ten}
-{dep_icao}{time_str}
-{tas_data}{level_data} {fpl_data}
-{arr_icao}{time_str}
-{fbl_item_eighteen}
-EET/
-REG/
-SEL/
-RMK/
{AFTN_FOOTER}"""
            telegrams.append(sita_telegram)
            logger.info(f"SITA FPL电报生成成功: {flight_no}")
            
            # 如果aftn_checked=1，额外生成AFTN电报
            if aftn_checked == 1:
                aftn_address = airport_info.get('aftn_address_pln', '')
                if not aftn_address:
                    logger.error(f"机场{dep_icao}缺少AFTN地址")
                    return None
                
                # 处理航班号格式：将G5开头的转换为HXA
                callsign = flight_no
                if callsign.upper().startswith('G5'):
                    callsign = 'HXA' + callsign[2:]
                
                # 确定飞行种类：如果stc为J则为S，否则为N
                flight_type = 'S' if str(flight_data.get('stc', '')).upper() == 'J' else 'N'
                
                aftn_telegram = f"""============= [AFTN电报] =============
ZCZC HXC{random.randint(1, 999):03d} {current_ddhhmm}
GG {aftn_address}
.ZUCKHXAX {current_ddhhmm}
(FPL-{callsign}-I{flight_type}
-{icao_ac_type}/{wake_turbulence_category}-{fbl_item_ten}
-{dep_icao}{time_str}
-{tas_data}{level_data} {fpl_data}
-{arr_icao}{time_str}
-{fbl_item_eighteen}
-EET/
-REG/
-SEL/
-RMK/
{AFTN_FOOTER}"""
                telegrams.append(aftn_telegram)
                logger.info(f"AFTN FPL电报生成成功: {flight_no}")
            
            # 返回所有电报，用分隔符隔开
            return '\n\n---\n\n'.join(telegrams)
            
        except Exception as e:
            logger.error(f"生成FPL电报失败: {str(e)}")
            logger.debug(traceback.format_exc())
            return None

def get_flight_data(foc_flight_id: int, database: str = 'g5air_nfoc_adjust_test_db') -> Optional[Dict[str, Any]]:
    """兼容旧接口的函数"""
    return FlightDataManager.get_flight_data(foc_flight_id, database)

def generate_aftn_fpl(flight_data: Dict[str, Any]) -> Optional[str]:
    """兼容旧接口的函数"""
    return AFTNTelegramGenerator.generate_fpl(flight_data)

def main(foc_id):
    """主函数 - 用于测试"""
    try:
        # 测试查询航班数据
        flight_data = get_flight_data(foc_id)
        
        if flight_data:
            print("=== 航班数据 ===")
            for key, value in flight_data.items():
                print(f"{key}: {value}")
            
            print("\n=== AFTN FPL电报 ===")
            fpl_telegram = generate_aftn_fpl(flight_data)
            if fpl_telegram:
                print(fpl_telegram)
            else:
                print("无法生成FPL电报")
        else:
            print("未查询到航班数据")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    main(1941792)