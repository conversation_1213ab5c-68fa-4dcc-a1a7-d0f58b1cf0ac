# -*- coding: utf-8 -*-
import datetime
import time
import random
import tkinter as tk
from threading import Thread, Event
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import tkinter.messagebox

Session = sessionmaker(
    bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/hxyr_common_data_test'))
session = Session()

stop_event = Event()
start_time = None


def update_timer():
    if start_time and not stop_event.is_set():
        elapsed_time = time.time() - start_time
        hours = int(elapsed_time // 3600)
        minutes = int((elapsed_time % 3600) // 60)
        seconds = int(elapsed_time % 60)
        timer_label.config(text=f"连接时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        root.after(1000, update_timer)
    elif stop_event.is_set():
        timer_label.config(text="连接时间: 00:00:00")


def query_sql(sql_str):
    try:
        query = session.execute(text(sql_str))
        results = query.all()
        session.close()
        return results
    except Exception as e:
        return None


def run_query():
    while not stop_event.is_set():
        sql = 'SELECT COUNT(*) from dispatch_cfp_text WHERE  parsed_state = 0'
        try:
            sj = query_sql(sql)
            if sj is None and button.text == "结束":
                root.after(0, lambda: tkinter.messagebox.showwarning("警告", "当前未连接VPN，请检查网络连接"))
                root.after(0, lambda: toggle_action())
                return  # 结束线程
            print(f"查询结果: {sj}")
            current_date = datetime.datetime.now()
            print(f"当前时间: {current_date}")
            sleep_time = random.randint(60, 120)
            time.sleep(sleep_time)
        except Exception as e:
            root.after(0, lambda: tkinter.messagebox.showerror("错误", f"查询发生错误: {str(e)}"))


class RoundedButton(tk.Canvas):
    def __init__(self, parent, width, height, text, command, corner_radius=10, padding=0):
        super().__init__(parent, width=width, height=height, bg='white', highlightthickness=0)
        self.command = command
        self._text = text
        self._state = "normal"
        self.width = width
        self.height = height

        self.rect_id = self.create_rounded_rect(padding, padding, width - padding * 2, height - padding * 2,
                                                corner_radius, fill='#4CAF50')
        self.text_id = self.create_text(width / 2, height / 2, text=text, fill='white', font=('Arial', 12))

        self.bind('<Button-1>', self._on_click)
        self.bind('<ButtonRelease-1>', self._on_click)
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)

        self.hitbox = self.create_rectangle(0, 0, width, height, fill='', outline='', tags="hitbox")
        self.tag_bind("hitbox", '<Button-1>', self._on_click)

    def _on_enter(self, event):
        self.configure(cursor='hand2')

    def _on_leave(self, event):
        self.configure(cursor='')

    def create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
        # 创建圆角矩形的路径
        points = [
            x1 + radius, y1,  # 左上角开始
            x2 - radius, y1,
            x2, y1,
            x2, y1 + radius,
            x2, y2 - radius,  # 右边
            x2, y2,
            x2 - radius, y2,  # 右下角
            x1 + radius, y2,  # 底边
            x1, y2,
            x1, y2 - radius,  # 左边
            x1, y1 + radius,
            x1, y1  # 回到起点
        ]
        return self.create_polygon(points, smooth=True, **kwargs)

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, value):
        self._text = value
        self.itemconfig(self.text_id, text=value)
        if value == "开始":
            self.itemconfig(self.rect_id, fill='#4CAF50')  # 绿色
        else:
            self.itemconfig(self.rect_id, fill='#f44336')  # 红色

    def _on_click(self, event):
        if self.command and self._state != "disabled":
            self.command()
            self.itemconfig(self.rect_id, stipple="gray25")
            self.after(100, lambda: self.itemconfig(self.rect_id, stipple=""))


def toggle_action():
    global start_time
    current_time = time.time()
    if hasattr(toggle_action, 'last_click_time') and current_time - toggle_action.last_click_time < 0.5:
        return
    toggle_action.last_click_time = current_time

    button._state = "disabled"

    if button.text == "开始":
        stop_event.clear()
        button.text = "结束"
        start_time = time.time()
        update_timer()
        thread = Thread(target=run_query)
        thread.daemon = True
        thread.start()
    else:
        stop_event.set()
        button.text = "开始"
        start_time = None

    root.update_idletasks()
    root.update()

    root.after(300, lambda: setattr(button, '_state', "normal"))


root = tk.Tk()
root.title("VPN连接器")
root.geometry("300x150")
root.configure(bg='white')
root.resizable(False, False)

timer_label = tk.Label(root, text="连接时间: 00:00:00", font=('Arial', 12), bg='white')
timer_label.pack(pady=20)

button = RoundedButton(root, width=200, height=40, text="开始", command=toggle_action)
button.pack(pady=20)


def center_window():
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 300) // 2
    y = (screen_height - 150) // 2
    root.geometry(f"300x150+{x}+{y}")
    root.update_idletasks()
    root.update()
    root.deiconify()

    def keep_alive():
        try:
            root.update_idletasks()
            root.after(500, keep_alive)
        except Exception:
            pass

    keep_alive()


if __name__ == '__main__':
    root.withdraw()
    root.after(200, center_window)
    root.mainloop()
