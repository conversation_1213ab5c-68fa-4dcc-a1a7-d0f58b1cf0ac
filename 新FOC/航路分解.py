import re
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>


def parse_cfp(cfp_text):
    lines = cfp_text.split('\n')
    data = {
        'route_type': None,
        'route_no': None,
        'cpt': None,
        'freq': None,
        'flt': None,
        'tro': None,
        't': None,
        'tdv': None,
        'wind_direction': None,
        'wind_speed': None,
        'comp': None,
        's': None,
        'tas': None,
        'grs': None,
        'ias': None,
        'mach': None,
        'awy': None,
        'msa': None,
        'mea': None,
        'mhdg': None,
        'mcrs': None,
        'dst': None,
        'dstr': None,
        'ete': None,
        'etr': None,
        'fu': None,
        'afu': None,
        'fr': None,
        'afr': None,
        'ffe': None,
        'lon': None,
        'lat': None,
    }

    # Parse PLAN line
    for line in lines:
        if 'MAIN PLAN' in line:
            print(line)
            data['route_type'] = 1
        if line.startswith('    ..'):
            first_line = line.split()
            print(first_line)
            data['cpt'] = 0 if first_line[1] == '..' else first_line[1]
            data['flt'] = None if first_line[2] == '...' else first_line[2]
            data['t'] = None if first_line[3] == '..' else first_line[3]
            data['wind_direction'] = None if first_line[4] == '.' else first_line[4]
            data['s'] = None if first_line[5] == '.' else first_line[5]
            data['tas'] = None if first_line[6] == '...' else first_line[6]
            data['awy'] = None if first_line[7] == '..' else first_line[7]
            data['mhdg'] = None if first_line[8] == '..' else first_line[8]
            data['dst'] = None if first_line[9] == '..' else first_line[9]
            data['ete'] = None if first_line[10] == '..' else first_line[10]
            data['etr'] = None if first_line[11] == '..' else first_line[11]
            data['fu'] = None if first_line[12] == '..' else first_line[12]
            data['ffe'] = None if first_line[13] == '..' else first_line[13]

            # break

    return pd.DataFrame([data])


if __name__ == '__main__':
    # 示例用法
    text = """
    PLAN 1414            ZSWA TO ZGOW            290/F  IFR 01/22/25
    NONSTOP COMPUTED 1425Z  FOR ETD 0020Z  PROGS  2106UK   B602P    KGS

                FUEL TIME  DIST  ETA   TXW    TOW     LW    ZFW    AV PLD OPNLWT
    DST  ZGOW 003059 01/39 0615 0159Z  036303 036104 033045 030217 007750 022467A

    ALT  ZSAM 000995 00/34 0181 0233Z  038555 038329 034065 032092 009625 022467M
    ALT  NIL  000000 00/00
    HLD       000900 00/30
    RES       000392 00/15
    XTR       000541 00/21
    TOF       005887 03/19
    TXI       000200
    TTL       006087 03/19

    FUEL BURN ADJUST FOR +/-1000KGS TOW: +0020KGS/-0026KGS
    FUEL BURN ADJUST FOR +/-2000FT  FL : -0041KGS/+0226KGS

    FUEL ON BOARD _______________OUT________________IN__________________

    DEP ATIS____________________________________________________________

    CLEARANCE___________________________________________________________

    TRK ZSWAZGOW: ZSWA.WHX.P566.TXN.P602.P473.P392.P645.P215.P48.P555.
                  P453.P232.P610.QO.P176.P379.PV.P508.P509.P510.DABER.
                  ZGOW
    WIND M008 T P09  MXSH 6/TXN
    TAS 378  FL  177/P473  217/P645  226/QO
                 236/P508  207

                        ACT. FLT LEVEL . . . . . . .

    ALT.LEVEL      ETE    WIND    FUEL

    256            0134   M010  002932

    217            0138   M007  003032

    197            0140   M005  003100

    TIME
    IN   ... ..               ON   ... ..
    OUT  ... ..               OFF  ... ..
    BLK  ... ..               FLT  ... ..

                                     RVSM
    RAMP COORD__________________           COAST OUT COORD_____________
    ALTIMETER NO 1______________           ALTIMETER NO 1______________
              NO 2______________                     NO 2______________
              STBY______________                     STBY______________
    ZSWA ELEV  00078FT

                            MAIN PLAN: ZSWA-ZGOW

       CPT   FLT T   WIND  S TAS  AWY      MHDG  DST ETE  ETR   FU   FR  FF/E
       FREQ  TRO TDV COMP    GRS  MSA  MEA MCRS DSTR ETA  ATA  AFU   AFR
                             IAS  MACH
    .. ZSWA  ...  .. . . . . ...  .....    ...   000 .... .... .... .... . .
       ...... .. ...  ....   ...  ...  ... ...  0615
       N31062 E118401        ...  ...
    _________________________________________________________________________
    01 WHX   041  .. . . . . ...  ..       239   007 0002 0137 0002 0057 . .
              .. ...  ....   ...  ...      241  0608
       N31054 E118388        250  CLB
    _________________________________________________________________________
    02 TOC   177  .. . . . . ...  ..       216   032 0006 0131 0004 0053 . .
              .. ...  ....   ...  082 /   /212  0576
       N30366 E118222        290  CLB

    RVSM  ALT DIFF LH_______ft  / RH_______ft
    _________________________________________________________________________
    03 P566  177  15 27029 5 378  ..       216   003 0001 0130 0000 0053 0890
              30  P05 M015   363  082 /   /212  0573
       N30336 E118208        290  0604
    _________________________________________________________________________
    04 TXN   177  14 27031 6 378  ..       197   049 0008 0122 0002 0051 0892
       116.1  30  P06 M005   373  082 /   /191  0524
       N29443 E118152        290  0602
    _________________________________________________________________________
    05 P602  177  13 27031 6 379  ..       234   017 0003 0119 0001 0050 0892
              29  P07 M023   356  060 /   /231  0507
       N29326 E118017        290  0603
    _________________________________________________________________________
    06 P473  177  13 27032 4 379  ..       254   029 0005 0114 0001 0049 0892
              28  P07 M030   349  074 /   /252  0478
       N29209 E117310        290  0603
    _________________________________________________________________________
    07 P392  217  23 28051 5 401  ..       210   018 0003 0111 0001 0047 1377
              28  P05 M012   389  074 /   /203  0460
       N29039 E117249        290  0650
    _________________________________________________________________________
    08 P645  217  22 28052 5 401  ..       211   014 0002 0109 0001 0047 0884
              27  P06 M015   386  074 /   /205  0446
       N28502 E117195        289  0649
    _________________________________________________________________________
    09 P215  226  24 28054 5 407  ..       178   048 0007 0102 0002 0045 0934
              34  P06 P018   425  059 /   /171  0398
       N28036 E117330        290  0661
    _________________________________________________________________________
    10 P48   226  24 28049 5 407  ..       177   032 0004 0058 0001 0043 0885
              47  P06 P017   424  091 /   /171  0366
       N27326 E117419        290  0661
    _________________________________________________________________________
    11 P555  226  23 28047 4 408  ..       177   020 0003 0055 0001 0042 0888
              52  P07 P017   425  091 /   /171  0346
       N27130 E117475        290  0662
    _________________________________________________________________________
    12 P453  226  22 28048 4 409  ..       177   022 0003 0052 0001 0041 0886
              52  P08 P016   425  091 /   /171  0324
       N26520 E117535        290  0662
    _________________________________________________________________________
    13 P232  226  22 28048 5 409  ..       176   009 0001 0051 0000 0041 0891
              51  P08 P016   425  073 /   /170  0315
       N26431 E117561        290  0662
    _________________________________________________________________________
    14 P610  226  21 28049 5 410  ..       176   007 0001 0050 0000 0041 0885
              51  P09 P015   425  073 /   /170  0308
       N26362 E117581        290  0662
    _________________________________________________________________________
    15 QO    226  21 28049 5 410  ..       177   004 0001 0049 0000 0041 0888
              51  P09 P014   424  073 /   /171  0304
       N26326 E117591        290  0662
    _________________________________________________________________________
    16 P176  236  23 28053 6 416  ..       240   014 0002 0047 0001 0040 1055
              52  P09 M039   377  073 /   /235  0290
       N26239 E117475        290  0675
    _________________________________________________________________________
    17 P379  236  23 28055 6 416  ..       240   037 0006 0041 0002 0038 0889
              52  P09 M041   375  077 /   /235  0253
       N26000 E117159        290  0675
    _________________________________________________________________________

    RVSM  ALT DIFF LH_______ft  / RH_______ft
    18 PV    236  21 28061 6 417  ..       241   040 0006 0035 0002 0036 0892
              52  P11 M045   372  079 /   /235  0213
       N25343 E116419        290  0674
    _________________________________________________________________________
    19 P508  236  20 28065 6 418  ..       244   029 0005 0030 0001 0035 0894
              51  P12 M048   370  079 /   /237  0184
       N25165 E116161        290  0674
    _________________________________________________________________________
    20 P509  207  13 28052 5 401  ..       183   023 0003 0027 0001 0034 0642
              51  P13 P013   414  079 /   /177  0161
       N24536 E116195        290  0638
    _________________________________________________________________________
    21 P510  207  13 28054 5 401  ..       132   032 0005 0022 0001 0033 0895
              51  P13 P048   449  072 /   /129  0129
       N24349 E116487        290  0638
    _________________________________________________________________________
    22 DABER 207  12 28056 5 402  ..       186   026 0004 0018 0001 0032 0895
       ZGZU/F 51  P14 P010   412  072 /   /178  0103
       N24086 E116517        290  0638
    _________________________________________________________________________
    23 TOD   207  11 28059 4 403  ..       221   050 0007 0011 0002 0029 0898
              51  P15 M024   379  072 /   /214  0053
       N23516 E116414        290  0639
    _________________________________________________________________________
    24 ZGOW  001  .. . . . . ...  ..       221   053 0011 0000 0001 0028 . .
       ZGOW ELEV   0005FT         072      214  0000
       N23332 E116301        257  DSC
    _________________________________________________________________________

    ARR ATIS____________________________________________________________



    FIRS  ZGZU/0141

                            ALTN PLAN: ZGOW-ZSAM


       CPT   FLT T   WIND  S TAS  AWY   MHDG  DST ETE  ETR   FU   FR  FF/E
       FREQ  TRO TDV COMP    GRS  MSA   MCRS DSTR ETA  ATA  AFU   AFR
                                  MACH
    .. ZGOW  ...  .. . . . . ...  ..... ...   ... ....           .... . .
       ...... .. ...  ....   ...  ...   ...   181 ....           ....
       N23332 E116301        ...  ...
    ______________________________________________________________________
    01 TOC   128 M02 25029 3 256  ..    353   023 0005           0025 . .
              51      P005   261  072   358   158
       N23562 E116276             CLB
    ______________________________________________________________________
    02 VETIB 128 M02 25029 3 318  ..    353   033 0006           0023 0769
              51      P006   324  072   358   125
       N24096 E116262             495
    ______________________________________________________________________
    03 DABER 128 M02 25029 4 318  W155  099   023 0004           0022 0772
              51      P026   344  072   097   102
       N24086 E116517             495
    ______________________________________________________________________
    04 TEBON 128 M02 25029 3 318  W155  096   035 0006           0020 0770
              51      P027   345  072   094   067
       N24083 E117301             495
    ______________________________________________________________________
    05 TOD   128 M02 23010 2 288  ..    097   034 0006           0019 . .
              51      P028   316  071   060   033
       N24280 E118005             DSC
    ______________________________________________________________________
    06 ZSAM  DSC P09 23010 2 000  ..    097   033 0007           0018 . .
              51       000   000  071   060   000
       N24327 E118076             DSC
    ______________________________________________________________________


    ALT - 2

    ALT - 3
    ALT - 4




              START OF WIND AND TEMPERATURE SUMMARY ZSWA TO ZGOW
              --------------------------------------------------

                     LVL  DIR/SPD/OAT  LVL  DIR/SPD/OAT  LVL  DIR/SPD/OAT

    WHX              F380 28/123 M47   F340 28/099 M45   F300 27/067 M46
                     F240 28/051 M31   F180 27/029 M17   F100 20/012 M04

    P566             F380 28/126 M47   F340 28/103 M44   F300 27/067 M45
                     F240 28/052 M31   F180 27/031 M16   F100 20/010 M04

    TXN              F380 29/135 M46   F340 28/112 M42   F300 27/067 M44
                     F240 28/056 M30   F180 28/033 M15   F100 20/007 M04

    P602             F380 29/142 M47   F340 28/118 M40   F300 28/076 M41
                     F240 28/059 M29   F180 27/034 M14   F100 22/008 M04

    P473             F380 29/143 M47   F340 28/122 M40   F300 28/082 M40
                     F240 28/060 M29   F180 27/034 M14   F100 23/009 M04

    P392             F380 29/145 M47   F340 28/125 M39   F300 28/089 M38
                     F240 28/062 M29   F180 27/034 M14   F100 24/010 M04

    P645             F380 29/146 M47   F340 29/128 M38   F300 28/095 M37
                     F240 28/063 M28   F180 27/034 M13   F100 24/010 M04

    P215             F380 29/145 M47   F340 29/129 M40   F300 28/102 M34
                     F240 28/061 M27   F180 27/034 M12   F100 25/012 M04

    P48              F380 29/140 M47   F340 29/129 M37   F300 28/109 M33
                     F240 28/056 M27   F180 27/035 M11   F100 25/015 M04

    P555             F380 29/137 M47   F340 29/128 M37   F300 28/112 M31
                     F240 28/053 M26   F180 27/036 M11   F100 25/018 M04

    P453             F380 29/132 M47   F340 29/125 M37   F300 28/112 M31
                     F240 28/054 M25   F180 28/035 M10   F100 25/022 M03

    P232             F380 29/129 M47   F340 29/123 M37   F300 28/111 M30
                     F240 28/055 M25   F180 28/035 M10   F100 25/025 M02

    P610             F380 29/127 M47   F340 29/122 M37   F300 28/111 M30
                     F240 28/056 M24   F180 28/035 M10   F100 25/027 M02

    QO               F380 29/126 M47   F340 29/121 M37   F300 28/111 M30
                     F240 28/056 M24   F180 28/035 M10   F100 25/028 M02

    P176             F380 29/125 M47   F340 29/120 M37   F300 28/111 M29
                     F240 28/056 M24   F180 28/035 M10   F100 24/029 M02

    P379             F380 29/121 M48   F340 29/117 M37   F300 28/110 M29
                     F240 28/059 M23   F180 28/034 M10   F100 24/032 M01

    PV               F380 29/113 M48   F340 28/110 M38   F300 28/105 M28
                     F240 28/064 M22   F180 28/035 M09   F100 24/026 M01

    P508             F380 28/104 M48   F340 28/103 M38   F300 28/101 M28
                     F240 28/067 M20   F180 28/036 M09   F100 24/025 M01

    P509             F380 28/098 M48   F340 28/098 M38   F300 28/099 M28
                     F240 28/070 M19   F180 28/039 M08   F100 23/026 P00

    P510             F380 28/095 M48   F340 28/094 M38   F300 28/096 M28
                     F240 28/071 M19   F180 28/041 M08   F100 23/024 P00

    DABER            F380 28/091 M49   F340 28/090 M38   F300 28/092 M28
                     F240 28/072 M18   F180 28/043 M07   F100 24/023 P01

    ZGOW             F380 28/085 M49   F340 28/083 M39   F300 28/087 M28
                     F240 28/073 M17   F180 28/048 M06   F100 24/021 P01


    DISPATCHER _________________________
    (HF_________VHF_________TEL________________FAX__________________)
    CAPTAIN    _________________________    F/A  ___________________
    F/O        _________________________    F/A  ___________________
    F/E        _________________________    F/A  ___________________
    ACM        _________________________    ACM  ___________________
    CAPT SIGNATURE _____________________

    END OF JEPPESEN DATAPLAN
    REQUEST NO.  1414

    """
    a = parse_cfp(text)
    print(a.to_string())