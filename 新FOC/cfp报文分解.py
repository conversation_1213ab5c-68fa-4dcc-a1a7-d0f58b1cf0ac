# -*- coding: utf-8 -*-
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
import re
from datetime import datetime, timedelta

Session = sessionmaker(
    bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/hxyr_common_data_test'))
session = Session()


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


def parse_cfp(cfp_text):
    lines = cfp_text.split('\n')
    data = {
        'plan_no': None,
        'dep_icao': None,
        'dst_icao': None,
        'flight_rule': None,
        'flight_date': None,
        'computed_time': None,
        'etd': None,
        'progs': None,
        'act_no': None,
        'weight_unit': None,
        'fly_fuel': None,
        'fly_time': None,
        'fly_dist': None,
        'eta': None,
        'actual_txw': None,
        'actual_tow': None,
        'actual_lw': None,
        'actual_zfw': None,
        'actual_av_pld': None,
        'actual_opnlwt': None,
        'first_alt_icao': None,
        'first_alt_fuel': None,
        'first_alt_time': None,
        'first_alt_dist': None,
        'first_alt_eta': None,
        'second_alt_icao': None,
        'second_alt_fuel': 0,
        'second_alt_time': 0,
        'second_alt_dist': None,
        'second_alt_eta': None,
        'max_txw': None,
        'max_tow': None,
        'max_lw': None,
        'max_zfw': None,
        'max_av_pld': None,
        'max_opnlwt': None,
        'hld': None,
        'hld_time': None,
        'res': None,
        'res_time': None,
        'xtr': None,
        'xtr_time': None,
        'tof': None,
        'tof_time': None,
        'txi': None,
        'ttl': None,
        'ttl_time': None,
        'trk': None,
        'wind': None,
        't': None,
        'mxsh': None,
        'tas': None,
    }

    # Parse PLAN line
    for line in lines:
        if line.startswith('PLAN'):
            match = re.match(r'PLAN\s+(\d+)\s+([A-Z]{4})\s+TO\s+([A-Z]{4}).*?([A-Z]+)\s+(\d{2}/\d{2}/\d{2})', line)
            if match:
                data['plan_no'] = match.group(1)
                data['dep_icao'] = match.group(2)
                data['dst_icao'] = match.group(3)
                data['flight_rule'] = match.group(4)
                flight_date_str = match.group(5)
                data['flight_date'] = datetime.strptime(flight_date_str, '%m/%d/%y').date()
            break
    new_data = 0
    # Parse NONSTOP line
    for line in lines:
        if 'NONSTOP' in line:
            computed_match = re.search(r'COMPUTED\s+(\d{4})Z', line)
            if computed_match:
                computed_time_str = computed_match.group(1)
                computed_time = datetime.strptime(computed_time_str, '%H%M').time()
                # data['computed_time'] = datetime.combine(data['flight_date'], computed_time)
                data['computed_time'] = computed_time

            etd_match = re.search(r'ETD\s+(\d{4})Z', line)
            if etd_match:
                etd_str = etd_match.group(1)
                etd_time = datetime.strptime(etd_str, '%H%M').time()
                data['etd'] = datetime.combine(data['flight_date'], etd_time)

            progs_match = re.search(r'PROGS\s+(\S+)\s+(\S+)\s+(\S+)', line)
            if progs_match:
                data['progs'] = progs_match.group(1)
                data['act_no'] = progs_match.group(2)
                data['weight_unit'] = progs_match.group(3)
            break

    for line in lines:
        if line.startswith('DST'):
            parts = line.split()
            data['fly_fuel'] = int(parts[2])
            fly_time_parts = parts[3].split('/')
            data['fly_time'] = int(fly_time_parts[0]) * 60 + int(fly_time_parts[1])
            data['fly_dist'] = int(parts[4])

            # 处理ETA时间
            eta_str = parts[5].strip('Z')
            eta_time = datetime.strptime(eta_str, '%H%M').time()
            eta_date = data['flight_date']  # 初始日期为航班日期

            # 检查ETD是否存在并比较时间
            if 'etd' in data:
                # print(data['etd'])
                etd_time = data['etd'].time()  # data['etd']是datetime对象
                if etd_time > eta_time:
                    eta_date += timedelta(days=1)  # ETD时间晚，ETA日期加一天
            new_data = eta_date
            data['eta'] = datetime.combine(eta_date, eta_time)

            data['actual_txw'] = int(parts[6])
            data['actual_tow'] = int(parts[7])
            data['actual_lw'] = int(parts[8])
            data['actual_zfw'] = int(parts[9])
            data['actual_av_pld'] = int(parts[10])
            data['actual_opnlwt'] = int(parts[11].rstrip('A'))
            break

    # Parse ALT lines
    alt_lines = [line for line in lines if line.startswith('ALT ')]
    if alt_lines:
        first_alt = alt_lines[0].split()
        data['first_alt_icao'] = first_alt[1]
        data['first_alt_fuel'] = int(first_alt[2])
        first_alt_time_parts = first_alt[3].split('/')
        data['first_alt_time'] = int(first_alt_time_parts[0]) * 60 + int(first_alt_time_parts[1])
        data['first_alt_dist'] = int(first_alt[4])
        first_alt_eta_time = datetime.strptime(first_alt[5].strip('Z'), '%H%M').time()

        first_alt_eta_date = data['flight_date']  # 初始日期为航班日期
        etd_time = data['etd'].time()  # data['etd']是datetime对象
        if etd_time > first_alt_eta_time:
            first_alt_eta_date += timedelta(days=1)  # ETD时间晚，ETA日期加一天

        data['first_alt_eta'] = datetime.combine(first_alt_eta_date, first_alt_eta_time)

        data['max_txw'] = int(first_alt[6])
        data['max_tow'] = int(first_alt[7])
        data['max_lw'] = int(first_alt[8])
        data['max_zfw'] = int(first_alt[9])
        data['max_av_pld'] = int(first_alt[10])
        data['max_opnlwt'] = int(first_alt[11].rstrip('M'))

    if len(alt_lines) > 1:
        if alt_lines[1].startswith('ALT  NIL'):
            data['second_alt_icao'] = 'NIL'
            # data['second_alt_fuel'] = 0
        else:
            second_alt = alt_lines[1].split()
            data['second_alt_icao'] = second_alt[1]
            data['second_alt_fuel'] = int(second_alt[2])
            second_alt_time_parts = second_alt[3].split('/')
            data['second_alt_time'] = int(second_alt_time_parts[0]) * 60 + int(second_alt_time_parts[1])
            data['second_alt_dist'] = int(second_alt[4])
            second_alt_eta_time = datetime.strptime(second_alt[5].strip('Z'), '%H%M').time()
            second_alt_eta_date = data['flight_date']  # 初始日期为航班日期
            etd_time = data['etd'].time()  # data['etd']是datetime对象
            if etd_time > second_alt_eta_time:
                second_alt_eta_date += timedelta(days=1)  # ETD时间晚，ETA日期加一天

            data['second_alt_eta'] = datetime.combine(second_alt_eta_date, second_alt_eta_time)

    # Parse fuel sections
    for line in lines:
        if line.startswith('HLD       0'):
            parts = line.split()
            data['hld'] = int(parts[1])
            hld_time = parts[2].split('/')
            data['hld_time'] = int(hld_time[0]) * 60 + int(hld_time[1])
            # data['hld_time'] = int(parts[2].split('/')[1])
        elif line.startswith('RES'):
            parts = line.split()
            data['res'] = int(parts[1])
            res_time = parts[2].split('/')
            data['res_time'] = int(res_time[0]) * 60 + int(res_time[1])
            # data['res_time'] = int(parts[2].split('/')[1])
        elif line.startswith('XTR'):
            parts = line.split()
            data['xtr'] = int(parts[1])
            xtr_time = parts[2].split('/')
            data['xtr_time'] = int(xtr_time[0]) * 60 + int(xtr_time[1])
            # data['xtr_time'] = int(parts[2].split('/')[1])
        elif line.startswith('TOF'):
            parts = line.split()
            data['tof'] = int(parts[1])
            tof_time = parts[2].split('/')
            data['tof_time'] = int(tof_time[0]) * 60 + int(tof_time[1])
        elif line.startswith('TXI'):
            parts = line.split()
            data['txi'] = int(parts[1])
        elif line.startswith('TTL'):
            parts = line.split()
            data['ttl'] = int(parts[1])
            ttl_time = parts[2].split('/')
            data['ttl_time'] = int(ttl_time[0]) * 60 + int(ttl_time[1])

    # Parse TRK section
    trk_lines = []
    in_trk = False
    for line in lines:
        if line.startswith('TRK'):
            in_trk = True
            trk_part = line.split(': ', 1)[-1].strip()
            # trk_part = line
            trk_lines.append(trk_part)
        elif in_trk:
            if line.strip() == '':
                in_trk = False
            else:
                trk_lines.append(line.strip())
    # print(trk_lines)
    result = []
    for line in trk_lines:
        if line.startswith('WIND'):
            break
        result.append(line)
    data['trk'] = ' '.join(result)

    # if trk_lines[1].startswith('WIND'):
    #     data['trk'] = f"{trk_lines[0]}"
    # else:
    #     data['trk'] = f"{trk_lines[0]} {trk_lines[1]}"

    for line in lines:
        if 'WIND M' in line or 'WIND P' in line:
            # if 'WIND' in line and 'T P' in line and 'MXSH' in line:
            wind_match = re.search(r'WIND\s+([A-Z]\d+)\s+T\s+([A-Z]\d+)\s+MXSH\s+(\d+)', line)
            if wind_match:
                wind_str = wind_match.group(1)
                wind_speed = int(wind_str[1:])
                data['wind'] = -wind_speed if wind_str[0] == 'M' else wind_speed

                temp_str = wind_match.group(2)
                temp = int(temp_str[1:])
                data['t'] = temp if temp_str[0] == 'P' else -temp
                data['mxsh'] = int(wind_match.group(3))

        # if 'TAS' in line:
        if line.startswith('TAS '):
            tas_match = re.search(r'TAS\s+(\d+)', line)
            if tas_match:
                data['tas'] = int(tas_match.group(1))  # Assuming TAS in kt

        if 'FL ' in line and line.startswith('TAS '):
            fl_index = line.find('FL')
            # print(line)
            if fl_index != -1:
                # 提取 FL 之后的子字符串
                substring = line[fl_index + len('FL'):]
                # all_numbers = re.findall(r'\d+', substring)
                all_numbers = re.findall(r'(?<!/[A-Za-z])\d+', substring)
                int_numbers = [int(num) for num in all_numbers]
                result = max(int_numbers)
                data['max_fl'] = result * 100
    return pd.DataFrame([data])


def get_cfp_content(foc_flight_id):
    sql = f'SELECT cfp_content from dispatch_cfp_text WHERE foc_flight_id = {foc_flight_id}'
    return query_sql(sql)[0][0]


def get_kf_data(foc_flight_id):
    sql = f'''
SELECT
    plan_no,
    dep_icao,
    dst_icao,
    flight_rule,
    flight_date,
    computed_time,
    etd,
    progs,
    act_no,
    weight_unit,
    fly_fuel,
    fly_time,
    fly_dist,
    eta,
    actual_txw,
    actual_tow,
    actual_lw,
    actual_zfw,
    actual_av_pld,
    actual_opnlwt,
    first_alt_icao,
    first_alt_fuel,
    first_alt_time,
    first_alt_dist,
    first_alt_eta,
    second_alt_icao,
    second_alt_fuel,
    second_alt_time,
    second_alt_dist,
    second_alt_eta,
    max_txw,
    max_tow,
    max_lw,
    max_zfw,
    max_av_pld,
    max_opnlwt,
    hld,
    hld_time,
    res,
    res_time,
    xtr,
    xtr_time,
    tof,
    tof_time,
    txi,
    ttl,
    ttl_time,
    trk,
    wind,
    t,
    mxsh,
    tas,
    max_fl 
FROM
    dispatch_parse_cfp 
WHERE
    foc_flight_id = {foc_flight_id}
    '''
    data = query_sql(sql)
    if data is not None:
        df = pd.DataFrame(data)
        try:
            df['computed_time'] = df['computed_time'].astype(str).str.split(' ').str[-1]
            df['computed_time'] = pd.to_datetime(df['computed_time'], format='%H:%M:%S').dt.time
            return df
        except Exception as e:
            print(f"处理 computed_time 列时出现异常: {e}")
            return False
    return False


def compare_data(df_kf_data, df_parse_data):
    if df_kf_data is not False:
        if not df_kf_data.columns.equals(df_parse_data.columns):
            diff_cols = df_kf_data.columns.symmetric_difference(df_parse_data.columns)
            print(f"列名不一致，差异列：{diff_cols.tolist()}")
            return False

        # 检查行数是否一致
        if len(df_kf_data) != len(df_parse_data):
            print(f"行数不一致：df1有{len(df_kf_data)}行，df2有{len(df_parse_data)}行")
            return False

        # 对齐索引（如果索引不重要可以重置）
        df1 = df_kf_data.reset_index(drop=True)
        df2 = df_parse_data.reset_index(drop=True)

        # 处理NaN并比较
        mask = (df1.fillna('NaN') != df2.fillna('NaN'))
        ne_stacked = mask.stack()
        changed = ne_stacked[ne_stacked]

        if changed.empty:
            # print("两个DataFrame完全相同")
            return

        print("发现以下差异：")
        for index in changed.index:
            row_idx, col = index
            val1 = df1.at[row_idx, col]
            val2 = df2.at[row_idx, col]
            print(f"行 {row_idx}, 列 '{col}':")
            print(f"  df_kf_data: {val1}")
            print(f"  df_parse_data: {val2}\n")
        return False


def get_all_flight_id():
    sql = "SELECT foc_flight_id from dispatch_parse_cfp where flight_date <= '2024-08-26' and deleted = 0"
    return query_sql(sql)


if __name__ == '__main__':
    # foc_flight_id = "1342270"
    # 1437217
    # 1437426
    foc_flight_id = "1342270"
    cfp_text = get_cfp_content(foc_flight_id)
    print(cfp_text)
    df_kf_data = get_kf_data(foc_flight_id)
    df_parse_data = parse_cfp(cfp_text)
    # print(df_parse_data.to_string())
    # print(df_kf_data.to_string())
    compare_data(df_kf_data, df_parse_data)

    # a = get_all_flight_id()
    # print(len(a))
    # for x in a:
    #     foc_flight_id = x[0]
    #
    #     cfp_text = get_cfp_content(foc_flight_id)
    #     df_kf_data = get_kf_data(foc_flight_id)
    #     df_parse_data = parse_cfp(cfp_text)
    #     b = compare_data(df_kf_data, df_parse_data)
    #     if b is False:
    #         print(foc_flight_id)
