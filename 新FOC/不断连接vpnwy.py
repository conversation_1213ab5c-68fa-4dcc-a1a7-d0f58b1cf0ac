# -*- coding: utf-8 -*-
import datetime
import time
import random
import tkinter as tk
from threading import Thread, Event
import tkinter.messagebox
import requests

# 创建停止事件
stop_event = Event()
start_time = None


def update_timer():
    if start_time and not stop_event.is_set():
        elapsed_time = time.time() - start_time
        hours = int(elapsed_time // 3600)
        minutes = int((elapsed_time % 3600) // 60)
        seconds = int(elapsed_time % 60)
        timer_label.config(text=f"连接时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        root.after(1000, update_timer)
    elif stop_event.is_set():
        timer_label.config(text="连接时间: 00:00:00")


def get_g5air():
    url = "https://www.g5air.com/"
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        # 添加超时设置为5秒
        response = requests.get(url, headers=headers, timeout=5)
        # 检查状态码
        if response.status_code == 200:
            print(response)
            return response
        return None
    except requests.RequestException as e:
        print(f"请求发生错误: {e}")
        return None
    except Exception as e:
        print(f"解析网页时发生错误: {e}")
        return None


def run_query():
    while not stop_event.is_set():
        try:
            sj = get_g5air()
            # 只在按钮为"结束"状态（即开始状态）时才显示未连接提示
            if sj is None and button.text == "结束":
                root.after(0, lambda: tkinter.messagebox.showwarning("警告", "当前未连接VPN，请检查网络连接"))
                root.after(0, lambda: toggle_action())
                return  # 结束线程
            print(f"查询结果: {sj}")
            current_date = datetime.datetime.now()
            print(f"当前时间: {current_date}")
            sleep_time = random.randint(60, 120)
            time.sleep(sleep_time)
        except Exception as e:
            root.after(0, lambda: tkinter.messagebox.showerror("错误", f"查询发生错误: {str(e)}"))


# 自定义圆角按钮样式
class RoundedButton(tk.Canvas):
    def __init__(self, parent, width, height, text, command, corner_radius=10, padding=0):
        super().__init__(parent, width=width, height=height, bg='white', highlightthickness=0)
        self.command = command
        self._text = text
        self._state = "normal"
        self.width = width
        self.height = height

        # 创建圆角矩形
        self.rect_id = self.create_rounded_rect(padding, padding, width - padding * 2, height - padding * 2,
                                                corner_radius, fill='#4CAF50')
        # 创建文本
        self.text_id = self.create_text(width / 2, height / 2, text=text, fill='white', font=('Arial', 12))

        # 绑定更多事件以确保点击能被捕获
        self.bind('<Button-1>', self._on_click)
        self.bind('<ButtonRelease-1>', self._on_click)
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)

        # 为整个按钮区域创建一个透明的矩形，确保点击能被捕获
        self.hitbox = self.create_rectangle(0, 0, width, height, fill='', outline='', tags="hitbox")
        self.tag_bind("hitbox", '<Button-1>', self._on_click)

    def _on_enter(self, event):
        self.configure(cursor='hand2')

    def _on_leave(self, event):
        self.configure(cursor='')

    def create_rounded_rect(self, x1, y1, x2, y2, radius, **kwargs):
        # 创建圆角矩形的路径
        points = [
            x1 + radius, y1,  # 左上角开始
            x2 - radius, y1,
            x2, y1,
            x2, y1 + radius,
            x2, y2 - radius,  # 右边
            x2, y2,
            x2 - radius, y2,  # 右下角
            x1 + radius, y2,  # 底边
            x1, y2,
            x1, y2 - radius,  # 左边
            x1, y1 + radius,
            x1, y1  # 回到起点
        ]
        return self.create_polygon(points, smooth=True, **kwargs)

    @property
    def text(self):
        return self._text

    @text.setter
    def text(self, value):
        self._text = value
        self.itemconfig(self.text_id, text=value)
        # 更新按钮颜色
        if value == "开始":
            self.itemconfig(self.rect_id, fill='#4CAF50')  # 绿色
        else:
            self.itemconfig(self.rect_id, fill='#f44336')  # 红色

    def _on_click(self, event):
        if self.command and self._state != "disabled":
            # 直接调用命令而不是使用after
            self.command()
            # 视觉反馈
            self.itemconfig(self.rect_id, stipple="gray25")
            self.after(100, lambda: self.itemconfig(self.rect_id, stipple=""))


def toggle_action():
    global start_time
    # 添加防抖动保护
    current_time = time.time()
    if hasattr(toggle_action, 'last_click_time') and current_time - toggle_action.last_click_time < 0.5:
        return  # 防止快速重复点击
    toggle_action.last_click_time = current_time

    # 禁用按钮，防止重复点击
    button._state = "disabled"

    if button.text == "开始":
        stop_event.clear()
        button.text = "结束"
        start_time = time.time()
        update_timer()
        thread = Thread(target=run_query)
        thread.daemon = True
        thread.start()
    else:
        stop_event.set()
        button.text = "开始"
        start_time = None

    # 强制更新UI
    root.update_idletasks()
    root.update()

    # 短暂延迟后重新启用按钮
    root.after(300, lambda: setattr(button, '_state', "normal"))


# 创建主窗口
root = tk.Tk()
root.title("VPN连接器")
root.geometry("300x150")
root.configure(bg='white')
root.resizable(False, False)

# 创建计时器标签
timer_label = tk.Label(root, text="连接时间: 00:00:00", font=('Arial', 12), bg='white')
timer_label.pack(pady=20)

# 创建圆角按钮
button = RoundedButton(root, width=200, height=40, text="开始", command=toggle_action)
button.pack(pady=20)


def center_window():
    # 获取屏幕尺寸
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # 计算窗口位置
    x = (screen_width - 300) // 2
    y = (screen_height - 150) // 2

    # 设置窗口位置
    root.geometry(f"300x150+{x}+{y}")

    # 强制更新窗口
    root.update_idletasks()
    root.update()
    root.deiconify()

    # 设置定期刷新事件循环的函数
    def keep_alive():
        try:
            # 强制刷新事件循环
            root.update_idletasks()
            # 每500毫秒执行一次，减少CPU使用
            root.after(500, keep_alive)
        except Exception:
            # 如果窗口已关闭，忽略异常
            pass

    # 启动keep_alive循环
    keep_alive()


if __name__ == '__main__':
    root.withdraw()  # 隐藏窗口直到初始化完成
    root.after(200, center_window)  # 增加延迟时间，确保窗口完全初始化
    root.mainloop()
