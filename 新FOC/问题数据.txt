/usr/local/bin/python3.11 /Users/<USER>/PycharmProjects/workPro/新FOC/cfp报文分解-多线程.py
需要处理的航班总数: 57211
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 23600
  df_parse_data: 22600

发现数据差异: 1426287
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 27600
  df_parse_data: 25600

发现数据差异: 1437217
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1342270
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1347953
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 27600
  df_parse_data: 26600

发现数据差异: 1352982
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1358720
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1360688
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1359941
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1362758
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1439459
行数不一致：df1有2行，df2有1行
发现数据差异: 1442624
行数不一致：df1有2行，df2有1行
发现数据差异: 1440334
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1370393
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1374063
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1374541
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1375287
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1377513
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1379065
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1379758
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1379229
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 20700
  df_parse_data: 19700

发现数据差异: 1390452
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1388547
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1389032
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1393398
行数不一致：df1有2行，df2有1行
发现数据差异: 1397231
行数不一致：df1有2行，df2有1行
发现数据差异: 1397597
行数不一致：df1有2行，df2有1行
发现数据差异: 1397247
行数不一致：df1有2行，df2有1行
发现数据差异: 1397110
行数不一致：df1有2行，df2有1行
发现数据差异: 1397351
行数不一致：df1有2行，df2有1行
发现数据差异: 1397476
行数不一致：df1有2行，df2有1行
发现数据差异: 1397590
行数不一致：df1有2行，df2有1行
发现数据差异: 1397126
行数不一致：df1有2行，df2有1行
发现数据差异: 1397350
行数不一致：df1有2行，df2有1行
发现数据差异: 1397596
行数不一致：df1有2行，df2有1行
发现数据差异: 1397118
行数不一致：df1有2行，df2有1行
发现数据差异: 1397112
行数不一致：df1有2行，df2有1行
发现数据差异: 1397128
行数不一致：df1有2行，df2有1行
发现数据差异: 1397001
行数不一致：df1有2行，df2有1行
发现数据差异: 1397122
行数不一致：df1有2行，df2有1行
发现数据差异: 1397249
行数不一致：df1有2行，df2有1行
发现数据差异: 1397363
行数不一致：df1有2行，df2有1行
发现数据差异: 1397009
行数不一致：df1有2行，df2有1行
发现数据差异: 1397008
行数不一致：df1有2行，df2有1行
发现数据差异: 1397485
行数不一致：df1有2行，df2有1行
发现数据差异: 1397369
行数不一致：df1有2行，df2有1行
发现数据差异: 1397486
行数不一致：df1有2行，df2有1行
发现数据差异: 1397243
行数不一致：df1有2行，df2有1行
发现数据差异: 1397003
行数不一致：df1有2行，df2有1行
发现数据差异: 1397480
行数不一致：df1有2行，df2有1行
发现数据差异: 1397245
行数不一致：df1有2行，df2有1行
发现数据差异: 1397123
行数不一致：df1有2行，df2有1行
发现数据差异: 1397000
行数不一致：df1有2行，df2有1行
发现数据差异: 1397121
行数不一致：df1有2行，df2有1行
发现数据差异: 1397482
行数不一致：df1有2行，df2有1行
发现数据差异: 1397241
行数不一致：df1有2行，df2有1行
发现数据差异: 1397361
行数不一致：df1有2行，df2有1行
发现数据差异: 1397120
行数不一致：df1有2行，df2有1行
发现数据差异: 1397455
行数不一致：df1有2行，df2有1行
发现数据差异: 1397240
行数不一致：df1有2行，df2有1行
发现数据差异: 1397576
行数不一致：df1有2行，df2有1行
发现数据差异: 1397002
行数不一致：df1有2行，df2有1行
发现数据差异: 1397694
行数不一致：df1有2行，df2有1行
发现数据差异: 1397699
行数不一致：df1有2行，df2有1行
发现数据差异: 1397578
行数不一致：df1有2行，df2有1行
发现数据差异: 1397454
行数不一致：df1有2行，df2有1行
发现数据差异: 1397457
行数不一致：df1有2行，df2有1行
发现数据差异: 1397452
行数不一致：df1有2行，df2有1行
发现数据差异: 1397331
行数不一致：df1有2行，df2有1行
发现数据差异: 1397698
行数不一致：df1有2行，df2有1行
发现数据差异: 1397225
行数不一致：df1有2行，df2有1行
发现数据差异: 1397450
行数不一致：df1有2行，df2有1行
发现数据差异: 1397219
行数不一致：df1有2行，df2有1行
发现数据差异: 1397339
行数不一致：df1有2行，df2有1行
发现数据差异: 1397692
行数不一致：df1有2行，df2有1行
发现数据差异: 1397103
行数不一致：df1有2行，df2有1行
发现数据差异: 1397588
行数不一致：df1有2行，df2有1行
发现数据差异: 1397690
行数不一致：df1有2行，df2有1行
发现数据差异: 1397587
行数不一致：df1有2行，df2有1行
发现数据差异: 1397341
行数不一致：df1有2行，df2有1行
发现数据差异: 1397227
行数不一致：df1有2行，df2有1行
发现数据差异: 1397466
行数不一致：df1有2行，df2有1行
发现数据差异: 1397584
行数不一致：df1有2行，df2有1行
发现数据差异: 1397333
行数不一致：df1有2行，df2有1行
发现数据差异: 1397589
行数不一致：df1有2行，df2有1行
发现数据差异: 1397574
行数不一致：df1有2行，df2有1行
发现数据差异: 1397222
行数不一致：df1有2行，df2有1行
发现数据差异: 1397464
行数不一致：df1有2行，df2有1行
发现数据差异: 1397585
行数不一致：df1有2行，df2有1行
发现数据差异: 1397220
行数不一致：df1有2行，df2有1行
发现数据差异: 1397158
行数不一致：df1有2行，df2有1行
发现数据差异: 1397462
行数不一致：df1有2行，df2有1行
发现数据差异: 1397580
行数不一致：df1有2行，df2有1行
发现数据差异: 1397229
行数不一致：df1有2行，df2有1行
发现数据差异: 1397586
行数不一致：df1有2行，df2有1行
发现数据差异: 1397159
行数不一致：df1有2行，df2有1行
发现数据差异: 1397108
行数不一致：df1有2行，df2有1行
发现数据差异: 1397155
行数不一致：df1有2行，df2有1行
发现数据差异: 1397460
行数不一致：df1有2行，df2有1行
发现数据差异: 1397279
行数不一致：df1有2行，df2有1行
发现数据差异: 1397277
行数不一致：df1有2行，df2有1行
发现数据差异: 1397101
行数不一致：df1有2行，df2有1行
发现数据差异: 1397109
行数不一致：df1有2行，df2有1行
发现数据差异: 1397396
行数不一致：df1有2行，df2有1行
发现数据差异: 1397151
行数不一致：df1有2行，df2有1行
发现数据差异: 1397392
行数不一致：df1有2行，df2有1行
发现数据差异: 1397394
行数不一致：df1有2行，df2有1行
发现数据差异: 1397390
行数不一致：df1有2行，df2有1行
发现数据差异: 1397286
行数不一致：df1有2行，df2有1行
发现数据差异: 1397165
行数不一致：df1有2行，df2有1行
发现数据差异: 1397166
行数不一致：df1有2行，df2有1行
发现数据差异: 1397167
行数不一致：df1有2行，df2有1行
发现数据差异: 1397168
行数不一致：df1有2行，df2有1行
发现数据差异: 1397047
行数不一致：df1有2行，df2有1行
发现数据差异: 1397287
行数不一致：df1有2行，df2有1行
发现数据差异: 1397162
行数不一致：df1有2行，df2有1行
发现数据差异: 1397153
行数不一致：df1有2行，df2有1行
发现数据差异: 1397284
行数不一致：df1有2行，df2有1行
发现数据差异: 1397045
行数不一致：df1有2行，df2有1行
发现数据差异: 1397283
行数不一致：df1有2行，df2有1行
发现数据差异: 1397110
行数不一致：df1有2行，df2有1行
发现数据差异: 1397281
行数不一致：df1有2行，df2有1行
发现数据差异: 1397161
行数不一致：df1有2行，df2有1行
发现数据差异: 1397049
行数不一致：df1有2行，df2有1行
发现数据差异: 1397118
行数不一致：df1有2行，df2有1行
发现数据差异: 1397112
行数不一致：df1有2行，df2有1行
发现数据差异: 1397596
行数不一致：df1有2行，df2有1行
发现数据差异: 1397597
行数不一致：df1有2行，df2有1行
发现数据差异: 1397043
行数不一致：df1有2行，df2有1行
发现数据差异: 1397285
行数不一致：df1有2行，df2有1行
发现数据差异: 1397249
行数不一致：df1有2行，df2有1行
发现数据差异: 1397128
行数不一致：df1有2行，df2有1行
发现数据差异: 1397476
行数不一致：df1有2行，df2有1行
发现数据差异: 1397369
行数不一致：df1有2行，df2有1行
发现数据差异: 1397126
行数不一致：df1有2行，df2有1行
发现数据差异: 1397590
行数不一致：df1有2行，df2有1行
发现数据差异: 1397122
行数不一致：df1有2行，df2有1行
发现数据差异: 1397363
行数不一致：df1有2行，df2有1行
发现数据差异: 1397231
行数不一致：df1有2行，df2有1行
发现数据差异: 1397485
行数不一致：df1有2行，df2有1行
发现数据差异: 1397247
行数不一致：df1有2行，df2有1行
发现数据差异: 1397243
行数不一致：df1有2行，df2有1行
发现数据差异: 1397351
行数不一致：df1有2行，df2有1行
发现数据差异: 1397486
行数不一致：df1有2行，df2有1行
发现数据差异: 1397121
行数不一致：df1有2行，df2有1行
发现数据差异: 1397245
行数不一致：df1有2行，df2有1行
发现数据差异: 1397000
行数不一致：df1有2行，df2有1行
发现数据差异: 1397003
行数不一致：df1有2行，df2有1行
发现数据差异: 1397002
行数不一致：df1有2行，df2有1行
发现数据差异: 1397123
行数不一致：df1有2行，df2有1行
发现数据差异: 1397001
行数不一致：df1有2行，df2有1行
发现数据差异: 1397350
行数不一致：df1有2行，df2有1行行数不一致：df1有2行，df2有1行
发现数据差异: 1397240

发现数据差异: 1397009
行数不一致：df1有2行，df2有1行
发现数据差异: 1397008
行数不一致：df1有2行，df2有1行
发现数据差异: 1397576
行数不一致：df1有2行，df2有1行
发现数据差异: 1397698
行数不一致：df1有2行，df2有1行
发现数据差异: 1397120
行数不一致：df1有2行，df2有1行
发现数据差异: 1397361
行数不一致：df1有2行，df2有1行
发现数据差异: 1397452
行数不一致：df1有2行，df2有1行
发现数据差异: 1397699
行数不一致：df1有2行，df2有1行
发现数据差异: 1397241
行数不一致：df1有2行，df2有1行
发现数据差异: 1397482
行数不一致：df1有2行，df2有1行
发现数据差异: 1397333
行数不一致：df1有2行，df2有1行
发现数据差异: 1397690
行数不一致：df1有2行，df2有1行
发现数据差异: 1397331
行数不一致：df1有2行，df2有1行
发现数据差异: 1397339
行数不一致：df1有2行，df2有1行
发现数据差异: 1397455行数不一致：df1有2行，df2有1行
发现数据差异: 1397480

行数不一致：df1有2行，df2有1行
发现数据差异: 1397694
行数不一致：df1有2行，df2有1行
发现数据差异: 1397457
行数不一致：df1有2行，df2有1行
发现数据差异: 1397578
行数不一致：df1有2行，df2有1行
发现数据差异: 1397454
行数不一致：df1有2行，df2有1行
发现数据差异: 1397450
行数不一致：df1有2行，df2有1行
发现数据差异: 1397692
行数不一致：df1有2行，df2有1行
发现数据差异: 1397574
行数不一致：df1有2行，df2有1行
发现数据差异: 1397587
行数不一致：df1有2行，df2有1行
发现数据差异: 1397589
行数不一致：df1有2行，df2有1行
发现数据差异: 1397219
行数不一致：df1有2行，df2有1行
发现数据差异: 1397225
行数不一致：df1有2行，df2有1行
发现数据差异: 1397466
行数不一致：df1有2行，df2有1行
发现数据差异: 1397227
行数不一致：df1有2行，df2有1行
发现数据差异: 1397103
行数不一致：df1有2行，df2有1行
发现数据差异: 1397101
行数不一致：df1有2行，df2有1行
发现数据差异: 1397584
行数不一致：df1有2行，df2有1行
发现数据差异: 1397588
行数不一致：df1有2行，df2有1行
发现数据差异: 1397220
行数不一致：df1有2行，df2有1行
发现数据差异: 1397229
行数不一致：df1有2行，df2有1行
发现数据差异: 1397585
行数不一致：df1有2行，df2有1行
发现数据差异: 1397222
行数不一致：df1有2行，df2有1行
发现数据差异: 1397462
行数不一致：df1有2行，df2有1行
发现数据差异: 1397341
行数不一致：df1有2行，df2有1行
发现数据差异: 1397109
行数不一致：df1有2行，df2有1行
发现数据差异: 1397464
行数不一致：df1有2行，df2有1行
发现数据差异: 1397158
行数不一致：df1有2行，df2有1行
发现数据差异: 1397586
行数不一致：df1有2行，df2有1行
发现数据差异: 1397580
行数不一致：df1有2行，df2有1行
发现数据差异: 1397108
行数不一致：df1有2行，df2有1行
发现数据差异: 1397155
行数不一致：df1有2行，df2有1行
发现数据差异: 1397159
行数不一致：df1有2行，df2有1行
发现数据差异: 1397151
行数不一致：df1有2行，df2有1行
发现数据差异: 1397460
行数不一致：df1有2行，df2有1行
发现数据差异: 1397396
行数不一致：df1有2行，df2有1行
发现数据差异: 1397277
行数不一致：df1有2行，df2有1行
发现数据差异: 1397153
行数不一致：df1有2行，df2有1行
发现数据差异: 1397049
行数不一致：df1有2行，df2有1行
发现数据差异: 1397279
行数不一致：df1有2行，df2有1行
发现数据差异: 1397166
行数不一致：df1有2行，df2有1行
发现数据差异: 1397287
行数不一致：df1有2行，df2有1行
发现数据差异: 1397390
行数不一致：df1有2行，df2有1行
发现数据差异: 1397047
行数不一致：df1有2行，df2有1行
发现数据差异: 1397392
行数不一致：df1有2行，df2有1行
发现数据差异: 1397045
行数不一致：df1有2行，df2有1行
发现数据差异: 1397165
行数不一致：df1有2行，df2有1行
发现数据差异: 1397394
行数不一致：df1有2行，df2有1行
发现数据差异: 1397043
行数不一致：df1有2行，df2有1行
发现数据差异: 1397168
行数不一致：df1有2行，df2有1行
发现数据差异: 1397283
行数不一致：df1有2行，df2有1行
发现数据差异: 1397284
行数不一致：df1有2行，df2有1行
发现数据差异: 1397167
行数不一致：df1有2行，df2有1行
发现数据差异: 1397285
行数不一致：df1有2行，df2有1行
发现数据差异: 1397162
行数不一致：df1有2行，df2有1行
发现数据差异: 1397281
行数不一致：df1有2行，df2有1行
发现数据差异: 1397379
行数不一致：df1有2行，df2有1行
发现数据差异: 1397161
行数不一致：df1有2行，df2有1行
发现数据差异: 1397160
行数不一致：df1有2行，df2有1行
发现数据差异: 1397017
行数不一致：df1有2行，df2有1行
发现数据差异: 1397286
行数不一致：df1有2行，df2有1行
发现数据差异: 1397378
行数不一致：df1有2行，df2有1行
发现数据差异: 1397375
行数不一致：df1有2行，df2有1行
发现数据差异: 1397139
行数不一致：df1有2行，df2有1行
发现数据差异: 1397011
行数不一致：df1有2行，df2有1行
发现数据差异: 1397373
行数不一致：df1有2行，df2有1行
发现数据差异: 1397133
行数不一致：df1有2行，df2有1行
发现数据差异: 1397018
行数不一致：df1有2行，df2有1行
发现数据差异: 1397376
行数不一致：df1有2行，df2有1行
发现数据差异: 1397491
行数不一致：df1有2行，df2有1行
发现数据差异: 1397377
行数不一致：df1有2行，df2有1行
发现数据差异: 1397371
行数不一致：df1有2行，df2有1行
发现数据差异: 1397019
行数不一致：df1有2行，df2有1行
发现数据差异: 1397496
行数不一致：df1有2行，df2有1行
发现数据差异: 1397010
行数不一致：df1有2行，df2有1行
发现数据差异: 1397016
行数不一致：df1有2行，df2有1行
发现数据差异: 1397268
行数不一致：df1有2行，df2有1行
发现数据差异: 1397494
行数不一致：df1有2行，df2有1行
发现数据差异: 1397022
行数不一致：df1有2行，df2有1行
发现数据差异: 1397495
行数不一致：df1有2行，df2有1行
发现数据差异: 1397147
行数不一致：df1有2行，df2有1行
发现数据差异: 1397382
行数不一致：df1有2行，df2有1行
发现数据差异: 1397023
行数不一致：df1有2行，df2有1行
发现数据差异: 1397386
行数不一致：df1有2行，df2有1行
发现数据差异: 1397266
行数不一致：df1有2行，df2有1行
发现数据差异: 1397384
行数不一致：df1有2行，df2有1行
发现数据差异: 1397144
行数不一致：df1有2行，df2有1行
发现数据差异: 1397149
行数不一致：df1有2行，df2有1行
发现数据差异: 1397264
行数不一致：df1有2行，df2有1行
发现数据差异: 1397381
行数不一致：df1有2行，df2有1行
发现数据差异: 1397141
行数不一致：df1有2行，df2有1行
发现数据差异: 1397020
行数不一致：df1有2行，df2有1行
发现数据差异: 1397145
行数不一致：df1有2行，df2有1行
发现数据差异: 1397025
行数不一致：df1有2行，df2有1行
发现数据差异: 1397079
行数不一致：df1有2行，df2有1行
发现数据差异: 1397089
行数不一致：df1有2行，df2有1行
发现数据差异: 1397021
行数不一致：df1有2行，df2有1行
发现数据差异: 1397262
行数不一致：df1有2行，df2有1行
发现数据差异: 1397388
行数不一致：df1有2行，df2有1行
发现数据差异: 1397088
行数不一致：df1有2行，df2有1行
发现数据差异: 1397078
行数不一致：df1有2行，df2有1行
发现数据差异: 1397059
行数不一致：df1有2行，df2有1行
发现数据差异: 1397056
行数不一致：df1有2行，df2有1行
发现数据差异: 1397178
行数不一致：df1有2行，df2有1行
发现数据差异: 1397294
行数不一致：df1有2行，df2有1行
发现数据差异: 1397054
行数不一致：df1有2行，df2有1行
发现数据差异: 1397080
行数不一致：df1有2行，df2有1行
发现数据差异: 1397293
行数不一致：df1有2行，df2有1行
发现数据差异: 1397380
行数不一致：df1有2行，df2有1行
发现数据差异: 1397086
行数不一致：df1有2行，df2有1行
发现数据差异: 1397171
行数不一致：df1有2行，df2有1行
发现数据差异: 1397050
行数不一致：df1有2行，df2有1行
发现数据差异: 1397087
行数不一致：df1有2行，df2有1行
发现数据差异: 1397291
行数不一致：df1有2行，df2有1行
发现数据差异: 1397172
行数不一致：df1有2行，df2有1行
发现数据差异: 1397081
行数不一致：df1有2行，df2有1行
发现数据差异: 1397290
行数不一致：df1有2行，df2有1行
发现数据差异: 1397292
行数不一致：df1有2行，df2有1行
发现数据差异: 1397052
行数不一致：df1有2行，df2有1行
发现数据差异: 1397058
行数不一致：df1有2行，df2有1行
发现数据差异: 1397170
行数不一致：df1有2行，df2有1行
发现数据差异: 1397295
行数不一致：df1有2行，df2有1行
发现数据差异: 1397066
行数不一致：df1有2行，df2有1行
发现数据差异: 1397062
行数不一致：df1有2行，df2有1行
发现数据差异: 1397182
行数不一致：df1有2行，df2有1行
发现数据差异: 1397186
行数不一致：df1有2行，df2有1行
发现数据差异: 1396940
行数不一致：df1有2行，df2有1行
发现数据差异: 1397188
行数不一致：df1有2行，df2有1行
发现数据差异: 1397180
行数不一致：df1有2行，df2有1行
发现数据差异: 1397065
行数不一致：df1有2行，df2有1行
发现数据差异: 1397063
行数不一致：df1有2行，df2有1行
发现数据差异: 1397064
行数不一致：df1有2行，df2有1行
发现数据差异: 1397068
行数不一致：df1有2行，df2有1行
发现数据差异: 1404098
行数不一致：df1有2行，df2有1行
发现数据差异: 1397098
行数不一致：df1有2行，df2有1行
发现数据差异: 1396943
行数不一致：df1有2行，df2有1行
发现数据差异: 1397184
行数不一致：df1有2行，df2有1行
发现数据差异: 1396946
行数不一致：df1有2行，df2有1行
发现数据差异: 1396948
行数不一致：df1有2行，df2有1行
发现数据差异: 1396942
行数不一致：df1有2行，df2有1行
发现数据差异: 1397090
行数不一致：df1有2行，df2有1行
发现数据差异: 1397099
行数不一致：df1有2行，df2有1行
发现数据差异: 1396950
行数不一致：df1有2行，df2有1行
发现数据差异: 1396944
行数不一致：df1有2行，df2有1行
发现数据差异: 1410427
行数不一致：df1有2行，df2有1行
发现数据差异: 1410425
行数不一致：df1有2行，df2有1行
发现数据差异: 1410426
行数不一致：df1有2行，df2有1行
发现数据差异: 1396952
行数不一致：df1有2行，df2有1行
发现数据差异: 1410424
行数不一致：df1有2行，df2有1行
发现数据差异: 1397091
行数不一致：df1有2行，df2有1行
发现数据差异: 1396939
行数不一致：df1有2行，df2有1行
发现数据差异: 1397160
行数不一致：df1有2行，df2有1行
发现数据差异: 1396945
行数不一致：df1有2行，df2有1行
发现数据差异: 1396957
行数不一致：df1有2行，df2有1行
发现数据差异: 1397805
行数不一致：df1有2行，df2有1行
发现数据差异: 1404097
行数不一致：df1有2行，df2有1行
发现数据差异: 1397806
行数不一致：df1有2行，df2有1行
发现数据差异: 1397016
行数不一致：df1有2行，df2有1行
发现数据差异: 1396959
行数不一致：df1有2行，df2有1行
发现数据差异: 1397139
行数不一致：df1有2行，df2有1行
发现数据差异: 1397018
行数不一致：df1有2行，df2有1行
发现数据差异: 1397379
行数不一致：df1有2行，df2有1行
发现数据差异: 1397017
行数不一致：df1有2行，df2有1行
发现数据差异: 1397378
行数不一致：df1有2行，df2有1行
发现数据差异: 1397375
行数不一致：df1有2行，df2有1行
发现数据差异: 1397376
行数不一致：df1有2行，df2有1行
发现数据差异: 1396958
行数不一致：df1有2行，df2有1行
发现数据差异: 1397011
行数不一致：df1有2行，df2有1行
发现数据差异: 1397496
行数不一致：df1有2行，df2有1行
发现数据差异: 1397133
行数不一致：df1有2行，df2有1行
发现数据差异: 1397494
行数不一致：df1有2行，df2有1行
发现数据差异: 1397019
行数不一致：df1有2行，df2有1行
发现数据差异: 1397495
行数不一致：df1有2行，df2有1行
发现数据差异: 1397010
行数不一致：df1有2行，df2有1行
发现数据差异: 1397373
行数不一致：df1有2行，df2有1行
发现数据差异: 1397268
行数不一致：df1有2行，df2有1行
发现数据差异: 1397491
行数不一致：df1有2行，df2有1行
发现数据差异: 1397377
行数不一致：df1有2行，df2有1行
发现数据差异: 1397371行数不一致：df1有2行，df2有1行
发现数据差异: 1397149

行数不一致：df1有2行，df2有1行
发现数据差异: 1397386
行数不一致：df1有2行，df2有1行
发现数据差异: 1397147
行数不一致：df1有2行，df2有1行
发现数据差异: 1397266
行数不一致：df1有2行，df2有1行
发现数据差异: 1397023
行数不一致：df1有2行，df2有1行
发现数据差异: 1397388
行数不一致：df1有2行，df2有1行
发现数据差异: 1397144
行数不一致：df1有2行，df2有1行
发现数据差异: 1397264
行数不一致：df1有2行，df2有1行
发现数据差异: 1397022
行数不一致：df1有2行，df2有1行
发现数据差异: 1397025
行数不一致：df1有2行，df2有1行
发现数据差异: 1397384
行数不一致：df1有2行，df2有1行
发现数据差异: 1397380
行数不一致：df1有2行，df2有1行
发现数据差异: 1397021
行数不一致：df1有2行，df2有1行
发现数据差异: 1397381
行数不一致：df1有2行，df2有1行
发现数据差异: 1397382
行数不一致：df1有2行，df2有1行
发现数据差异: 1397088
行数不一致：df1有2行，df2有1行
发现数据差异: 1397262
行数不一致：df1有2行，df2有1行
发现数据差异: 1397020
行数不一致：df1有2行，df2有1行
发现数据差异: 1397145
行数不一致：df1有2行，df2有1行
发现数据差异: 1397141
行数不一致：df1有2行，df2有1行
发现数据差异: 1397081
行数不一致：df1有2行，df2有1行
发现数据差异: 1397079
行数不一致：df1有2行，df2有1行
发现数据差异: 1397086
行数不一致：df1有2行，df2有1行
发现数据差异: 1397089
行数不一致：df1有2行，df2有1行
发现数据差异: 1397058
行数不一致：df1有2行，df2有1行
发现数据差异: 1397056
行数不一致：df1有2行，df2有1行
发现数据差异: 1397078
行数不一致：df1有2行，df2有1行
发现数据差异: 1397178
行数不一致：df1有2行，df2有1行
发现数据差异: 1397059
行数不一致：df1有2行，df2有1行
发现数据差异: 1397052
行数不一致：df1有2行，df2有1行
发现数据差异: 1397080
行数不一致：df1有2行，df2有1行
发现数据差异: 1397050
行数不一致：df1有2行，df2有1行
发现数据差异: 1397295
行数不一致：df1有2行，df2有1行
发现数据差异: 1397293
行数不一致：df1有2行，df2有1行
发现数据差异: 1397087
行数不一致：df1有2行，df2有1行
发现数据差异: 1397292
行数不一致：df1有2行，df2有1行
发现数据差异: 1397290
行数不一致：df1有2行，df2有1行
发现数据差异: 1397170
行数不一致：df1有2行，df2有1行
发现数据差异: 1397294
行数不一致：df1有2行，df2有1行
发现数据差异: 1397172
行数不一致：df1有2行，df2有1行
发现数据差异: 1397171
行数不一致：df1有2行，df2有1行
发现数据差异: 1397054
行数不一致：df1有2行，df2有1行
发现数据差异: 1397184
行数不一致：df1有2行，df2有1行
发现数据差异: 1397291
行数不一致：df1有2行，df2有1行
发现数据差异: 1397066
行数不一致：df1有2行，df2有1行
发现数据差异: 1397186
行数不一致：df1有2行，df2有1行
发现数据差异: 1397063
行数不一致：df1有2行，df2有1行
发现数据差异: 1397062
行数不一致：df1有2行，df2有1行
发现数据差异: 1397065
行数不一致：df1有2行，df2有1行
发现数据差异: 1397064
行数不一致：df1有2行，df2有1行
发现数据差异: 1397068
行数不一致：df1有2行，df2有1行
发现数据差异: 1397188
行数不一致：df1有2行，df2有1行
发现数据差异: 1396943
行数不一致：df1有2行，df2有1行
发现数据差异: 1397182
行数不一致：df1有2行，df2有1行
发现数据差异: 1397090
行数不一致：df1有2行，df2有1行
发现数据差异: 1397180
行数不一致：df1有2行，df2有1行
发现数据差异: 1396946
行数不一致：df1有2行，df2有1行
发现数据差异: 1397099
行数不一致：df1有2行，df2有1行
发现数据差异: 1397098
行数不一致：df1有2行，df2有1行
发现数据差异: 1396945
行数不一致：df1有2行，df2有1行
发现数据差异: 1404098
行数不一致：df1有2行，df2有1行
发现数据差异: 1396948
行数不一致：df1有2行，df2有1行
发现数据差异: 1404097
行数不一致：df1有2行，df2有1行
发现数据差异: 1396950
行数不一致：df1有2行，df2有1行
发现数据差异: 1410426
行数不一致：df1有2行，df2有1行
发现数据差异: 1397091
行数不一致：df1有2行，df2有1行
发现数据差异: 1396942
行数不一致：df1有2行，df2有1行
发现数据差异: 1396939
行数不一致：df1有2行，df2有1行
发现数据差异: 1396944
行数不一致：df1有2行，df2有1行
发现数据差异: 1410425
行数不一致：df1有2行，df2有1行
发现数据差异: 1410424
行数不一致：df1有2行，df2有1行
发现数据差异: 1396954
行数不一致：df1有2行，df2有1行
发现数据差异: 1396940
行数不一致：df1有2行，df2有1行
发现数据差异: 1397806
行数不一致：df1有2行，df2有1行
发现数据差异: 1396955
行数不一致：df1有2行，df2有1行
发现数据差异: 1397805
行数不一致：df1有2行，df2有1行
发现数据差异: 1410427
行数不一致：df1有2行，df2有1行
发现数据差异: 1396958
行数不一致：df1有2行，df2有1行
发现数据差异: 1396929
行数不一致：df1有2行，df2有1行
发现数据差异: 1396957
行数不一致：df1有2行，df2有1行
发现数据差异: 1396926
行数不一致：df1有2行，df2有1行
发现数据差异: 1396936
行数不一致：df1有2行，df2有1行
发现数据差异: 1396984
行数不一致：df1有2行，df2有1行
发现数据差异: 1396952
行数不一致：df1有2行，df2有1行
发现数据差异: 1396959
行数不一致：df1有2行，df2有1行
发现数据差异: 1396956
行数不一致：df1有2行，df2有1行
发现数据差异: 1396934
行数不一致：df1有2行，df2有1行
发现数据差异: 1397712
行数不一致：df1有2行，df2有1行
发现数据差异: 1397715
行数不一致：df1有2行，df2有1行
发现数据差异: 1396980
行数不一致：df1有2行，df2有1行
发现数据差异: 1396982
行数不一致：df1有2行，df2有1行
发现数据差异: 1397714
行数不一致：df1有2行，df2有1行
发现数据差异: 1396993
行数不一致：df1有2行，df2有1行
发现数据差异: 1396986
行数不一致：df1有2行，df2有1行
发现数据差异: 1396991
行数不一致：df1有2行，df2有1行
发现数据差异: 1397607
行数不一致：df1有2行，df2有1行
发现数据差异: 1396995
行数不一致：df1有2行，df2有1行
发现数据差异: 1397709
行数不一致：df1有2行，df2有1行
发现数据差异: 1397713
行数不一致：df1有2行，df2有1行
发现数据差异: 1397728
行数不一致：df1有2行，df2有1行
发现数据差异: 1397603
行数不一致：df1有2行，df2有1行
发现数据差异: 1397726行数不一致：df1有2行，df2有1行
发现数据差异: 1397600

行数不一致：df1有2行，df2有1行
发现数据差异: 1397727
行数不一致：df1有2行，df2有1行
发现数据差异: 1397606
行数不一致：df1有2行，df2有1行
发现数据差异: 1397601
行数不一致：df1有2行，df2有1行
发现数据差异: 1397605
行数不一致：df1有2行，df2有1行
发现数据差异: 1396997
行数不一致：df1有2行，df2有1行
发现数据差异: 1397609
行数不一致：df1有2行，df2有1行
发现数据差异: 1397602
行数不一致：df1有2行，df2有1行
发现数据差异: 1397604
行数不一致：df1有2行，df2有1行
发现数据差异: 1396968
行数不一致：df1有2行，df2有1行
发现数据差异: 1397725
行数不一致：df1有2行，df2有1行
发现数据差异: 1396962
行数不一致：df1有2行，df2有1行
发现数据差异: 1396966
行数不一致：df1有2行，df2有1行
发现数据差异: 1396965
行数不一致：df1有2行，df2有1行
发现数据差异: 1396964
行数不一致：df1有2行，df2有1行
发现数据差异: 1397810
行数不一致：df1有2行，df2有1行
发现数据差异: 1396963
行数不一致：df1有2行，df2有1行
发现数据差异: 1396973
行数不一致：df1有2行，df2有1行
发现数据差异: 1397700
行数不一致：df1有2行，df2有1行
发现数据差异: 1396979
行数不一致：df1有2行，df2有1行
发现数据差异: 1396975
行数不一致：df1有2行，df2有1行
发现数据差异: 1396976
行数不一致：df1有2行，df2有1行
发现数据差异: 1396971
行数不一致：df1有2行，df2有1行
发现数据差异: 1397809
行数不一致：df1有2行，df2有1行
发现数据差异: 1396977
行数不一致：df1有2行，df2有1行
发现数据差异: 1396974
行数不一致：df1有2行，df2有1行
发现数据差异: 1397701
行数不一致：df1有2行，df2有1行
发现数据差异: 1397752
行数不一致：df1有2行，df2有1行
发现数据差异: 1397753
行数不一致：df1有2行，df2有1行
发现数据差异: 1397755
行数不一致：df1有2行，df2有1行
发现数据差异: 1397708
行数不一致：df1有2行，df2有1行
发现数据差异: 1397645
行数不一致：df1有2行，df2有1行
发现数据差异: 1397522
行数不一致：df1有2行，df2有1行
发现数据差异: 1397520
行数不一致：df1有2行，df2有1行
发现数据差异: 1397754
行数不一致：df1有2行，df2有1行行数不一致：df1有2行，df2有1行
发现数据差异: 1397529

发现数据差异: 1397515
行数不一致：df1有2行，df2有1行
发现数据差异: 1397751
行数不一致：df1有2行，df2有1行
发现数据差异: 1397648
行数不一致：df1有2行，df2有1行
发现数据差异: 1396978
行数不一致：df1有2行，df2有1行
发现数据差异: 1397643
行数不一致：df1有2行，df2有1行
发现数据差异: 1397517
行数不一致：df1有2行，df2有1行
发现数据差异: 1397756
行数不一致：df1有2行，df2有1行
发现数据差异: 1397647
行数不一致：df1有2行，df2有1行
发现数据差异: 1397731
行数不一致：df1有2行，df2有1行
发现数据差异: 1397611
行数不一致：df1有2行，df2有1行
发现数据差异: 1397502
行数不一致：df1有2行，df2有1行
发现数据差异: 1397730
行数不一致：df1有2行，df2有1行
发现数据差异: 1397503
行数不一致：df1有2行，df2有1行
发现数据差异: 1397613
行数不一致：df1有2行，df2有1行
发现数据差异: 1397748
行数不一致：df1有2行，df2有1行
发现数据差异: 1397621
行数不一致：df1有2行，df2有1行
发现数据差异: 1397313
行数不一致：df1有2行，df2有1行
发现数据差异: 1397797
行数不一致：df1有2行，df2有1行
发现数据差异: 1397619
行数不一致：df1有2行，df2有1行
发现数据差异: 1397746
行数不一致：df1有2行，df2有1行
发现数据差异: 1397623
行数不一致：df1有2行，df2有1行
发现数据差异: 1397500
行数不一致：df1有2行，df2有1行
发现数据差异: 1397749
行数不一致：df1有2行，df2有1行
发现数据差异: 1397747
行数不一致：df1有2行，df2有1行
发现数据差异: 1397745
行数不一致：df1有2行，df2有1行
发现数据差异: 1397625
行数不一致：df1有2行，df2有1行
发现数据差异: 1397676
行数不一致：df1有2行，df2有1行
发现数据差异: 1397501
行数不一致：df1有2行，df2有1行
发现数据差异: 1397505
行数不一致：df1有2行，df2有1行
发现数据差异: 1397433
行数不一致：df1有2行，df2有1行
发现数据差异: 1397435
行数不一致：df1有2行，df2有1行
发现数据差异: 1397677
行数不一致：df1有2行，df2有1行
发现数据差异: 1397796
行数不一致：df1有2行，df2有1行
发现数据差异: 1396956
行数不一致：df1有2行，df2有1行
发现数据差异: 1397678
行数不一致：df1有2行，df2有1行
发现数据差异: 1397675
行数不一致：df1有2行，df2有1行
发现数据差异: 1396954
行数不一致：df1有2行，df2有1行
发现数据差异: 1397312
行数不一致：df1有2行，df2有1行
发现数据差异: 1397799
行数不一致：df1有2行，df2有1行
发现数据差异: 1397671
行数不一致：df1有2行，df2有1行
发现数据差异: 1396934
行数不一致：df1有2行，df2有1行
发现数据差异: 1397315
行数不一致：df1有2行，df2有1行
发现数据差异: 1396936
行数不一致：df1有2行，df2有1行
发现数据差异: 1396982
行数不一致：df1有2行，df2有1行
发现数据差异: 1396929
行数不一致：df1有2行，df2有1行
发现数据差异: 1397550
行数不一致：df1有2行，df2有1行
发现数据差异: 1396926
行数不一致：df1有2行，df2有1行
发现数据差异: 1396980
行数不一致：df1有2行，df2有1行
发现数据差异: 1397798
行数不一致：df1有2行，df2有1行
发现数据差异: 1397712
行数不一致：df1有2行，df2有1行
发现数据差异: 1396984
行数不一致：df1有2行，df2有1行
发现数据差异: 1397713
行数不一致：df1有2行，df2有1行
发现数据差异: 1396986
行数不一致：df1有2行，df2有1行
发现数据差异: 1396955
行数不一致：df1有2行，df2有1行
发现数据差异: 1397715
行数不一致：df1有2行，df2有1行
发现数据差异: 1396993
行数不一致：df1有2行，df2有1行
发现数据差异: 1397728
行数不一致：df1有2行，df2有1行
发现数据差异: 1396991
行数不一致：df1有2行，df2有1行
发现数据差异: 1397607
行数不一致：df1有2行，df2有1行
发现数据差异: 1397609
行数不一致：df1有2行，df2有1行
发现数据差异: 1396995
行数不一致：df1有2行，df2有1行
发现数据差异: 1397714
行数不一致：df1有2行，df2有1行
发现数据差异: 1397601
行数不一致：df1有2行，df2有1行
发现数据差异: 1397606
行数不一致：df1有2行，df2有1行
发现数据差异: 1397709
行数不一致：df1有2行，df2有1行
发现数据差异: 1397603
行数不一致：df1有2行，df2有1行
发现数据差异: 1397725
行数不一致：df1有2行，df2有1行
发现数据差异: 1397600
行数不一致：df1有2行，df2有1行
发现数据差异: 1397810
行数不一致：df1有2行，df2有1行
发现数据差异: 1397727
行数不一致：df1有2行，df2有1行
发现数据差异: 1397604
行数不一致：df1有2行，df2有1行
发现数据差异: 1397605
行数不一致：df1有2行，df2有1行
发现数据差异: 1396997
行数不一致：df1有2行，df2有1行
发现数据差异: 1397726
行数不一致：df1有2行，df2有1行
发现数据差异: 1396966
行数不一致：df1有2行，df2有1行
发现数据差异: 1396963
行数不一致：df1有2行，df2有1行
发现数据差异: 1396968
行数不一致：df1有2行，df2有1行
发现数据差异: 1396964
行数不一致：df1有2行，df2有1行
发现数据差异: 1396965
行数不一致：df1有2行，df2有1行
发现数据差异: 1397809
行数不一致：df1有2行，df2有1行
发现数据差异: 1397602
行数不一致：df1有2行，df2有1行
发现数据差异: 1396971
行数不一致：df1有2行，df2有1行
发现数据差异: 1396973
行数不一致：df1有2行，df2有1行
发现数据差异: 1396974
行数不一致：df1有2行，df2有1行
发现数据差异: 1396976
行数不一致：df1有2行，df2有1行
发现数据差异: 1397701
行数不一致：df1有2行，df2有1行
发现数据差异: 1397708
行数不一致：df1有2行，df2有1行
发现数据差异: 1396962
行数不一致：df1有2行，df2有1行
发现数据差异: 1397752
行数不一致：df1有2行，df2有1行
发现数据差异: 1396975
行数不一致：df1有2行，df2有1行
发现数据差异: 1396978
行数不一致：df1有2行，df2有1行
发现数据差异: 1396977
行数不一致：df1有2行，df2有1行
发现数据差异: 1397755
行数不一致：df1有2行，df2有1行
发现数据差异: 1397522
行数不一致：df1有2行，df2有1行
发现数据差异: 1397700
行数不一致：df1有2行，df2有1行
发现数据差异: 1397753
行数不一致：df1有2行，df2有1行
发现数据差异: 1397751
行数不一致：df1有2行，df2有1行
发现数据差异: 1397520
行数不一致：df1有2行，df2有1行
发现数据差异: 1397643
行数不一致：df1有2行，df2有1行
发现数据差异: 1397756
行数不一致：df1有2行，df2有1行
发现数据差异: 1397645
行数不一致：df1有2行，df2有1行
发现数据差异: 1396979
行数不一致：df1有2行，df2有1行
发现数据差异: 1397647
行数不一致：df1有2行，df2有1行
发现数据差异: 1397648
行数不一致：df1有2行，df2有1行
发现数据差异: 1397529
行数不一致：df1有2行，df2有1行
发现数据差异: 1397517
行数不一致：df1有2行，df2有1行
发现数据差异: 1397502
行数不一致：df1有2行，df2有1行
发现数据差异: 1397730
行数不一致：df1有2行，df2有1行
发现数据差异: 1397619
行数不一致：df1有2行，df2有1行
发现数据差异: 1397731
行数不一致：df1有2行，df2有1行
发现数据差异: 1397613
行数不一致：df1有2行，df2有1行
发现数据差异: 1397515
行数不一致：df1有2行，df2有1行
发现数据差异: 1397500
行数不一致：df1有2行，df2有1行
发现数据差异: 1397621
行数不一致：df1有2行，df2有1行
发现数据差异: 1397754
行数不一致：df1有2行，df2有1行
发现数据差异: 1397501
行数不一致：df1有2行，df2有1行
发现数据差异: 1397746
行数不一致：df1有2行，df2有1行
发现数据差异: 1397625
行数不一致：df1有2行，df2有1行
发现数据差异: 1397749
行数不一致：df1有2行，df2有1行
发现数据差异: 1397623
行数不一致：df1有2行，df2有1行
发现数据差异: 1397745
行数不一致：df1有2行，df2有1行
发现数据差异: 1397503
行数不一致：df1有2行，df2有1行
发现数据差异: 1397611
行数不一致：df1有2行，df2有1行
发现数据差异: 1397796
行数不一致：df1有2行，df2有1行
发现数据差异: 1397676
行数不一致：df1有2行，df2有1行
发现数据差异: 1397433
行数不一致：df1有2行，df2有1行
发现数据差异: 1397748
行数不一致：df1有2行，df2有1行
发现数据差异: 1397677
行数不一致：df1有2行，df2有1行
发现数据差异: 1397747
行数不一致：df1有2行，df2有1行
发现数据差异: 1397798
行数不一致：df1有2行，df2有1行
发现数据差异: 1397313
行数不一致：df1有2行，df2有1行
发现数据差异: 1397315
行数不一致：df1有2行，df2有1行
发现数据差异: 1397671
行数不一致：df1有2行，df2有1行
发现数据差异: 1397675
行数不一致：df1有2行，df2有1行
发现数据差异: 1397312
行数不一致：df1有2行，df2有1行
发现数据差异: 1397505
行数不一致：df1有2行，df2有1行
发现数据差异: 1397799
行数不一致：df1有2行，df2有1行
发现数据差异: 1397797
行数不一致：df1有2行，df2有1行
发现数据差异: 1397435
行数不一致：df1有2行，df2有1行
发现数据差异: 1397678
行数不一致：df1有2行，df2有1行
发现数据差异: 1397431
行数不一致：df1有2行，df2有1行
发现数据差异: 1397794
行数不一致：df1有2行，df2有1行
发现数据差异: 1397311
行数不一致：df1有2行，df2有1行
发现数据差异: 1397673
行数不一致：df1有2行，df2有1行
发现数据差异: 1397550
行数不一致：df1有2行，df2有1行
发现数据差异: 1397437
行数不一致：df1有2行，df2有1行
发现数据差异: 1397319
行数不一致：df1有2行，df2有1行
发现数据差异: 1397317
行数不一致：df1有2行，df2有1行
发现数据差异: 1397795
行数不一致：df1有2行，df2有1行
发现数据差异: 1397552
行数不一致：df1有2行，df2有1行
发现数据差异: 1397310
行数不一致：df1有2行，df2有1行
发现数据差异: 1397318
行数不一致：df1有2行，df2有1行
发现数据差异: 1397687
行数不一致：df1有2行，df2有1行
发现数据差异: 1397203
行数不一致：df1有2行，df2有1行
发现数据差异: 1397445
行数不一致：df1有2行，df2有1行
发现数据差异: 1397447
行数不一致：df1有2行，df2有1行
发现数据差异: 1397791
行数不一致：df1有2行，df2有1行
发现数据差异: 1397790
行数不一致：df1有2行，df2有1行
发现数据差异: 1397688
行数不一致：df1有2行，df2有1行
发现数据差异: 1397566
行数不一致：df1有2行，df2有1行
发现数据差异: 1397686
行数不一致：df1有2行，df2有1行
发现数据差异: 1397568
行数不一致：df1有2行，df2有1行
发现数据差异: 1397205
行数不一致：df1有2行，df2有1行
发现数据差异: 1397683
行数不一致：df1有2行，df2有1行
发现数据差异: 1397444
行数不一致：df1有2行，df2有1行
发现数据差异: 1397325
行数不一致：df1有2行，df2有1行
发现数据差异: 1397446
行数不一致：df1有2行，df2有1行
发现数据差异: 1397685
行数不一致：df1有2行，df2有1行
发现数据差异: 1397443
行数不一致：df1有2行，df2有1行
发现数据差异: 1397321
行数不一致：df1有2行，df2有1行
发现数据差异: 1397323
行数不一致：df1有2行，df2有1行
发现数据差异: 1397201
行数不一致：df1有2行，df2有1行
发现数据差异: 1397449
行数不一致：df1有2行，df2有1行
发现数据差异: 1397684
行数不一致：df1有2行，df2有1行
发现数据差异: 1397327
行数不一致：df1有2行，df2有1行
发现数据差异: 1397207
行数不一致：df1有2行，df2有1行
发现数据差异: 1397533
行数不一致：df1有2行，df2有1行
发现数据差异: 1397689
行数不一致：df1有2行，df2有1行
发现数据差异: 1397535
行数不一致：df1有2行，df2有1行
发现数据差异: 1397531
行数不一致：df1有2行，df2有1行
发现数据差异: 1397539
行数不一致：df1有2行，df2有1行
发现数据差异: 1397209
行数不一致：df1有2行，df2有1行
发现数据差异: 1397302
行数不一致：df1有2行，df2有1行
发现数据差异: 1397538
行数不一致：df1有2行，df2有1行
发现数据差异: 1397544
行数不一致：df1有2行，df2有1行
发现数据差异: 1397418
行数不一致：df1有2行，df2有1行
发现数据差异: 1397419
行数不一致：df1有2行，df2有1行
发现数据差异: 1397543
行数不一致：df1有2行，df2有1行
发现数据差异: 1397786
行数不一致：df1有2行，df2有1行
发现数据差异: 1397788
行数不一致：df1有2行，df2有1行
发现数据差异: 1397304
行数不一致：df1有2行，df2有1行
发现数据差异: 1397423
行数不一致：df1有2行，df2有1行
发现数据差异: 1397303
行数不一致：df1有2行，df2有1行
发现数据差异: 1397667
行数不一致：df1有2行，df2有1行
发现数据差异: 1397425
行数不一致：df1有2行，df2有1行
发现数据差异: 1397546
行数不一致：df1有2行，df2有1行
发现数据差异: 1397545
行数不一致：df1有2行，df2有1行
发现数据差异: 1397785
行数不一致：df1有2行，df2有1行
发现数据差异: 1397660
行数不一致：df1有2行，df2有1行
发现数据差异: 1397541
行数不一致：df1有2行，df2有1行
发现数据差异: 1397787
行数不一致：df1有2行，df2有1行
发现数据差异: 1397784
行数不一致：df1有2行，df2有1行
发现数据差异: 1397306
行数不一致：df1有2行，df2有1行
发现数据差异: 1397542
行数不一致：df1有2行，df2有1行
发现数据差异: 1397421
行数不一致：df1有2行，df2有1行
发现数据差异: 1397540
行数不一致：df1有2行，df2有1行
发现数据差异: 1397662
行数不一致：df1有2行，df2有1行
发现数据差异: 1397599
行数不一致：df1有2行，df2有1行
发现数据差异: 1397548
行数不一致：df1有2行，df2有1行
发现数据差异: 1397669
行数不一致：df1有2行，df2有1行
发现数据差异: 1397591
行数不一致：df1有2行，df2有1行
发现数据差异: 1397547
行数不一致：df1有2行，df2有1行
发现数据差异: 1397594
行数不一致：df1有2行，df2有1行
发现数据差异: 1397230
行数不一致：df1有2行，df2有1行
发现数据差异: 1397549
行数不一致：df1有2行，df2有1行
发现数据差异: 1397305
行数不一致：df1有2行，df2有1行
发现数据差异: 1397789
行数不一致：df1有2行，df2有1行
发现数据差异: 1397475
行数不一致：df1有2行，df2有1行
发现数据差异: 1397477
行数不一致：df1有2行，df2有1行
发现数据差异: 1397479
行数不一致：df1有2行，df2有1行
发现数据差异: 1397368
行数不一致：df1有2行，df2有1行
发现数据差异: 1397307
行数不一致：df1有2行，df2有1行
发现数据差异: 1397242
行数不一致：df1有2行，df2有1行
发现数据差异: 1397593
行数不一致：df1有2行，df2有1行
发现数据差异: 1397592
行数不一致：df1有2行，df2有1行
发现数据差异: 1397360
行数不一致：df1有2行，df2有1行
发现数据差异: 1397214
行数不一致：df1有2行，df2有1行
发现数据差异: 1397127
行数不一致：df1有2行，df2有1行
发现数据差异: 1397697
行数不一致：df1有2行，df2有1行
发现数据差异: 1397129
行数不一致：df1有2行，df2有1行
发现数据差异: 1397362
行数不一致：df1有2行，df2有1行
发现数据差异: 1397484
行数不一致：df1有2行，df2有1行
发现数据差异: 1397248
行数不一致：df1有2行，df2有1行
发现数据差异: 1397216
行数不一致：df1有2行，df2有1行
发现数据差异: 1397246
行数不一致：df1有2行，df2有1行
发现数据差异: 1397458
行数不一致：df1有2行，df2有1行
发现数据差异: 1397481
行数不一致：df1有2行，df2有1行
发现数据差异: 1397456
行数不一致：df1有2行，df2有1行
发现数据差异: 1397244
行数不一致：df1有2行，df2有1行
发现数据差异: 1397311
行数不一致：df1有2行，df2有1行
发现数据差异: 1397483
行数不一致：df1有2行，df2有1行
发现数据差异: 1397431
行数不一致：df1有2行，df2有1行
发现数据差异: 1397317
行数不一致：df1有2行，df2有1行
发现数据差异: 1397552
行数不一致：df1有2行，df2有1行
发现数据差异: 1397673
行数不一致：df1有2行，df2有1行
发现数据差异: 1397795
行数不一致：df1有2行，df2有1行
发现数据差异: 1397310
行数不一致：df1有2行，df2有1行
发现数据差异: 1397213
行数不一致：df1有2行，df2有1行
发现数据差异: 1397318
行数不一致：df1有2行，df2有1行
发现数据差异: 1397791
行数不一致：df1有2行，df2有1行
发现数据差异: 1397566
行数不一致：df1有2行，df2有1行
发现数据差异: 1397444
行数不一致：df1有2行，df2有1行
发现数据差异: 1397445
行数不一致：df1有2行，df2有1行
发现数据差异: 1397319
行数不一致：df1有2行，df2有1行
发现数据差异: 1397687
行数不一致：df1有2行，df2有1行
发现数据差异: 1397686
行数不一致：df1有2行，df2有1行
发现数据差异: 1397203
行数不一致：df1有2行，df2有1行
发现数据差异: 1397790
行数不一致：df1有2行，df2有1行
发现数据差异: 1397323
行数不一致：df1有2行，df2有1行
发现数据差异: 1397794
行数不一致：df1有2行，df2有1行
发现数据差异: 1397689
行数不一致：df1有2行，df2有1行
发现数据差异: 1397446
行数不一致：df1有2行，df2有1行
发现数据差异: 1397568
行数不一致：df1有2行，df2有1行
发现数据差异: 1397205
行数不一致：df1有2行，df2有1行
发现数据差异: 1397688
行数不一致：df1有2行，df2有1行
发现数据差异: 1397447
行数不一致：df1有2行，df2有1行
发现数据差异: 1397325
行数不一致：df1有2行，df2有1行
发现数据差异: 1397437
行数不一致：df1有2行，df2有1行
发现数据差异: 1397443
行数不一致：df1有2行，df2有1行
发现数据差异: 1397209
行数不一致：df1有2行，df2有1行
发现数据差异: 1397201
行数不一致：df1有2行，df2有1行
发现数据差异: 1397684
行数不一致：df1有2行，df2有1行
发现数据差异: 1397531
行数不一致：df1有2行，df2有1行
发现数据差异: 1397418
行数不一致：df1有2行，df2有1行
发现数据差异: 1397419
行数不一致：df1有2行，df2有1行
发现数据差异: 1397449
行数不一致：df1有2行，df2有1行
发现数据差异: 1397683
行数不一致：df1有2行，df2有1行
发现数据差异: 1397538
行数不一致：df1有2行，df2有1行
发现数据差异: 1397423
行数不一致：df1有2行，df2有1行
发现数据差异: 1397321
行数不一致：df1有2行，df2有1行
发现数据差异: 1397539
行数不一致：df1有2行，df2有1行
发现数据差异: 1397685
行数不一致：df1有2行，df2有1行
发现数据差异: 1397533
行数不一致：df1有2行，df2有1行
发现数据差异: 1397207
行数不一致：df1有2行，df2有1行
发现数据差异: 1397327
行数不一致：df1有2行，df2有1行
发现数据差异: 1397302
行数不一致：df1有2行，df2有1行
发现数据差异: 1397546
行数不一致：df1有2行，df2有1行
发现数据差异: 1397785
行数不一致：df1有2行，df2有1行
发现数据差异: 1397535
行数不一致：df1有2行，df2有1行
发现数据差异: 1397667
行数不一致：df1有2行，df2有1行
发现数据差异: 1397786
行数不一致：df1有2行，df2有1行
发现数据差异: 1397304
行数不一致：df1有2行，df2有1行
发现数据差异: 1397543
行数不一致：df1有2行，df2有1行
发现数据差异: 1397544
行数不一致：df1有2行，df2有1行
发现数据差异: 1397303
行数不一致：df1有2行，df2有1行
发现数据差异: 1397425
行数不一致：df1有2行，df2有1行
发现数据差异: 1397542
行数不一致：df1有2行，df2有1行
发现数据差异: 1397545
行数不一致：df1有2行，df2有1行
发现数据差异: 1397788
行数不一致：df1有2行，df2有1行
发现数据差异: 1397540
行数不一致：df1有2行，df2有1行
发现数据差异: 1397421
行数不一致：df1有2行，df2有1行
发现数据差异: 1397787
行数不一致：df1有2行，df2有1行
发现数据差异: 1397662
行数不一致：df1有2行，df2有1行
发现数据差异: 1397784
行数不一致：df1有2行，df2有1行
发现数据差异: 1397305
行数不一致：df1有2行，df2有1行
发现数据差异: 1397660
行数不一致：df1有2行，df2有1行
发现数据差异: 1397547
行数不一致：df1有2行，df2有1行
发现数据差异: 1397306
行数不一致：df1有2行，df2有1行
发现数据差异: 1397549
行数不一致：df1有2行，df2有1行
发现数据差异: 1397548
行数不一致：df1有2行，df2有1行
发现数据差异: 1397307
行数不一致：df1有2行，df2有1行
发现数据差异: 1397541
行数不一致：df1有2行，df2有1行
发现数据差异: 1397669
行数不一致：df1有2行，df2有1行
发现数据差异: 1397477
行数不一致：df1有2行，df2有1行
发现数据差异: 1397599
行数不一致：df1有2行，df2有1行
发现数据差异: 1397594
行数不一致：df1有2行，df2有1行
发现数据差异: 1397248
行数不一致：df1有2行，df2有1行
发现数据差异: 1397230
行数不一致：df1有2行，df2有1行
发现数据差异: 1397368
行数不一致：df1有2行，df2有1行
发现数据差异: 1397591
行数不一致：df1有2行，df2有1行
发现数据差异: 1397475
行数不一致：df1有2行，df2有1行
发现数据差异: 1397592
行数不一致：df1有2行，df2有1行
发现数据差异: 1397246
行数不一致：df1有2行，df2有1行
发现数据差异: 1397789
行数不一致：df1有2行，df2有1行
发现数据差异: 1397242
行数不一致：df1有2行，df2有1行
发现数据差异: 1397244
行数不一致：df1有2行，df2有1行
发现数据差异: 1397360
行数不一致：df1有2行，df2有1行
发现数据差异: 1397697
行数不一致：df1有2行，df2有1行
发现数据差异: 1397129
行数不一致：df1有2行，df2有1行
发现数据差异: 1397479
行数不一致：df1有2行，df2有1行
发现数据差异: 1397216
行数不一致：df1有2行，df2有1行
发现数据差异: 1397593
行数不一致：df1有2行，df2有1行
发现数据差异: 1397127
行数不一致：df1有2行，df2有1行
发现数据差异: 1397484
行数不一致：df1有2行，df2有1行
发现数据差异: 1397210
行数不一致：df1有2行，df2有1行
发现数据差异: 1397362
行数不一致：df1有2行，df2有1行
发现数据差异: 1397215
行数不一致：df1有2行，df2有1行
发现数据差异: 1397458
行数不一致：df1有2行，df2有1行
发现数据差异: 1397213
行数不一致：df1有2行，df2有1行
发现数据差异: 1397456
行数不一致：df1有2行，df2有1行
发现数据差异: 1397451
行数不一致：df1有2行，df2有1行
发现数据差异: 1397693
行数不一致：df1有2行，df2有1行
发现数据差异: 1397214
行数不一致：df1有2行，df2有1行
发现数据差异: 1397212
行数不一致：df1有2行，df2有1行
发现数据差异: 1397218
行数不一致：df1有2行，df2有1行
发现数据差异: 1397453
行数不一致：df1有2行，df2有1行
发现数据差异: 1397211
行数不一致：df1有2行，df2有1行
发现数据差异: 1397696
行数不一致：df1有2行，df2有1行
发现数据差异: 1397695
行数不一致：df1有2行，df2有1行
发现数据差异: 1397481
行数不一致：df1有2行，df2有1行
发现数据差异: 1397572
行数不一致：df1有2行，df2有1行
发现数据差异: 1397332
行数不一致：df1有2行，df2有1行
发现数据差异: 1397217
行数不一致：df1有2行，df2有1行
发现数据差异: 1397483
行数不一致：df1有2行，df2有1行
发现数据差异: 1397330
行数不一致：df1有2行，df2有1行
发现数据差异: 1397459
行数不一致：df1有2行，df2有1行
发现数据差异: 1397338
行数不一致：df1有2行，df2有1行
发现数据差异: 1397224
行数不一致：df1有2行，df2有1行
发现数据差异: 1397691
行数不一致：df1有2行，df2有1行
发现数据差异: 1397106
行数不一致：df1有2行，df2有1行
发现数据差异: 1397344
行数不一致：df1有2行，df2有1行
发现数据差异: 1397583
行数不一致：df1有2行，df2有1行
发现数据差异: 1397104
行数不一致：df1有2行，df2有1行
发现数据差异: 1397105
行数不一致：df1有2行，df2有1行
发现数据差异: 1397570
行数不一致：df1有2行，df2有1行
发现数据差异: 1397226
行数不一致：df1有2行，df2有1行
发现数据差异: 1397100
行数不一致：df1有2行，df2有1行
发现数据差异: 1397221
行数不一致：df1有2行，df2有1行
发现数据差异: 1397228
行数不一致：df1有2行，df2有1行
发现数据差异: 1397465
行数不一致：df1有2行，df2有1行
发现数据差异: 1397223
行数不一致：df1有2行，df2有1行
发现数据差异: 1397102
行数不一致：df1有2行，df2有1行
发现数据差异: 1397340
行数不一致：df1有2行，df2有1行
发现数据差异: 1397582
行数不一致：df1有2行，df2有1行
发现数据差异: 1397461
行数不一致：df1有2行，df2有1行
发现数据差异: 1397037
行数不一致：df1有2行，df2有1行
发现数据差异: 1397107
行数不一致：df1有2行，df2有1行
发现数据差异: 1397276
行数不一致：df1有2行，df2有1行
发现数据差异: 1397038
行数不一致：df1有2行，df2有1行
发现数据差异: 1397463
行数不一致：df1有2行，df2有1行
发现数据差异: 1397275
行数不一致：df1有2行，df2有1行
发现数据差异: 1397034
行数不一致：df1有2行，df2有1行
发现数据差异: 1397036
行数不一致：df1有2行，df2有1行
发现数据差异: 1397039
行数不一致：df1有2行，df2有1行
发现数据差异: 1397398
行数不一致：df1有2行，df2有1行
发现数据差异: 1397033
行数不一致：df1有2行，df2有1行
发现数据差异: 1397343
行数不一致：df1有2行，df2有1行
发现数据差异: 1397399
行数不一致：df1有2行，df2有1行
发现数据差异: 1397395
行数不一致：df1有2行，df2有1行
发现数据差异: 1397035
行数不一致：df1有2行，df2有1行
发现数据差异: 1397270
行数不一致：df1有2行，df2有1行
发现数据差异: 1397030
行数不一致：df1有2行，df2有1行
发现数据差异: 1397274
行数不一致：df1有2行，df2有1行
发现数据差异: 1397393
行数不一致：df1有2行，df2有1行
发现数据差异: 1397169
行数不一致：df1有2行，df2有1行
发现数据差异: 1397031
行数不一致：df1有2行，df2有1行
发现数据差异: 1397278
行数不一致：df1有2行，df2有1行
发现数据差异: 1397271
行数不一致：df1有2行，df2有1行
发现数据差异: 1397397
行数不一致：df1有2行，df2有1行
发现数据差异: 1397044
行数不一致：df1有2行，df2有1行
发现数据差异: 1397289
行数不一致：df1有2行，df2有1行
发现数据差异: 1397391
行数不一致：df1有2行，df2有1行
发现数据差异: 1397040
行数不一致：df1有2行，df2有1行
发现数据差异: 1397048
行数不一致：df1有2行，df2有1行
发现数据差异: 1397032
行数不一致：df1有2行，df2有1行
发现数据差异: 1397041
行数不一致：df1有2行，df2有1行
发现数据差异: 1397288
行数不一致：df1有2行，df2有1行
发现数据差异: 1397282
行数不一致：df1有2行，df2有1行
发现数据差异: 1397015
行数不一致：df1有2行，df2有1行
发现数据差异: 1397136
行数不一致：df1有2行，df2有1行
发现数据差异: 1397046
行数不一致：df1有2行，df2有1行
发现数据差异: 1397280
行数不一致：df1有2行，df2有1行
发现数据差异: 1397137
行数不一致：df1有2行，df2有1行
发现数据差异: 1397042
行数不一致：df1有2行，df2有1行
发现数据差异: 1397163
行数不一致：df1有2行，df2有1行
发现数据差异: 1397138
行数不一致：df1有2行，df2有1行
发现数据差异: 1397499
行数不一致：df1有2行，df2有1行
发现数据差异: 1397374
行数不一致：df1有2行，df2有1行
发现数据差异: 1397498
行数不一致：df1有2行，df2有1行
发现数据差异: 1397164
行数不一致：df1有2行，df2有1行
发现数据差异: 1397497
行数不一致：df1有2行，df2有1行
发现数据差异: 1397012
行数不一致：df1有2行，df2有1行
发现数据差异: 1397148
行数不一致：df1有2行，df2有1行
发现数据差异: 1397493
行数不一致：df1有2行，df2有1行
发现数据差异: 1397029
行数不一致：df1有2行，df2有1行
发现数据差异: 1397370
行数不一致：df1有2行，df2有1行
发现数据差异: 1397492
行数不一致：df1有2行，df2有1行
发现数据差异: 1397013
行数不一致：df1有2行，df2有1行
发现数据差异: 1397385
行数不一致：df1有2行，df2有1行
发现数据差异: 1397146
行数不一致：df1有2行，df2有1行
发现数据差异: 1397372
行数不一致：df1有2行，df2有1行
发现数据差异: 1397269
行数不一致：df1有2行，df2有1行
发现数据差异: 1397389
行数不一致：df1有2行，df2有1行
发现数据差异: 1397026
行数不一致：df1有2行，df2有1行
发现数据差异: 1397215
行数不一致：df1有2行，df2有1行
发现数据差异: 1397014
行数不一致：df1有2行，df2有1行
发现数据差异: 1397267
行数不一致：df1有2行，df2有1行
发现数据差异: 1397210
行数不一致：df1有2行，df2有1行
发现数据差异: 1397028
行数不一致：df1有2行，df2有1行
发现数据差异: 1397265
行数不一致：df1有2行，df2有1行
发现数据差异: 1397143
行数不一致：df1有2行，df2有1行
发现数据差异: 1397330
行数不一致：df1有2行，df2有1行
发现数据差异: 1397027
行数不一致：df1有2行，df2有1行
发现数据差异: 1397572
行数不一致：df1有2行，df2有1行
发现数据差异: 1397387
行数不一致：df1有2行，df2有1行
发现数据差异: 1397212
行数不一致：df1有2行，df2有1行
发现数据差异: 1397217
行数不一致：df1有2行，df2有1行
发现数据差异: 1397211
行数不一致：df1有2行，df2有1行
发现数据差异: 1397695
行数不一致：df1有2行，df2有1行
发现数据差异: 1397338
行数不一致：df1有2行，df2有1行
发现数据差异: 1397453
行数不一致：df1有2行，df2有1行
发现数据差异: 1397451
行数不一致：df1有2行，df2有1行
发现数据差异: 1397459
行数不一致：df1有2行，df2有1行
发现数据差异: 1397332
行数不一致：df1有2行，df2有1行
发现数据差异: 1397691
行数不一致：df1有2行，df2有1行
发现数据差异: 1397693
行数不一致：df1有2行，df2有1行
发现数据差异: 1397224
行数不一致：df1有2行，df2有1行
发现数据差异: 1397218
行数不一致：df1有2行，df2有1行
发现数据差异: 1397104
行数不一致：df1有2行，df2有1行
发现数据差异: 1397106
行数不一致：df1有2行，df2有1行
发现数据差异: 1397463
行数不一致：df1有2行，df2有1行
发现数据差异: 1397696
行数不一致：df1有2行，df2有1行
发现数据差异: 1397570
行数不一致：df1有2行，df2有1行
发现数据差异: 1397223
行数不一致：df1有2行，df2有1行
发现数据差异: 1397105
行数不一致：df1有2行，df2有1行
发现数据差异: 1397226
行数不一致：df1有2行，df2有1行
发现数据差异: 1397583
行数不一致：df1有2行，df2有1行
发现数据差异: 1397465
行数不一致：df1有2行，df2有1行
发现数据差异: 1397102
行数不一致：df1有2行，df2有1行
发现数据差异: 1397461
行数不一致：df1有2行，df2有1行
发现数据差异: 1397344
行数不一致：df1有2行，df2有1行
发现数据差异: 1397228
行数不一致：df1有2行，df2有1行
发现数据差异: 1397100
行数不一致：df1有2行，df2有1行
发现数据差异: 1397221
行数不一致：df1有2行，df2有1行
发现数据差异: 1397039
行数不一致：df1有2行，df2有1行
发现数据差异: 1397107
行数不一致：df1有2行，df2有1行
发现数据差异: 1397038
行数不一致：df1有2行，df2有1行
发现数据差异: 1397037
行数不一致：df1有2行，df2有1行
发现数据差异: 1397397
行数不一致：df1有2行，df2有1行
发现数据差异: 1397275
行数不一致：df1有2行，df2有1行
发现数据差异: 1397343
行数不一致：df1有2行，df2有1行
发现数据差异: 1397340
行数不一致：df1有2行，df2有1行
发现数据差异: 1397034
行数不一致：df1有2行，df2有1行
发现数据差异: 1397582
行数不一致：df1有2行，df2有1行
发现数据差异: 1397398
行数不一致：df1有2行，df2有1行
发现数据差异: 1397033
行数不一致：df1有2行，df2有1行
发现数据差异: 1397035
行数不一致：df1有2行，df2有1行
发现数据差异: 1397276
行数不一致：df1有2行，df2有1行
发现数据差异: 1397278
行数不一致：df1有2行，df2有1行
发现数据差异: 1397036
行数不一致：df1有2行，df2有1行
发现数据差异: 1397395
行数不一致：df1有2行，df2有1行
发现数据差异: 1397399
行数不一致：df1有2行，df2有1行
发现数据差异: 1397031
行数不一致：df1有2行，df2有1行
发现数据差异: 1397032
行数不一致：df1有2行，df2有1行
发现数据差异: 1397274
行数不一致：df1有2行，df2有1行
发现数据差异: 1397393
行数不一致：df1有2行，df2有1行
发现数据差异: 1397391
行数不一致：df1有2行，df2有1行
发现数据差异: 1397030
行数不一致：df1有2行，df2有1行
发现数据差异: 1397271
行数不一致：df1有2行，df2有1行
发现数据差异: 1397270
行数不一致：df1有2行，df2有1行
发现数据差异: 1397048
行数不一致：df1有2行，df2有1行
发现数据差异: 1397282
行数不一致：df1有2行，df2有1行
发现数据差异: 1397169
行数不一致：df1有2行，df2有1行
发现数据差异: 1397163
行数不一致：df1有2行，df2有1行
发现数据差异: 1397044
行数不一致：df1有2行，df2有1行
发现数据差异: 1397046
行数不一致：df1有2行，df2有1行
发现数据差异: 1397288
行数不一致：df1有2行，df2有1行
发现数据差异: 1397164
行数不一致：df1有2行，df2有1行
发现数据差异: 1397289
行数不一致：df1有2行，df2有1行
发现数据差异: 1397040
行数不一致：df1有2行，df2有1行
发现数据差异: 1397137
行数不一致：df1有2行，df2有1行
发现数据差异: 1397136
行数不一致：df1有2行，df2有1行
发现数据差异: 1397041
行数不一致：df1有2行，df2有1行
发现数据差异: 1397280
行数不一致：df1有2行，df2有1行
发现数据差异: 1397370
行数不一致：df1有2行，df2有1行
发现数据差异: 1397015
行数不一致：df1有2行，df2有1行
发现数据差异: 1397138
行数不一致：df1有2行，df2有1行
发现数据差异: 1397498
行数不一致：df1有2行，df2有1行
发现数据差异: 1397499
行数不一致：df1有2行，df2有1行
发现数据差异: 1397012
行数不一致：df1有2行，df2有1行
发现数据差异: 1397013
行数不一致：df1有2行，df2有1行
发现数据差异: 1397497
行数不一致：df1有2行，df2有1行
发现数据差异: 1397372
行数不一致：df1有2行，df2有1行
发现数据差异: 1397492
行数不一致：df1有2行，df2有1行
发现数据差异: 1397014
行数不一致：df1有2行，df2有1行
发现数据差异: 1397374
行数不一致：df1有2行，df2有1行
发现数据差异: 1397042
行数不一致：df1有2行，df2有1行
发现数据差异: 1397493
行数不一致：df1有2行，df2有1行
发现数据差异: 1397389
行数不一致：df1有2行，df2有1行
发现数据差异: 1397026
行数不一致：df1有2行，df2有1行
发现数据差异: 1397029
行数不一致：df1有2行，df2有1行
发现数据差异: 1397027
行数不一致：df1有2行，df2有1行
发现数据差异: 1397269
行数不一致：df1有2行，df2有1行
发现数据差异: 1397143
行数不一致：df1有2行，df2有1行
发现数据差异: 1397142
行数不一致：df1有2行，df2有1行
发现数据差异: 1397387
行数不一致：df1有2行，df2有1行
发现数据差异: 1397385
行数不一致：df1有2行，df2有1行
发现数据差异: 1397263
行数不一致：df1有2行，df2有1行
发现数据差异: 1397140
行数不一致：df1有2行，df2有1行
发现数据差异: 1397148
行数不一致：df1有2行，df2有1行
发现数据差异: 1397383
行数不一致：df1有2行，df2有1行
发现数据差异: 1398049
行数不一致：df1有2行，df2有1行
发现数据差异: 1397146
行数不一致：df1有2行，df2有1行
发现数据差异: 1398167
行数不一致：df1有2行，df2有1行
发现数据差异: 1397265
行数不一致：df1有2行，df2有1行
发现数据差异: 1399379
行数不一致：df1有2行，df2有1行
发现数据差异: 1397267
行数不一致：df1有2行，df2有1行
发现数据差异: 1397028
行数不一致：df1有2行，df2有1行
发现数据差异: 1399258
行数不一致：df1有2行，df2有1行
发现数据差异: 1399499
行数不一致：df1有2行，df2有1行
发现数据差异: 1398045
行数不一致：df1有2行，df2有1行
发现数据差异: 1398289
行数不一致：df1有2行，df2有1行
发现数据差异: 1399139
行数不一致：df1有2行，df2有1行
发现数据差异: 1398163
行数不一致：df1有2行，df2有1行
发现数据差异: 1398041
行数不一致：df1有2行，df2有1行
发现数据差异: 1398285
行数不一致：df1有2行，df2有1行
发现数据差异: 1398161
行数不一致：df1有2行，df2有1行
发现数据差异: 1399135
行数不一致：df1有2行，df2有1行
发现数据差异: 1399254
行数不一致：df1有2行，df2有1行
发现数据差异: 1398059
行数不一致：df1有2行，df2有1行
发现数据差异: 1398043
行数不一致：df1有2行，df2有1行
发现数据差异: 1398047
行数不一致：df1有2行，df2有1行
发现数据差异: 1399495
行数不一致：df1有2行，df2有1行
发现数据差异: 1398173
行数不一致：df1有2行，df2有1行
发现数据差异: 1399147
行数不一致：df1有2行，df2有1行
发现数据差异: 1399026
行数不一致：df1有2行，df2有1行
发现数据差异: 1399491
行数不一致：df1有2行，df2有1行
发现数据差异: 1398299
行数不一致：df1有2行，df2有1行
发现数据差异: 1399028
行数不一致：df1有2行，df2有1行
发现数据差异: 1398024
行数不一致：df1有2行，df2有1行
发现数据差异: 1399266
行数不一致：df1有2行，df2有1行
发现数据差异: 1398051
行数不一致：df1有2行，df2有1行
发现数据差异: 1399143
行数不一致：df1有2行，df2有1行
发现数据差异: 1399024
行数不一致：df1有2行，df2有1行
发现数据差异: 1399383
行数不一致：df1有2行，df2有1行
发现数据差异: 1398171
行数不一致：df1有2行，df2有1行
发现数据差异: 1399262
行数不一致：df1有2行，df2有1行
发现数据差异: 1398027
行数不一致：df1有2行，df2有1行
发现数据差异: 1399020
行数不一致：df1有2行，df2有1行
发现数据差异: 1398029
行数不一致：df1有2行，df2有1行
发现数据差异: 1398265
行数不一致：df1有2行，df2有1行
发现数据差异: 1398269
行数不一致：df1有2行，df2有1行
发现数据差异: 1398055
行数不一致：df1有2行，df2有1行
发现数据差异: 1399113
行数不一致：df1有2行，df2有1行
发现数据差异: 1399353
行数不一致：df1有2行，df2有1行
发现数据差异: 1398141
行数不一致：df1有2行，df2有1行
发现数据差异: 1398023
行数不一致：df1有2行，df2有1行
发现数据差异: 1399357
行数不一致：df1有2行，df2有1行
发现数据差异: 1398261
行数不一致：df1有2行，df2有1行
发现数据差异: 1398035
行数不一致：df1有2行，df2有1行
发现数据差异: 1399355
行数不一致：df1有2行，df2有1行
发现数据差异: 1398037
行数不一致：df1有2行，df2有1行
发现数据差异: 1398021
行数不一致：df1有2行，df2有1行
发现数据差异: 1398273
行数不一致：df1有2行，df2有1行
发现数据差异: 1398399
行数不一致：df1有2行，df2有1行
发现数据差异: 1398143
行数不一致：df1有2行，df2有1行
发现数据差异: 1398145
行数不一致：df1有2行，df2有1行
发现数据差异: 1399487
行数不一致：df1有2行，df2有1行
发现数据差异: 1398033
行数不一致：df1有2行，df2有1行
发现数据差异: 1399170
行数不一致：df1有2行，df2有1行
发现数据差异: 1399483
行数不一致：df1有2行，df2有1行
发现数据差异: 1399177
行数不一致：df1有2行，df2有1行
发现数据差异: 1398038
行数不一致：df1有2行，df2有1行
发现数据差异: 1398159
行数不一致：df1有2行，df2有1行
发现数据差异: 1398031
行数不一致：df1有2行，df2有1行
发现数据差异: 1399050
行数不一致：df1有2行，df2有1行
发现数据差异: 1398085
行数不一致：df1有2行，df2有1行
发现数据差异: 1399067
行数不一致：df1有2行，df2有1行
发现数据差异: 1399054
行数不一致：df1有2行，df2有1行
发现数据差异: 1398099
行数不一致：df1有2行，df2有1行
发现数据差异: 1398095
行数不一致：df1有2行，df2有1行
发现数据差异: 1398097
行数不一致：df1有2行，df2有1行
发现数据差异: 1398091
行数不一致：df1有2行，df2有1行
发现数据差异: 1399182
行数不一致：df1有2行，df2有1行
发现数据差异: 1398083
行数不一致：df1有2行，df2有1行
发现数据差异: 1398067
行数不一致：df1有2行，df2有1行
发现数据差异: 1398093
行数不一致：df1有2行，df2有1行
发现数据差异: 1399037
行数不一致：df1有2行，df2有1行
发现数据差异: 1399186
行数不一致：df1有2行，df2有1行
发现数据差异: 1398063
行数不一致：df1有2行，df2有1行
发现数据差异: 1398189
行数不一致：df1有2行，df2有1行
发现数据差异: 1399036
行数不一致：df1有2行，df2有1行
发现数据差异: 1399155
行数不一致：df1有2行，df2有1行
发现数据差异: 1399399
行数不一致：df1有2行，df2有1行
发现数据差异: 1399159
行数不一致：df1有2行，df2有1行
发现数据差异: 1399157
行数不一致：df1有2行，df2有1行
发现数据差异: 1398185
行数不一致：df1有2行，df2有1行
发现数据差异: 1399276
行数不一致：df1有2行，df2有1行
发现数据差异: 1399274
行数不一致：df1有2行，df2有1行
发现数据差异: 1399153
行数不一致：df1有2行，df2有1行
发现数据差异: 1399395
行数不一致：df1有2行，df2有1行
发现数据差异: 1399167
行数不一致：df1有2行，df2有1行
发现数据差异: 1398079
行数不一致：df1有2行，df2有1行
发现数据差异: 1399288
行数不一致：df1有2行，df2有1行
发现数据差异: 1399161
行数不一致：df1有2行，df2有1行
发现数据差异: 1399165
行数不一致：df1有2行，df2有1行
发现数据差异: 1398197
行数不一致：df1有2行，df2有1行
行数不一致：df1有2行，df2有1行
发现数据差异: 1399163
发现数据差异: 1398071
行数不一致：df1有2行，df2有1行
发现数据差异: 1398075
行数不一致：df1有2行，df2有1行
发现数据差异: 1399272
行数不一致：df1有2行，df2有1行
发现数据差异: 1398193
行数不一致：df1有2行，df2有1行
发现数据差异: 1404099
行数不一致：df1有2行，df2有1行
发现数据差异: 1397915
行数不一致：df1有2行，df2有1行
发现数据差异: 1399280
行数不一致：df1有2行，df2有1行
发现数据差异: 1397917
行数不一致：df1有2行，df2有1行
发现数据差异: 1397921
行数不一致：df1有2行，df2有1行
发现数据差异: 1399168
行数不一致：df1有2行，df2有1行
发现数据差异: 1399284
行数不一致：df1有2行，df2有1行
发现数据差异: 1399097
行数不一致：df1有2行，df2有1行
发现数据差异: 1397913
行数不一致：df1有2行，df2有1行
发现数据差异: 1397911
行数不一致：df1有2行，df2有1行
发现数据差异: 1399190
行数不一致：df1有2行，df2有1行
发现数据差异: 1397919
行数不一致：df1有2行，df2有1行
发现数据差异: 1399071
行数不一致：df1有2行，df2有1行
发现数据差异: 1399083
行数不一致：df1有2行，df2有1行
发现数据差异: 1399081
行数不一致：df1有2行，df2有1行
发现数据差异: 1397923
行数不一致：df1有2行，df2有1行
发现数据差异: 1399087
行数不一致：df1有2行，df2有1行
发现数据差异: 1399079
行数不一致：df1有2行，df2有1行
发现数据差异: 1398928
行数不一致：df1有2行，df2有1行
发现数据差异: 1399085
行数不一致：df1有2行，df2有1行
发现数据差异: 1399093
行数不一致：df1有2行，df2有1行
发现数据差异: 1398805
行数不一致：df1有2行，df2有1行
发现数据差异: 1399075
行数不一致：df1有2行，df2有1行
发现数据差异: 1397927
行数不一致：df1有2行，df2有1行
发现数据差异: 1397953
行数不一致：df1有2行，df2有1行
发现数据差异: 1397957
行数不一致：df1有2行，df2有1行
发现数据差异: 1397834
行数不一致：df1有2行，df2有1行
发现数据差异: 1398931
行数不一致：df1有2行，df2有1行
发现数据差异: 1398801
行数不一致：df1有2行，df2有1行
发现数据差异: 1398808
行数不一致：df1有2行，df2有1行
发现数据差异: 1398810
行数不一致：df1有2行，df2有1行
发现数据差异: 1397838
行数不一致：df1有2行，df2有1行
发现数据差异: 1398930
行数不一致：df1有2行，df2有1行
发现数据差异: 1397961
行数不一致：df1有2行，df2有1行
发现数据差异: 1398818
行数不一致：df1有2行，df2有1行
发现数据差异: 1398938
行数不一致：df1有2行，df2有1行
发现数据差异: 1398816
行数不一致：df1有2行，df2有1行
发现数据差异: 1397848
行数不一致：df1有2行，df2有1行
发现数据差异: 1398814
行数不一致：df1有2行，df2有1行
发现数据差异: 1398936
行数不一致：df1有2行，df2有1行
发现数据差异: 1398812
行数不一致：df1有2行，df2有1行
发现数据差异: 1398933
行数不一致：df1有2行，df2有1行
发现数据差异: 1397931
行数不一致：df1有2行，df2有1行
发现数据差异: 1397935
行数不一致：df1有2行，df2有1行
发现数据差异: 1397945
行数不一致：df1有2行，df2有1行
发现数据差异: 1397816
行数不一致：df1有2行，df2有1行
发现数据差异: 1397846
行数不一致：df1有2行，df2有1行
发现数据差异: 1397995
行数不一致：df1有2行，df2有1行
发现数据差异: 1397821
行数不一致：df1有2行，df2有1行
发现数据差异: 1397949
行数不一致：df1有2行，df2有1行
发现数据差异: 1397874
行数不一致：df1有2行，df2有1行
发现数据差异: 1397876
行数不一致：df1有2行，df2有1行
发现数据差异: 1398609
行数不一致：df1有2行，df2有1行
发现数据差异: 1397997
行数不一致：df1有2行，df2有1行
发现数据差异: 1397842
行数不一致：df1有2行，df2有1行
发现数据差异: 1397993
行数不一致：df1有2行，df2有1行
发现数据差异: 1397991
行数不一致：df1有2行，df2有1行
发现数据差异: 1397872
行数不一致：df1有2行，df2有1行
发现数据差异: 1398605
行数不一致：df1有2行，df2有1行
发现数据差异: 1398723
行数不一致：df1有2行，df2有1行
发现数据差异: 1398725
行数不一致：df1有2行，df2有1行
发现数据差异: 1397870
行数不一致：df1有2行，df2有1行
发现数据差异: 1397887
行数不一致：df1有2行，df2有1行
发现数据差异: 1397878
行数不一致：df1有2行，df2有1行
发现数据差异: 1398727
行数不一致：df1有2行，df2有1行
发现数据差异: 1398729
行数不一致：df1有2行，df2有1行
发现数据差异: 1398733
行数不一致：df1有2行，df2有1行
发现数据差异: 1398607
行数不一致：df1有2行，df2有1行
发现数据差异: 1409956
行数不一致：df1有2行，df2有1行
发现数据差异: 1397889
行数不一致：df1有2行，df2有1行
发现数据差异: 1398739
行数不一致：df1有2行，df2有1行
发现数据差异: 1398731
行数不一致：df1有2行，df2有1行
发现数据差异: 1398974
行数不一致：df1有2行，df2有1行
发现数据差异: 1398735
行数不一致：df1有2行，df2有1行
发现数据差异: 1397852
行数不一致：df1有2行，df2有1行
发现数据差异: 1397882
行数不一致：df1有2行，df2有1行
发现数据差异: 1398603
行数不一致：df1有2行，df2有1行
发现数据差异: 1398978
行数不一致：df1有2行，df2有1行
发现数据差异: 1398946
行数不一致：df1有2行，df2有1行
发现数据差异: 1398942
行数不一致：df1有2行，df2有1行
发现数据差异: 1397880
行数不一致：df1有2行，df2有1行
发现数据差异: 1397854
行数不一致：df1有2行，df2有1行
发现数据差异: 1398820
行数不一致：df1有2行，df2有1行
发现数据差异: 1398822
行数不一致：df1有2行，df2有1行
发现数据差异: 1398828
行数不一致：df1有2行，df2有1行
发现数据差异: 1398940
行数不一致：df1有2行，df2有1行
发现数据差异: 1397858
行数不一致：df1有2行，df2有1行
发现数据差异: 1398826
行数不一致：df1有2行，df2有1行
发现数据差异: 1398970
行数不一致：df1有2行，df2有1行
发现数据差异: 1397850
行数不一致：df1有2行，df2有1行
发现数据差异: 1398954
行数不一致：df1有2行，df2有1行
发现数据差异: 1398709
行数不一致：df1有2行，df2有1行
发现数据差异: 1398711
行数不一致：df1有2行，df2有1行
发现数据差异: 1398836
行数不一致：df1有2行，df2有1行
发现数据差异: 1397140
行数不一致：df1有2行，df2有1行
发现数据差异: 1398950
行数不一致：df1有2行，df2有1行
发现数据差异: 1397263
行数不一致：df1有2行，df2有1行
发现数据差异: 1398958
行数不一致：df1有2行，df2有1行
发现数据差异: 1397383
行数不一致：df1有2行，df2有1行
发现数据差异: 1398832
行数不一致：df1有2行，df2有1行
发现数据差异: 1398049
行数不一致：df1有2行，df2有1行
发现数据差异: 1397866
行数不一致：df1有2行，df2有1行
发现数据差异: 1399139
行数不一致：df1有2行，df2有1行
发现数据差异: 1397142
行数不一致：df1有2行，df2有1行
发现数据差异: 1397862行数不一致：df1有2行，df2有1行
发现数据差异: 1399499

行数不一致：df1有2行，df2有1行
发现数据差异: 1399495
行数不一致：df1有2行，df2有1行
发现数据差异: 1399379
行数不一致：df1有2行，df2有1行
发现数据差异: 1399258
行数不一致：df1有2行，df2有1行
发现数据差异: 1399135
行数不一致：df1有2行，df2有1行
发现数据差异: 1398047
行数不一致：df1有2行，df2有1行
发现数据差异: 1399254
行数不一致：df1有2行，df2有1行
发现数据差异: 1398167
行数不一致：df1有2行，df2有1行
发现数据差异: 1398041
行数不一致：df1有2行，df2有1行
发现数据差异: 1398043
行数不一致：df1有2行，df2有1行
发现数据差异: 1398289
行数不一致：df1有2行，df2有1行
发现数据差异: 1398299
行数不一致：df1有2行，df2有1行
发现数据差异: 1399026
行数不一致：df1有2行，df2有1行
发现数据差异: 1398163
行数不一致：df1有2行，df2有1行
发现数据差异: 1398059
行数不一致：df1有2行，df2有1行
发现数据差异: 1399024
行数不一致：df1有2行，df2有1行
发现数据差异: 1399147
行数不一致：df1有2行，df2有1行
发现数据差异: 1398285
行数不一致：df1有2行，df2有1行
发现数据差异: 1399491
行数不一致：df1有2行，df2有1行
发现数据差异: 1399028
行数不一致：df1有2行，df2有1行
发现数据差异: 1398045
行数不一致：df1有2行，df2有1行
发现数据差异: 1399262
行数不一致：df1有2行，df2有1行
发现数据差异: 1398055
行数不一致：df1有2行，df2有1行
发现数据差异: 1399143
行数不一致：df1有2行，df2有1行
发现数据差异: 1399383
行数不一致：df1有2行，df2有1行
发现数据差异: 1398051
行数不一致：df1有2行，df2有1行
发现数据差异: 1398173
行数不一致：df1有2行，df2有1行
发现数据差异: 1398269
行数不一致：df1有2行，df2有1行
发现数据差异: 1398161
行数不一致：df1有2行，df2有1行
发现数据差异: 1399266
行数不一致：df1有2行，df2有1行
发现数据差异: 1398024
行数不一致：df1有2行，df2有1行
发现数据差异: 1398141
行数不一致：df1有2行，df2有1行
发现数据差异: 1399353
行数不一致：df1有2行，df2有1行
发现数据差异: 1398145
行数不一致：df1有2行，df2有1行
发现数据差异: 1398171
行数不一致：df1有2行，df2有1行
发现数据差异: 1399020
行数不一致：df1有2行，df2有1行
发现数据差异: 1398038
行数不一致：df1有2行，df2有1行
发现数据差异: 1399113
行数不一致：df1有2行，df2有1行
发现数据差异: 1398143
行数不一致：df1有2行，df2有1行
发现数据差异: 1398261
行数不一致：df1有2行，df2有1行
发现数据差异: 1398029
行数不一致：df1有2行，df2有1行
发现数据差异: 1399355
行数不一致：df1有2行，df2有1行
发现数据差异: 1398159
行数不一致：df1有2行，df2有1行
发现数据差异: 1398027
行数不一致：df1有2行，df2有1行
发现数据差异: 1398021
行数不一致：df1有2行，df2有1行
发现数据差异: 1399357
行数不一致：df1有2行，df2有1行
发现数据差异: 1398265
行数不一致：df1有2行，df2有1行
发现数据差异: 1398399
行数不一致：df1有2行，df2有1行
发现数据差异: 1398037
行数不一致：df1有2行，df2有1行
发现数据差异: 1398035
行数不一致：df1有2行，df2有1行
发现数据差异: 1398023
行数不一致：df1有2行，df2有1行
发现数据差异: 1398031
行数不一致：df1有2行，df2有1行
发现数据差异: 1398085
行数不一致：df1有2行，df2有1行
发现数据差异: 1399483
行数不一致：df1有2行，df2有1行
发现数据差异: 1398083
行数不一致：df1有2行，df2有1行
发现数据差异: 1399177
行数不一致：df1有2行，df2有1行
发现数据差异: 1399170
行数不一致：df1有2行，df2有1行
发现数据差异: 1399050
行数不一致：df1有2行，df2有1行
发现数据差异: 1398091
行数不一致：df1有2行，df2有1行
发现数据差异: 1399054
行数不一致：df1有2行，df2有1行
发现数据差异: 1398033
行数不一致：df1有2行，df2有1行
发现数据差异: 1398273
行数不一致：df1有2行，df2有1行
发现数据差异: 1398093
行数不一致：df1有2行，df2有1行
发现数据差异: 1399159
行数不一致：df1有2行，df2有1行
发现数据差异: 1399067
行数不一致：df1有2行，df2有1行
发现数据差异: 1398097
行数不一致：df1有2行，df2有1行
发现数据差异: 1398099
行数不一致：df1有2行，df2有1行
发现数据差异: 1399182
行数不一致：df1有2行，df2有1行
发现数据差异: 1399036
行数不一致：df1有2行，df2有1行
发现数据差异: 1398189
行数不一致：df1有2行，df2有1行
发现数据差异: 1399186
行数不一致：df1有2行，df2有1行
发现数据差异: 1399487
行数不一致：df1有2行，df2有1行
发现数据差异: 1398185
行数不一致：df1有2行，df2有1行
发现数据差异: 1399157
行数不一致：df1有2行，df2有1行
发现数据差异: 1399037
行数不一致：df1有2行，df2有1行
发现数据差异: 1398063
行数不一致：df1有2行，df2有1行
发现数据差异: 1399155
行数不一致：df1有2行，df2有1行
发现数据差异: 1399272
行数不一致：df1有2行，df2有1行
发现数据差异: 1398079
行数不一致：df1有2行，df2有1行
发现数据差异: 1399288
行数不一致：df1有2行，df2有1行
发现数据差异: 1398067
行数不一致：df1有2行，df2有1行
发现数据差异: 1399276
行数不一致：df1有2行，df2有1行
发现数据差异: 1399274
行数不一致：df1有2行，df2有1行
发现数据差异: 1399395
行数不一致：df1有2行，df2有1行
发现数据差异: 1399163
行数不一致：df1有2行，df2有1行
发现数据差异: 1398095
行数不一致：df1有2行，df2有1行
发现数据差异: 1399167
行数不一致：df1有2行，df2有1行
发现数据差异: 1399399
行数不一致：df1有2行，df2有1行
发现数据差异: 1399165
行数不一致：df1有2行，df2有1行
发现数据差异: 1399284
行数不一致：df1有2行，df2有1行
发现数据差异: 1398401
行数不一致：df1有2行，df2有1行
发现数据差异: 1398075
行数不一致：df1有2行，df2有1行
发现数据差异: 1399168
行数不一致：df1有2行，df2有1行
发现数据差异: 1398197
行数不一致：df1有2行，df2有1行
发现数据差异: 1399153
行数不一致：df1有2行，df2有1行
发现数据差异: 1404100
行数不一致：df1有2行，df2有1行
发现数据差异: 1398524
行数不一致：df1有2行，df2有1行
发现数据差异: 1398409
行数不一致：df1有2行，df2有1行
发现数据差异: 1398765
行数不一致：df1有2行，df2有1行
发现数据差异: 1398521
行数不一致：df1有2行，df2有1行
发现数据差异: 1398520
行数不一致：df1有2行，df2有1行
发现数据差异: 1398649
行数不一致：df1有2行，df2有1行
发现数据差异: 1398645
行数不一致：df1有2行，df2有1行
发现数据差异: 1398777
行数不一致：df1有2行，df2有1行
发现数据差异: 1399503
行数不一致：df1有2行，df2有1行
发现数据差异: 1398526
行数不一致：df1有2行，df2有1行
发现数据差异: 1398641
行数不一致：df1有2行，df2有1行
发现数据差异: 1398529
行数不一致：df1有2行，df2有1行
发现数据差异: 1398769
行数不一致：df1有2行，df2有1行
发现数据差异: 1398896
行数不一致：df1有2行，df2有1行
行数不一致：df1有2行，df2有1行
发现数据差异: 1398655
发现数据差异: 1398773
行数不一致：df1有2行，df2有1行
发现数据差异: 1398894
行数不一致：df1有2行，df2有1行
发现数据差异: 1398651
行数不一致：df1有2行，df2有1行
发现数据差异: 1398539
行数不一致：df1有2行，df2有1行
发现数据差异: 1399507
行数不一致：df1有2行，df2有1行
发现数据差异: 1398537
行数不一致：df1有2行，df2有1行
发现数据差异: 1398866
行数不一致：df1有2行，df2有1行
发现数据差异: 1398531
行数不一致：df1有2行，df2有1行
发现数据差异: 1398411
行数不一致：df1有2行，df2有1行
发现数据差异: 1397892
行数不一致：df1有2行，df2有1行
发现数据差异: 1398621
行数不一致：df1有2行，df2有1行
发现数据差异: 1397891
行数不一致：df1有2行，df2有1行
发现数据差异: 1398755
行数不一致：df1有2行，df2有1行
发现数据差异: 1398629
行数不一致：df1有2行，df2有1行
发现数据差异: 1398862
行数不一致：df1有2行，df2有1行
发现数据差异: 1398874
行数不一致：df1有2行，df2有1行
发现数据差异: 1398637
行数不一致：df1有2行，df2有1行
发现数据差异: 1398633
行数不一致：df1有2行，df2有1行
发现数据差异: 1398325
行数不一致：df1有2行，df2有1行
发现数据差异: 1398518
行数不一致：df1有2行，df2有1行
发现数据差异: 1398870
行数不一致：df1有2行，df2有1行
发现数据差异: 1398327
行数不一致：df1有2行，df2有1行
发现数据差异: 1398982
行数不一致：df1有2行，df2有1行
发现数据差异: 1398448
行数不一致：df1有2行，df2有1行
发现数据差异: 1398689
行数不一致：df1有2行，df2有1行
发现数据差异: 1398757
行数不一致：df1有2行，df2有1行
发现数据差异: 1398519
行数不一致：df1有2行，df2有1行
发现数据差异: 1398321
行数不一致：df1有2行，df2有1行
发现数据差异: 1398625
行数不一致：df1有2行，df2有1行
发现数据差异: 1398447
行数不一致：df1有2行，df2有1行
发现数据差异: 1398566
行数不一致：df1有2行，df2有1行
发现数据差异: 1398568
行数不一致：df1有2行，df2有1行
发现数据差异: 1398323
行数不一致：df1有2行，df2有1行
发现数据差异: 1398685
行数不一致：df1有2行，df2有1行
发现数据差异: 1398562
行数不一致：df1有2行，df2有1行
发现数据差异: 1398443
行数不一致：df1有2行，df2有1行
发现数据差异: 1399305
行数不一致：df1有2行，df2有1行
发现数据差异: 1398442
行数不一致：df1有2行，df2有1行
发现数据差异: 1398560
行数不一致：df1有2行，df2有1行
发现数据差异: 1398329
行数不一致：df1有2行，df2有1行
发现数据差异: 1398578
行数不一致：df1有2行，df2有1行
发现数据差异: 1398681
行数不一致：df1有2行，df2有1行
发现数据差异: 1398444
行数不一致：df1有2行，df2有1行
发现数据差异: 1398456
行数不一致：df1有2行，df2有1行
发现数据差异: 1398337
行数不一致：df1有2行，df2有1行
发现数据差异: 1399303
行数不一致：df1有2行，df2有1行
发现数据差异: 1398459
行数不一致：df1有2行，df2有1行
发现数据差异: 1398440
行数不一致：df1有2行，df2有1行
发现数据差异: 1398331
行数不一致：df1有2行，df2有1行
发现数据差异: 1398217
行数不一致：df1有2行，df2有1行
发现数据差异: 1398458
行数不一致：df1有2行，df2有1行
发现数据差异: 1398576
行数不一致：df1有2行，df2有1行
发现数据差异: 1399306
行数不一致：df1有2行，df2有1行
发现数据差异: 1398453
行数不一致：df1有2行，df2有1行
发现数据差异: 1398333
行数不一致：df1有2行，df2有1行
发现数据差异: 1399549
行数不一致：df1有2行，df2有1行
发现数据差异: 1399302
行数不一致：df1有2行，df2有1行
发现数据差异: 1398213
行数不一致：df1有2行，df2有1行
发现数据差异: 1398454
行数不一致：df1有2行，df2有1行
发现数据差异: 1399309
行数不一致：df1有2行，df2有1行
发现数据差异: 1399304
行数不一致：df1有2行，df2有1行
发现数据差异: 1398570
行数不一致：df1有2行，df2有1行
发现数据差异: 1399300
行数不一致：df1有2行，df2有1行
发现数据差异: 1398303
行数不一致：df1有2行，df2有1行
发现数据差异: 1398305
行数不一致：df1有2行，df2有1行
发现数据差异: 1398691
行数不一致：df1有2行，df2有1行
发现数据差异: 1399515
行数不一致：df1有2行，df2有1行
发现数据差异: 1398693
行数不一致：df1有2行，df2有1行
发现数据差异: 1399307
行数不一致：df1有2行，df2有1行
发现数据差异: 1398541
行数不一致：df1有2行，df2有1行
发现数据差异: 1398545
行数不一致：df1有2行，df2有1行
发现数据差异: 1399513
行数不一致：df1有2行，df2有1行
发现数据差异: 1398547
行数不一致：df1有2行，df2有1行
发现数据差异: 1398301
行数不一致：df1有2行，df2有1行
发现数据差异: 1399511
行数不一致：df1有2行，df2有1行
发现数据差异: 1398797
行数不一致：df1有2行，df2有1行
发现数据差异: 1398438
行数不一致：df1有2行，df2有1行
发现数据差异: 1398543
行数不一致：df1有2行，df2有1行
发现数据差异: 1398793
行数不一致：df1有2行，df2有1行
发现数据差异: 1398677
行数不一致：df1有2行，df2有1行
发现数据差异: 1398071
行数不一致：df1有2行，df2有1行
发现数据差异: 1398559
行数不一致：df1有2行，df2有1行
发现数据差异: 1399407
行数不一致：df1有2行，df2有1行
发现数据差异: 1399093
行数不一致：df1有2行，df2有1行
发现数据差异: 1399403
行数不一致：df1有2行，df2有1行
发现数据差异: 1404099
行数不一致：df1有2行，df2有1行
发现数据差异: 1397913
行数不一致：df1有2行，df2有1行
发现数据差异: 1399097
行数不一致：df1有2行，df2有1行
发现数据差异: 1397911
行数不一致：df1有2行，df2有1行
发现数据差异: 1398193
行数不一致：df1有2行，df2有1行
发现数据差异: 1399161
行数不一致：df1有2行，df2有1行
发现数据差异: 1397927
行数不一致：df1有2行，df2有1行
发现数据差异: 1397915
行数不一致：df1有2行，df2有1行
发现数据差异: 1399280
行数不一致：df1有2行，df2有1行
发现数据差异: 1399190
行数不一致：df1有2行，df2有1行
发现数据差异: 1397923
行数不一致：df1有2行，df2有1行
发现数据差异: 1397921
行数不一致：df1有2行，df2有1行
发现数据差异: 1397919
行数不一致：df1有2行，df2有1行
发现数据差异: 1397917
行数不一致：df1有2行，df2有1行
发现数据差异: 1399071
行数不一致：df1有2行，df2有1行
发现数据差异: 1399075
行数不一致：df1有2行，df2有1行
发现数据差异: 1399087
行数不一致：df1有2行，df2有1行
发现数据差异: 1399085
行数不一致：df1有2行，df2有1行
发现数据差异: 1399079
行数不一致：df1有2行，df2有1行
发现数据差异: 1399081
行数不一致：df1有2行，df2有1行
发现数据差异: 1397957
行数不一致：df1有2行，df2有1行
发现数据差异: 1398928
行数不一致：df1有2行，df2有1行
发现数据差异: 1398808
行数不一致：df1有2行，df2有1行
发现数据差异: 1397834
行数不一致：df1有2行，df2有1行
发现数据差异: 1397961
行数不一致：df1有2行，df2有1行
发现数据差异: 1397842
行数不一致：df1有2行，df2有1行
发现数据差异: 1399083
行数不一致：df1有2行，df2有1行
发现数据差异: 1398801
行数不一致：df1有2行，df2有1行
发现数据差异: 1398805
行数不一致：df1有2行，df2有1行
发现数据差异: 1397953
行数不一致：df1有2行，df2有1行
发现数据差异: 1397838
行数不一致：df1有2行，df2有1行
发现数据差异: 1398810
行数不一致：df1有2行，df2有1行
发现数据差异: 1398931
行数不一致：df1有2行，df2有1行
发现数据差异: 1397848
行数不一致：df1有2行，df2有1行
发现数据差异: 1398930
行数不一致：df1有2行，df2有1行
发现数据差异: 1398936
行数不一致：df1有2行，df2有1行
发现数据差异: 1398816
行数不一致：df1有2行，df2有1行
发现数据差异: 1398814
行数不一致：df1有2行，df2有1行
发现数据差异: 1398933
行数不一致：df1有2行，df2有1行
发现数据差异: 1397846
行数不一致：df1有2行，df2有1行
发现数据差异: 1397931
行数不一致：df1有2行，df2有1行
发现数据差异: 1397935
行数不一致：df1有2行，df2有1行
发现数据差异: 1398938
行数不一致：df1有2行，df2有1行
发现数据差异: 1397816
行数不一致：df1有2行，df2有1行
发现数据差异: 1397876
行数不一致：df1有2行，df2有1行
发现数据差异: 1398818
行数不一致：df1有2行，df2有1行
发现数据差异: 1397874
行数不一致：df1有2行，df2有1行
发现数据差异: 1397949
行数不一致：df1有2行，df2有1行
发现数据差异: 1398723
行数不一致：df1有2行，df2有1行
发现数据差异: 1397997
行数不一致：df1有2行，df2有1行
发现数据差异: 1397945
行数不一致：df1有2行，df2有1行
发现数据差异: 1397995
行数不一致：df1有2行，df2有1行
发现数据差异: 1398812
行数不一致：df1有2行，df2有1行
发现数据差异: 1397821
行数不一致：df1有2行，df2有1行
发现数据差异: 1397870
行数不一致：df1有2行，df2有1行
发现数据差异: 1397991
行数不一致：df1有2行，df2有1行
发现数据差异: 1397872
行数不一致：df1有2行，df2有1行
发现数据差异: 1398607
行数不一致：df1有2行，df2有1行
发现数据差异: 1398729
行数不一致：df1有2行，df2有1行
发现数据差异: 1398605
行数不一致：df1有2行，df2有1行
发现数据差异: 1397993
行数不一致：df1有2行，df2有1行
发现数据差异: 1398609
行数不一致：df1有2行，df2有1行
发现数据差异: 1398603
行数不一致：df1有2行，df2有1行
发现数据差异: 1398727
行数不一致：df1有2行，df2有1行
发现数据差异: 1409956
行数不一致：df1有2行，df2有1行
发现数据差异: 1397880
行数不一致：df1有2行，df2有1行
发现数据差异: 1397887
行数不一致：df1有2行，df2有1行
发现数据差异: 1397882
行数不一致：df1有2行，df2有1行
发现数据差异: 1398731
行数不一致：df1有2行，df2有1行
发现数据差异: 1398970
行数不一致：df1有2行，df2有1行
发现数据差异: 1397878
行数不一致：df1有2行，df2有1行
发现数据差异: 1398733
行数不一致：df1有2行，df2有1行
发现数据差异: 1398725
行数不一致：df1有2行，df2有1行
发现数据差异: 1398739
行数不一致：df1有2行，df2有1行
发现数据差异: 1398978
行数不一致：df1有2行，df2有1行
发现数据差异: 1398942
行数不一致：df1有2行，df2有1行
发现数据差异: 1398974
行数不一致：df1有2行，df2有1行
发现数据差异: 1397850
行数不一致：df1有2行，df2有1行
发现数据差异: 1398735
行数不一致：df1有2行，df2有1行
发现数据差异: 1398940
行数不一致：df1有2行，df2有1行
发现数据差异: 1397854
行数不一致：df1有2行，df2有1行
发现数据差异: 1397852
行数不一致：df1有2行，df2有1行
发现数据差异: 1397889
行数不一致：df1有2行，df2有1行
发现数据差异: 1398826
行数不一致：df1有2行，df2有1行
发现数据差异: 1398822
行数不一致：df1有2行，df2有1行
发现数据差异: 1398946
行数不一致：df1有2行，df2有1行
发现数据差异: 1397858
行数不一致：df1有2行，df2有1行
发现数据差异: 1398711
行数不一致：df1有2行，df2有1行
发现数据差异: 1398832
行数不一致：df1有2行，df2有1行
发现数据差异: 1398828
行数不一致：df1有2行，df2有1行
发现数据差异: 1398709
行数不一致：df1有2行，df2有1行
发现数据差异: 1398836
行数不一致：df1有2行，df2有1行
发现数据差异: 1398958
行数不一致：df1有2行，df2有1行
发现数据差异: 1399218
行数不一致：df1有2行，df2有1行
发现数据差异: 1398127
行数不一致：df1有2行，df2有1行
发现数据差异: 1398954
行数不一致：df1有2行，df2有1行
发现数据差异: 1398820
行数不一致：df1有2行，df2有1行
发现数据差异: 1399459
行数不一致：df1有2行，df2有1行
发现数据差异: 1398950
行数不一致：df1有2行，df2有1行
发现数据差异: 1397862
行数不一致：df1有2行，df2有1行
发现数据差异: 1398243
行数不一致：df1有2行，df2有1行
发现数据差异: 1399339
行数不一致：df1有2行，df2有1行
发现数据差异: 1399455
行数不一致：df1有2行，df2有1行
发现数据差异: 1399333
行数不一致：df1有2行，df2有1行
发现数据差异: 1398123
行数不一致：df1有2行，df2有1行
发现数据差异: 1399349
行数不一致：df1有2行，df2有1行
发现数据差异: 1397866
行数不一致：df1有2行，df2有1行
发现数据差异: 1399331
行数不一致：df1有2行，df2有1行
发现数据差异: 1398125
行数不一致：df1有2行，df2有1行
发现数据差异: 1398129
行数不一致：df1有2行，df2有1行
发现数据差异: 1399214
行数不一致：df1有2行，df2有1行
发现数据差异: 1398379
行数不一致：df1有2行，df2有1行
发现数据差异: 1399105
行数不一致：df1有2行，df2有1行
发现数据差异: 1399589
行数不一致：df1有2行，df2有1行
发现数据差异: 1398245
行数不一致：df1有2行，df2有1行
发现数据差异: 1398019
行数不一致：df1有2行，df2有1行
发现数据差异: 1399226
行数不一致：df1有2行，df2有1行
发现数据差异: 1399585
行数不一致：df1有2行，df2有1行
发现数据差异: 1398255
行数不一致：df1有2行，df2有1行
发现数据差异: 1399345
行数不一致：df1有2行，df2有1行
发现数据差异: 1399101
行数不一致：df1有2行，df2有1行
发现数据差异: 1398139
行数不一致：df1有2行，df2有1行
发现数据差异: 1399467
行数不一致：df1有2行，df2有1行
发现数据差异: 1398373
行数不一致：df1有2行，df2有1行
发现数据差异: 1399109
行数不一致：df1有2行，df2有1行
发现数据差异: 1399222
行数不一致：df1有2行，df2有1行
发现数据差异: 1399343
行数不一致：df1有2行，df2有1行
发现数据差异: 1398377
行数不一致：df1有2行，df2有1行
发现数据差异: 1399559
行数不一致：df1有2行，df2有1行
发现数据差异: 1399555
行数不一致：df1有2行，df2有1行
发现数据差异: 1398250
行数不一致：df1有2行，df2有1行
发现数据差异: 1399581
行数不一致：df1有2行，df2有1行
发现数据差异: 1399463
行数不一致：df1有2行，df2有1行
发现数据差异: 1399313
行数不一致：df1有2行，df2有1行
发现数据差异: 1399311
行数不一致：df1有2行，df2有1行
发现数据差异: 1399341
行数不一致：df1有2行，df2有1行
发现数据差异: 1398103
行数不一致：df1有2行，df2有1行
发现数据差异: 1398347
行数不一致：df1有2行，df2有1行
发现数据差异: 1398349
行数不一致：df1有2行，df2有1行
发现数据差异: 1399437
行数不一致：df1有2行，df2有1行
发现数据差异: 1398375
行数不一致：df1有2行，df2有1行
发现数据差异: 1398341
行数不一致：df1有2行，df2有1行
发现数据差异: 1398345
行数不一致：df1有2行，df2有1行
发现数据差异: 1399551
行数不一致：df1有2行，df2有1行
发现数据差异: 1398239
行数不一致：df1有2行，df2有1行
发现数据差异: 1399327
行数不一致：df1有2行，df2有1行
发现数据差异: 1398460
行数不一致：df1有2行，df2有1行
发现数据差异: 1398357
行数不一致：df1有2行，df2有1行
发现数据差异: 1399325
行数不一致：df1有2行，df2有1行
发现数据差异: 1399329
行数不一致：df1有2行，df2有1行
发现数据差异: 1398231
行数不一致：df1有2行，df2有1行
发现数据差异: 1398234
行数不一致：df1有2行，df2有1行
发现数据差异: 1398355
行数不一致：df1有2行，df2有1行
发现数据差异: 1398287
行数不一致：df1有2行，df2有1行
发现数据差异: 1398590
行数不一致：df1有2行，df2有1行
发现数据差异: 1398283
行数不一致：df1有2行，df2有1行
发现数据差异: 1399497
行数不一致：df1有2行，df2有1行
发现数据差异: 1399377
行数不一致：df1有2行，df2有1行
发现数据差异: 1399323
行数不一致：df1有2行，df2有1行
发现数据差异: 1398593
行数不一致：df1有2行，df2有1行
发现数据差异: 1398233
行数不一致：df1有2行，df2有1行
发现数据差异: 1399493
行数不一致：df1有2行，df2有1行
发现数据差异: 1399375
行数不一致：df1有2行，df2有1行
发现数据差异: 1399137
行数不一致：df1有2行，df2有1行
发现数据差异: 1399019
行数不一致：df1有2行，df2有1行
发现数据差异: 1399441
行数不一致：df1有2行，df2有1行
发现数据差异: 1399567
行数不一致：df1有2行，df2有1行
发现数据差异: 1398169
行数不一致：df1有2行，df2有1行
发现数据差异: 1398165
行数不一致：df1有2行，df2有1行
发现数据差异: 1399149
行数不一致：df1有2行，df2有1行
发现数据差异: 1398057
行数不一致：df1有2行，df2有1行
发现数据差异: 1399371
行数不一致：df1有2行，df2有1行
发现数据差异: 1398177
行数不一致：df1有2行，df2有1行
发现数据差异: 1399252
行数不一致：df1有2行，df2有1行
发现数据差异: 1399145
行数不一致：df1有2行，df2有1行
发现数据差异: 1399140
行数不一致：df1有2行，df2有1行
发现数据差异: 1398179
行数不一致：df1有2行，df2有1行
发现数据差异: 1399385
行数不一致：df1有2行，df2有1行
发现数据差异: 1399389行数不一致：df1有2行，df2有1行
发现数据差异: 1399387

行数不一致：df1有2行，df2有1行
发现数据差异: 1399256
行数不一致：df1有2行，df2有1行
发现数据差异: 1399264
行数不一致：df1有2行，df2有1行
发现数据差异: 1398175
行数不一致：df1有2行，df2有1行
发现数据差异: 1399260
行数不一致：df1有2行，df2有1行
发现数据差异: 1398053
行数不一致：df1有2行，df2有1行
发现数据差异: 1399386
行数不一致：df1有2行，df2有1行
发现数据差异: 1398291
行数不一致：df1有2行，df2有1行
发现数据差异: 1399381
行数不一致：df1有2行，df2有1行
发现数据差异: 1398149
行数不一致：df1有2行，df2有1行
发现数据差异: 1398297
行数不一致：df1有2行，df2有1行
发现数据差异: 1399023
行数不一致：df1有2行，df2有1行
发现数据差异: 1398293
行数不一致：df1有2行，df2有1行
发现数据差异: 1398295
行数不一致：df1有2行，df2有1行
发现数据差异: 1399479
行数不一致：df1有2行，df2有1行
发现数据差异: 1399117
行数不一致：df1有2行，df2有1行
发现数据差异: 1398645
行数不一致：df1有2行，df2有1行
发现数据差异: 1399359
行数不一致：df1有2行，df2有1行
发现数据差异: 1398765
行数不一致：df1有2行，df2有1行
发现数据差异: 1399477
行数不一致：df1有2行，df2有1行
发现数据差异: 1398401行数不一致：df1有2行，df2有1行
发现数据差异: 1398526

行数不一致：df1有2行，df2有1行
发现数据差异: 1398520
行数不一致：df1有2行，df2有1行
发现数据差异: 1398521
行数不一致：df1有2行，df2有1行
发现数据差异: 1398649
行数不一致：df1有2行，df2有1行
发现数据差异: 1398409行数不一致：df1有2行，df2有1行
发现数据差异: 1399475

行数不一致：df1有2行，df2有1行
发现数据差异: 1398769
行数不一致：df1有2行，df2有1行
发现数据差异: 1398641
行数不一致：df1有2行，df2有1行
发现数据差异: 1398777
行数不一致：df1有2行，df2有1行
发现数据差异: 1398524
行数不一致：df1有2行，df2有1行
发现数据差异: 1398529
行数不一致：df1有2行，df2有1行
发现数据差异: 1404100
行数不一致：df1有2行，df2有1行
发现数据差异: 1398655
行数不一致：df1有2行，df2有1行
发现数据差异: 1399503
行数不一致：df1有2行，df2有1行
发现数据差异: 1398896
行数不一致：df1有2行，df2有1行
发现数据差异: 1398773
行数不一致：df1有2行，df2有1行
发现数据差异: 1398894
行数不一致：df1有2行，df2有1行
发现数据差异: 1398621
行数不一致：df1有2行，df2有1行
发现数据差异: 1398982
行数不一致：df1有2行，df2有1行
发现数据差异: 1398866
行数不一致：df1有2行，df2有1行
发现数据差异: 1398531
行数不一致：df1有2行，df2有1行
发现数据差异: 1398862
行数不一致：df1有2行，df2有1行
发现数据差异: 1398625
行数不一致：df1有2行，df2有1行
发现数据差异: 1398755
行数不一致：df1有2行，df2有1行
发现数据差异: 1398539
行数不一致：df1有2行，df2有1行
发现数据差异: 1398537
行数不一致：df1有2行，df2有1行
发现数据差异: 1399507
行数不一致：df1有2行，df2有1行
发现数据差异: 1398651
行数不一致：df1有2行，df2有1行
发现数据差异: 1398411
行数不一致：df1有2行，df2有1行
发现数据差异: 1397892
行数不一致：df1有2行，df2有1行
发现数据差异: 1397891
行数不一致：df1有2行，df2有1行
发现数据差异: 1398874
行数不一致：df1有2行，df2有1行
发现数据差异: 1398629
行数不一致：df1有2行，df2有1行
发现数据差异: 1398757
行数不一致：df1有2行，df2有1行
发现数据差异: 1398633
行数不一致：df1有2行，df2有1行
发现数据差异: 1398519
行数不一致：df1有2行，df2有1行
发现数据差异: 1398327
行数不一致：df1有2行，df2有1行
发现数据差异: 1398870
行数不一致：df1有2行，df2有1行
发现数据差异: 1398637
行数不一致：df1有2行，df2有1行
发现数据差异: 1398442
行数不一致：df1有2行，df2有1行
发现数据差异: 1398689
行数不一致：df1有2行，df2有1行
发现数据差异: 1398566
行数不一致：df1有2行，df2有1行
发现数据差异: 1398518
行数不一致：df1有2行，df2有1行
发现数据差异: 1398447
行数不一致：df1有2行，df2有1行
发现数据差异: 1398685
行数不一致：df1有2行，df2有1行
发现数据差异: 1398321
行数不一致：df1有2行，df2有1行
发现数据差异: 1398568
行数不一致：df1有2行，df2有1行
发现数据差异: 1398448
行数不一致：df1有2行，df2有1行
发现数据差异: 1398443
行数不一致：df1有2行，df2有1行
发现数据差异: 1399305
行数不一致：df1有2行，df2有1行
发现数据差异: 1398323
行数不一致：df1有2行，df2有1行
发现数据差异: 1398560
行数不一致：df1有2行，df2有1行
发现数据差异: 1398444
行数不一致：df1有2行，df2有1行
发现数据差异: 1398562
行数不一致：df1有2行，df2有1行
发现数据差异: 1398578
行数不一致：df1有2行，df2有1行
发现数据差异: 1398440
行数不一致：df1有2行，df2有1行
发现数据差异: 1398456
行数不一致：df1有2行，df2有1行
发现数据差异: 1398681
行数不一致：df1有2行，df2有1行
发现数据差异: 1398217
行数不一致：df1有2行，df2有1行
发现数据差异: 1398329
行数不一致：df1有2行，df2有1行
发现数据差异: 1398337
行数不一致：df1有2行，df2有1行
发现数据差异: 1399303
行数不一致：df1有2行，df2有1行
发现数据差异: 1398325
行数不一致：df1有2行，df2有1行
发现数据差异: 1398453
行数不一致：df1有2行，df2有1行
发现数据差异: 1399304
行数不一致：df1有2行，df2有1行
发现数据差异: 1398576
行数不一致：df1有2行，df2有1行
发现数据差异: 1399309
行数不一致：df1有2行，df2有1行
发现数据差异: 1399302
行数不一致：df1有2行，df2有1行
发现数据差异: 1398458
行数不一致：df1有2行，df2有1行
发现数据差异: 1398459
行数不一致：df1有2行，df2有1行
发现数据差异: 1399300
行数不一致：df1有2行，df2有1行
发现数据差异: 1398454
行数不一致：df1有2行，df2有1行
发现数据差异: 1399306
行数不一致：df1有2行，df2有1行
发现数据差异: 1399307
行数不一致：df1有2行，df2有1行
发现数据差异: 1398691
行数不一致：df1有2行，df2有1行
发现数据差异: 1398333
行数不一致：df1有2行，df2有1行
发现数据差异: 1399549
行数不一致：df1有2行，df2有1行
发现数据差异: 1398570
行数不一致：df1有2行，df2有1行
发现数据差异: 1398213
行数不一致：df1有2行，df2有1行
发现数据差异: 1398331
行数不一致：df1有2行，df2有1行
发现数据差异: 1398303
行数不一致：df1有2行，df2有1行
发现数据差异: 1398547
行数不一致：df1有2行，df2有1行
发现数据差异: 1398693
行数不一致：df1有2行，df2有1行
发现数据差异: 1399515
行数不一致：df1有2行，df2有1行
发现数据差异: 1398301
行数不一致：df1有2行，df2有1行
发现数据差异: 1398545
行数不一致：df1有2行，df2有1行
发现数据差异: 1399511
行数不一致：df1有2行，df2有1行
发现数据差异: 1399513
行数不一致：df1有2行，df2有1行
发现数据差异: 1398677
行数不一致：df1有2行，df2有1行
发现数据差异: 1398797
行数不一致：df1有2行，df2有1行
发现数据差异: 1399403
行数不一致：df1有2行，df2有1行
发现数据差异: 1398541
行数不一致：df1有2行，df2有1行
发现数据差异: 1399407
行数不一致：df1有2行，df2有1行
发现数据差异: 1398543
行数不一致：df1有2行，df2有1行
发现数据差异: 1398438
行数不一致：df1有2行，df2有1行
发现数据差异: 1398147
行数不一致：df1有2行，df2有1行
发现数据差异: 1398793
行数不一致：df1有2行，df2有1行
发现数据差异: 1398559
行数不一致：df1有2行，df2有1行
发现数据差异: 1399473
行数不一致：df1有2行，df2有1行
发现数据差异: 1399111
行数不一致：df1有2行，df2有1行
发现数据差异: 1398268
行数不一致：df1有2行，df2有1行
发现数据差异: 1398380
行数不一致：df1有2行，df2有1行
发现数据差异: 1398305
行数不一致：df1有2行，df2有1行
发现数据差异: 1399125
行数不一致：df1有2行，df2有1行
发现数据差异: 1399471
行数不一致：df1有2行，df2有1行
发现数据差异: 1398260
行数不一致：df1有2行，df2有1行
发现数据差异: 1399351
行数不一致：df1有2行，df2有1行
发现数据差异: 1398397
行数不一致：df1有2行，df2有1行
发现数据差异: 1399129
行数不一致：df1有2行，df2有1行
发现数据差异: 1398264
行数不一致：df1有2行，df2有1行
发现数据差异: 1398381
行数不一致：df1有2行，df2有1行
发现数据差异: 1399127
行数不一致：df1有2行，df2有1行
发现数据差异: 1399123
行数不一致：df1有2行，df2有1行
发现数据差异: 1399489
行数不一致：df1有2行，df2有1行
发现数据差异: 1398395
行数不一致：df1有2行，df2有1行
发现数据差异: 1399367
行数不一致：df1有2行，df2有1行
发现数据差异: 1399485
行数不一致：df1有2行，df2有1行
发现数据差异: 1399121
行数不一致：df1有2行，df2有1行
发现数据差异: 1398393
行数不一致：df1有2行，df2有1行
发现数据差异: 1398153
行数不一致：df1有2行，df2有1行
发现数据差异: 1399363
行数不一致：df1有2行，df2有1行
发现数据差异: 1399481
行数不一致：df1有2行，df2有1行
发现数据差异: 1398272
行数不一致：df1有2行，df2有1行
发现数据差异: 1399298
行数不一致：df1有2行，df2有1行
发现数据差异: 1399178
行数不一致：df1有2行，df2有1行
发现数据差异: 1398081
行数不一致：df1有2行，df2有1行
发现数据差异: 1399173
行数不一致：df1有2行，df2有1行
发现数据差异: 1399189
行数不一致：df1有2行，df2有1行
发现数据差异: 1398151
行数不一致：df1有2行，df2有1行
发现数据差异: 1399294
行数不一致：df1有2行，df2有1行
发现数据差异: 1399175
行数不一致：df1有2行，df2有1行
发现数据差异: 1399290
行数不一致：df1有2行，df2有1行
发现数据差异: 1399066
行数不一致：df1有2行，df2有1行
发现数据差异: 1399296
行数不一致：df1有2行，df2有1行
发现数据差异: 1398181
行数不一致：df1有2行，df2有1行
发现数据差异: 1399278
行数不一致：df1有2行，df2有1行
发现数据差异: 1399394
行数不一致：df1有2行，df2有1行
发现数据差异: 1399292
行数不一致：df1有2行，df2有1行
发现数据差异: 1399063
行数不一致：df1有2行，df2有1行
发现数据差异: 1399185
行数不一致：df1有2行，df2有1行
发现数据差异: 1399398
行数不一致：df1有2行，df2有1行
发现数据差异: 1398061
行数不一致：df1有2行，df2有1行
发现数据差异: 1399391
行数不一致：df1有2行，df2有1行
发现数据差异: 1398077
行数不一致：df1有2行，df2有1行
发现数据差异: 1399181
行数不一致：df1有2行，df2有1行
发现数据差异: 1398199
行数不一致：df1有2行，df2有1行
发现数据差异: 1398065
行数不一致：df1有2行，df2有1行
发现数据差异: 1399046
行数不一致：df1有2行，df2有1行
发现数据差异: 1399286
行数不一致：df1有2行，df2有1行
发现数据差异: 1399091
行数不一致：df1有2行，df2有1行
发现数据差异: 1399061
行数不一致：df1有2行，df2有1行
发现数据差异: 1399044
行数不一致：df1有2行，df2有1行
发现数据差异: 1399041
行数不一致：df1有2行，df2有1行
发现数据差异: 1397925
行数不一致：df1有2行，df2有1行
发现数据差异: 1399099
行数不一致：df1有2行，df2有1行
发现数据差异: 1397907
行数不一致：df1有2行，df2有1行
发现数据差异: 1399070
行数不一致：df1有2行，df2有1行
发现数据差异: 1399095
行数不一致：df1有2行，df2有1行
发现数据差异: 1399074
行数不一致：df1有2行，df2有1行
发现数据差异: 1397837
行数不一致：df1有2行，df2有1行
发现数据差异: 1398920
行数不一致：df1有2行，df2有1行
发现数据差异: 1397830
行数不一致：df1有2行，df2有1行
发现数据差异: 1399040
行数不一致：df1有2行，df2有1行
发现数据差异: 1399282
行数不一致：df1有2行，df2有1行
发现数据差异: 1399078
行数不一致：df1有2行，df2有1行
发现数据差异: 1397903
行数不一致：df1有2行，df2有1行
发现数据差异: 1397952
行数不一致：df1有2行，df2有1行
发现数据差异: 1399089
行数不一致：df1有2行，df2有1行
发现数据差异: 1398924
行数不一致：df1有2行，df2有1行
发现数据差异: 1398800
行数不一致：df1有2行，df2有1行
发现数据差异: 1397833
行数不一致：df1有2行，df2有1行
发现数据差异: 1397901
行数不一致：df1有2行，df2有1行
发现数据差异: 1409506
行数不一致：df1有2行，df2有1行
发现数据差异: 1398922
行数不一致：df1有2行，df2有1行
发现数据差异: 1398926
行数不一致：df1有2行，df2有1行
发现数据差异: 1397840
行数不一致：df1有2行，df2有1行
发现数据差异: 1397956
行数不一致：df1有2行，df2有1行
发现数据差异: 1397969
行数不一致：df1有2行，df2有1行
发现数据差异: 1398804
行数不一致：df1有2行，df2有1行
发现数据差异: 1397845
行数不一致：df1有2行，df2有1行
发现数据差异: 1398906
行数不一致：df1有2行，df2有1行
发现数据差异: 1397967
行数不一致：df1有2行，df2有1行
发现数据差异: 1397939
行数不一致：df1有2行，df2有1行
发现数据差异: 1397818
行数不一致：df1有2行，df2有1行
发现数据差异: 1397820
行数不一致：df1有2行，df2有1行
发现数据差异: 1397933
行数不一致：df1有2行，df2有1行
发现数据差异: 1397960
行数不一致：df1有2行，df2有1行
发现数据差异: 1398904
行数不一致：df1有2行，df2有1行
发现数据差异: 1398902
行数不一致：df1有2行，df2有1行
发现数据差异: 1397948
行数不一致：df1有2行，df2有1行
发现数据差异: 1404088
行数不一致：df1有2行，df2有1行
发现数据差异: 1398910
行数不一致：df1有2行，df2有1行
发现数据差异: 1398908
行数不一致：df1有2行，df2有1行
发现数据差异: 1399339
行数不一致：df1有2行，df2有1行
发现数据差异: 1397828
行数不一致：df1有2行，df2有1行
发现数据差异: 1398900
行数不一致：df1有2行，df2有1行
发现数据差异: 1399455
行数不一致：df1有2行，df2有1行
发现数据差异: 1398127
行数不一致：df1有2行，df2有1行
发现数据差异: 1398129
行数不一致：df1有2行，df2有1行
发现数据差异: 1399218
行数不一致：df1有2行，df2有1行
发现数据差异: 1397929
行数不一致：df1有2行，df2有1行
发现数据差异: 1398243
行数不一致：df1有2行，df2有1行
发现数据差异: 1399214
行数不一致：df1有2行，df2有1行
发现数据差异: 1404080
行数不一致：df1有2行，df2有1行
发现数据差异: 1398125
行数不一致：df1有2行，df2有1行
发现数据差异: 1397941
行数不一致：df1有2行，df2有1行
发现数据差异: 1398379
行数不一致：df1有2行，df2有1行
发现数据差异: 1399105
行数不一致：df1有2行，df2有1行
发现数据差异: 1398123
行数不一致：df1有2行，df2有1行
发现数据差异: 1399589
行数不一致：df1有2行，df2有1行
发现数据差异: 1398245
行数不一致：df1有2行，df2有1行
发现数据差异: 1399331
行数不一致：df1有2行，df2有1行
发现数据差异: 1398019
行数不一致：df1有2行，df2有1行
发现数据差异: 1398255
行数不一致：df1有2行，df2有1行
发现数据差异: 1399459
行数不一致：df1有2行，df2有1行
发现数据差异: 1399226
行数不一致：df1有2行，df2有1行
发现数据差异: 1399333
行数不一致：df1有2行，df2有1行
发现数据差异: 1398375
行数不一致：df1有2行，df2有1行
发现数据差异: 1398139
行数不一致：df1有2行，df2有1行
发现数据差异: 1399349
行数不一致：df1有2行，df2有1行
发现数据差异: 1399585
行数不一致：df1有2行，df2有1行
发现数据差异: 1399101
行数不一致：df1有2行，df2有1行
发现数据差异: 1399345
行数不一致：df1有2行，df2有1行
发现数据差异: 1398377
行数不一致：df1有2行，df2有1行
发现数据差异: 1399581
行数不一致：df1有2行，df2有1行
发现数据差异: 1399222
行数不一致：df1有2行，df2有1行
发现数据差异: 1399341
行数不一致：df1有2行，df2有1行
发现数据差异: 1399467
行数不一致：df1有2行，df2有1行
发现数据差异: 1399559
行数不一致：df1有2行，df2有1行
发现数据差异: 1399463
行数不一致：df1有2行，df2有1行
发现数据差异: 1399109
行数不一致：df1有2行，df2有1行
发现数据差异: 1399343
行数不一致：df1有2行，df2有1行
发现数据差异: 1399313
行数不一致：df1有2行，df2有1行
发现数据差异: 1399555
行数不一致：df1有2行，df2有1行
发现数据差异: 1398347
行数不一致：df1有2行，df2有1行
发现数据差异: 1398103
行数不一致：df1有2行，df2有1行
发现数据差异: 1398345行数不一致：df1有2行，df2有1行
发现数据差异: 1398373

行数不一致：df1有2行，df2有1行
发现数据差异: 1398250
行数不一致：df1有2行，df2有1行
发现数据差异: 1399437
行数不一致：df1有2行，df2有1行
发现数据差异: 1398349
行数不一致：df1有2行，df2有1行
发现数据差异: 1399551
行数不一致：df1有2行，df2有1行
发现数据差异: 1398460
行数不一致：df1有2行，df2有1行
发现数据差异: 1398341
行数不一致：df1有2行，df2有1行
发现数据差异: 1399567
行数不一致：df1有2行，df2有1行
发现数据差异: 1399327
行数不一致：df1有2行，df2有1行
发现数据差异: 1398239
行数不一致：df1有2行，df2有1行
发现数据差异: 1399325
行数不一致：df1有2行，df2有1行
发现数据差异: 1398357
行数不一致：df1有2行，df2有1行
发现数据差异: 1399311
行数不一致：df1有2行，df2有1行
发现数据差异: 1399323
行数不一致：df1有2行，df2有1行
发现数据差异: 1398355
行数不一致：df1有2行，df2有1行
发现数据差异: 1398234
行数不一致：df1有2行，df2有1行
发现数据差异: 1399441
行数不一致：df1有2行，df2有1行
发现数据差异: 1398233
行数不一致：df1有2行，df2有1行
发现数据差异: 1399019
行数不一致：df1有2行，df2有1行
发现数据差异: 1398593
行数不一致：df1有2行，df2有1行
发现数据差异: 1398231
行数不一致：df1有2行，df2有1行
发现数据差异: 1399329
行数不一致：df1有2行，df2有1行
发现数据差异: 1399497
行数不一致：df1有2行，df2有1行
发现数据差异: 1398169行数不一致：df1有2行，df2有1行
发现数据差异: 1399375
行数不一致：df1有2行，df2有1行
发现数据差异: 1399377

行数不一致：df1有2行，df2有1行
发现数据差异: 1399256
行数不一致：df1有2行，df2有1行
发现数据差异: 1399137
行数不一致：df1有2行，df2有1行
发现数据差异: 1399371
行数不一致：df1有2行，df2有1行
发现数据差异: 1398590
行数不一致：df1有2行，df2有1行
发现数据差异: 1399149
行数不一致：df1有2行，df2有1行
发现数据差异: 1398165
行数不一致：df1有2行，df2有1行
发现数据差异: 1398287
行数不一致：df1有2行，df2有1行
发现数据差异: 1399389
行数不一致：df1有2行，df2有1行
发现数据差异: 1398283
行数不一致：df1有2行，df2有1行
发现数据差异: 1398177
行数不一致：df1有2行，df2有1行
发现数据差异: 1399252
行数不一致：df1有2行，df2有1行
发现数据差异: 1398057
行数不一致：df1有2行，df2有1行
发现数据差异: 1399493
行数不一致：df1有2行，df2有1行
发现数据差异: 1398179
行数不一致：df1有2行，df2有1行
发现数据差异: 1398295
行数不一致：df1有2行，df2有1行
发现数据差异: 1399145
行数不一致：df1有2行，df2有1行
发现数据差异: 1399386
行数不一致：df1有2行，df2有1行
发现数据差异: 1398297
行数不一致：df1有2行，df2有1行
发现数据差异: 1399387
行数不一致：df1有2行，df2有1行
发现数据差异: 1399264
行数不一致：df1有2行，df2有1行
发现数据差异: 1399385
行数不一致：df1有2行，df2有1行
发现数据差异: 1398149
行数不一致：df1有2行，df2有1行
发现数据差异: 1398053
行数不一致：df1有2行，df2有1行
发现数据差异: 1398175
行数不一致：df1有2行，df2有1行
发现数据差异: 1399260
行数不一致：df1有2行，df2有1行
发现数据差异: 1399023
行数不一致：df1有2行，df2有1行
发现数据差异: 1399140
行数不一致：df1有2行，df2有1行
发现数据差异: 1399479
行数不一致：df1有2行，df2有1行
发现数据差异: 1398293
行数不一致：df1有2行，df2有1行
发现数据差异: 1398291
行数不一致：df1有2行，df2有1行
发现数据差异: 1399359
行数不一致：df1有2行，df2有1行
发现数据差异: 1399117
行数不一致：df1有2行，df2有1行
发现数据差异: 1399477
行数不一致：df1有2行，df2有1行
发现数据差异: 1399475
行数不一致：df1有2行，df2有1行
发现数据差异: 1399381
行数不一致：df1有2行，df2有1行
发现数据差异: 1398268
行数不一致：df1有2行，df2有1行
发现数据差异: 1399471
行数不一致：df1有2行，df2有1行
发现数据差异: 1399473
行数不一致：df1有2行，df2有1行
发现数据差异: 1398260
行数不一致：df1有2行，df2有1行
发现数据差异: 1399111
行数不一致：df1有2行，df2有1行
发现数据差异: 1398264
行数不一致：df1有2行，df2有1行
发现数据差异: 1398380
行数不一致：df1有2行，df2有1行
发现数据差异: 1399127
行数不一致：df1有2行，df2有1行
发现数据差异: 1399367
行数不一致：df1有2行，df2有1行
发现数据差异: 1398147
行数不一致：df1有2行，df2有1行
发现数据差异: 1398381
行数不一致：df1有2行，df2有1行
发现数据差异: 1399121
行数不一致：df1有2行，df2有1行
发现数据差异: 1399351
行数不一致：df1有2行，df2有1行
发现数据差异: 1399125
行数不一致：df1有2行，df2有1行
发现数据差异: 1399129
行数不一致：df1有2行，df2有1行
发现数据差异: 1399363
行数不一致：df1有2行，df2有1行
发现数据差异: 1398397
行数不一致：df1有2行，df2有1行
发现数据差异: 1399489
行数不一致：df1有2行，df2有1行
发现数据差异: 1399481
行数不一致：df1有2行，df2有1行
发现数据差异: 1399290行数不一致：df1有2行，df2有1行
发现数据差异: 1399123

行数不一致：df1有2行，df2有1行
发现数据差异: 1398393
行数不一致：df1有2行，df2有1行
发现数据差异: 1399178
行数不一致：df1有2行，df2有1行
发现数据差异: 1399298
行数不一致：df1有2行，df2有1行
发现数据差异: 1398153
行数不一致：df1有2行，df2有1行
发现数据差异: 1399175
行数不一致：df1有2行，df2有1行
发现数据差异: 1399173
行数不一致：df1有2行，df2有1行
发现数据差异: 1399485
行数不一致：df1有2行，df2有1行
发现数据差异: 1398395
行数不一致：df1有2行，df2有1行
发现数据差异: 1399296
行数不一致：df1有2行，df2有1行
发现数据差异: 1399292
行数不一致：df1有2行，df2有1行
发现数据差异: 1398272
行数不一致：df1有2行，df2有1行
发现数据差异: 1399066
行数不一致：df1有2行，df2有1行
发现数据差异: 1399181
行数不一致：df1有2行，df2有1行
发现数据差异: 1398081
行数不一致：df1有2行，df2有1行
发现数据差异: 1399394
行数不一致：df1有2行，df2有1行
发现数据差异: 1399185
行数不一致：df1有2行，df2有1行
发现数据差异: 1399391
行数不一致：df1有2行，df2有1行
发现数据差异: 1399189
行数不一致：df1有2行，df2有1行
发现数据差异: 1399061
行数不一致：df1有2行，df2有1行
发现数据差异: 1398065
行数不一致：df1有2行，df2有1行
发现数据差异: 1399063
行数不一致：df1有2行，df2有1行
发现数据差异: 1398181
行数不一致：df1有2行，df2有1行
发现数据差异: 1398151
行数不一致：df1有2行，df2有1行
发现数据差异: 1399278
行数不一致：df1有2行，df2有1行
发现数据差异: 1398061
行数不一致：df1有2行，df2有1行
发现数据差异: 1398199
行数不一致：df1有2行，df2有1行
发现数据差异: 1399294
行数不一致：df1有2行，df2有1行
发现数据差异: 1399286
行数不一致：df1有2行，df2有1行
发现数据差异: 1399398行数不一致：df1有2行，df2有1行
发现数据差异: 1398077

行数不一致：df1有2行，df2有1行
发现数据差异: 1399091
行数不一致：df1有2行，df2有1行
发现数据差异: 1397925
行数不一致：df1有2行，df2有1行
发现数据差异: 1399095
行数不一致：df1有2行，df2有1行
发现数据差异: 1399046
行数不一致：df1有2行，df2有1行
发现数据差异: 1399044
行数不一致：df1有2行，df2有1行
发现数据差异: 1399041
行数不一致：df1有2行，df2有1行
发现数据差异: 1399040
行数不一致：df1有2行，df2有1行
发现数据差异: 1397907
行数不一致：df1有2行，df2有1行
发现数据差异: 1399078
行数不一致：df1有2行，df2有1行
发现数据差异: 1399282
行数不一致：df1有2行，df2有1行
发现数据差异: 1397901
行数不一致：df1有2行，df2有1行
发现数据差异: 1399074
行数不一致：df1有2行，df2有1行
发现数据差异: 1399099
行数不一致：df1有2行，df2有1行
发现数据差异: 1397903
行数不一致：df1有2行，df2有1行
发现数据差异: 1399089
行数不一致：df1有2行，df2有1行
发现数据差异: 1397830
行数不一致：df1有2行，df2有1行
发现数据差异: 1398920
行数不一致：df1有2行，df2有1行
发现数据差异: 1409506
行数不一致：df1有2行，df2有1行
发现数据差异: 1398804
行数不一致：df1有2行，df2有1行
发现数据差异: 1397969
行数不一致：df1有2行，df2有1行
发现数据差异: 1398800
行数不一致：df1有2行，df2有1行
发现数据差异: 1397840
行数不一致：df1有2行，df2有1行
发现数据差异: 1399070
行数不一致：df1有2行，df2有1行
发现数据差异: 1397952
行数不一致：df1有2行，df2有1行
发现数据差异: 1398924
行数不一致：df1有2行，df2有1行
发现数据差异: 1398922
行数不一致：df1有2行，df2有1行
发现数据差异: 1398906
行数不一致：df1有2行，df2有1行
发现数据差异: 1397956
行数不一致：df1有2行，df2有1行
发现数据差异: 1397845
行数不一致：df1有2行，df2有1行
发现数据差异: 1398926
行数不一致：df1有2行，df2有1行
发现数据差异: 1398904
行数不一致：df1有2行，df2有1行
发现数据差异: 1397818
行数不一致：df1有2行，df2有1行
发现数据差异: 1397929
行数不一致：df1有2行，df2有1行
发现数据差异: 1398902
行数不一致：df1有2行，df2有1行
发现数据差异: 1397960
行数不一致：df1有2行，df2有1行
发现数据差异: 1404080
行数不一致：df1有2行，df2有1行
发现数据差异: 1397939
行数不一致：df1有2行，df2有1行
发现数据差异: 1397837
行数不一致：df1有2行，df2有1行
发现数据差异: 1397828
行数不一致：df1有2行，df2有1行
发现数据差异: 1397833
行数不一致：df1有2行，df2有1行
发现数据差异: 1398900
行数不一致：df1有2行，df2有1行
发现数据差异: 1404088
行数不一致：df1有2行，df2有1行
发现数据差异: 1397967
行数不一致：df1有2行，df2有1行
发现数据差异: 1397948
行数不一致：df1有2行，df2有1行
发现数据差异: 1397933
行数不一致：df1有2行，df2有1行
发现数据差异: 1398910
行数不一致：df1有2行，df2有1行
发现数据差异: 1398908
行数不一致：df1有2行，df2有1行
发现数据差异: 1397941
行数不一致：df1有2行，df2有1行
发现数据差异: 1397820
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1398392
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 23600
  df_parse_data: 22600

发现数据差异: 1401500
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 23600
  df_parse_data: 22600

发现数据差异: 1401499
行数不一致：df1有2行，df2有1行
发现数据差异: 1440334
行数不一致：df1有2行，df2有1行
发现数据差异: 1442624
行数不一致：df1有2行，df2有1行
发现数据差异: 1442333
行数不一致：df1有2行，df2有1行
发现数据差异: 1442640
行数不一致：df1有2行，df2有1行
发现数据差异: 1442084
行数不一致：df1有2行，df2有1行
发现数据差异: 1442497
行数不一致：df1有2行，df2有1行
发现数据差异: 1443601
行数不一致：df1有2行，df2有1行
发现数据差异: 1440354
行数不一致：df1有2行，df2有1行
发现数据差异: 1440447
行数不一致：df1有2行，df2有1行
发现数据差异: 1441788
行数不一致：df1有2行，df2有1行
发现数据差异: 1442090
行数不一致：df1有2行，df2有1行
发现数据差异: 1457525
行数不一致：df1有2行，df2有1行
发现数据差异: 1442642
行数不一致：df1有2行，df2有1行
发现数据差异: 1441872
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1364500
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1363573
处理 computed_time 列时出现异常: time data "days" doesn't match format "%H:%M:%S", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
缺少知识库数据: 1366661
行数不一致：df1有2行，df2有1行
发现数据差异: 1443296
行数不一致：df1有2行，df2有1行
发现数据差异: 1443321
行数不一致：df1有2行，df2有1行
发现数据差异: 1441477
行数不一致：df1有2行，df2有1行
发现数据差异: 1441011
行数不一致：df1有2行，df2有1行
发现数据差异: 1442626
行数不一致：df1有2行，df2有1行
发现数据差异: 1442090
行数不一致：df1有2行，df2有1行
发现数据差异: 1442410
行数不一致：df1有2行，df2有1行
发现数据差异: 1442084
行数不一致：df1有2行，df2有1行
发现数据差异: 1457525
行数不一致：df1有2行，df2有1行
发现数据差异: 1442497
行数不一致：df1有2行，df2有1行
发现数据差异: 1442333
行数不一致：df1有2行，df2有1行
发现数据差异: 1440354
行数不一致：df1有2行，df2有1行
发现数据差异: 1441788
行数不一致：df1有2行，df2有1行
发现数据差异: 1443601
行数不一致：df1有2行，df2有1行
发现数据差异: 1442640
行数不一致：df1有2行，df2有1行
发现数据差异: 1442642
行数不一致：df1有2行，df2有1行
发现数据差异: 1440447
行数不一致：df1有2行，df2有1行
发现数据差异: 1441872
行数不一致：df1有2行，df2有1行
发现数据差异: 1443296
行数不一致：df1有2行，df2有1行
发现数据差异: 1443321
行数不一致：df1有2行，df2有1行
发现数据差异: 1441011
行数不一致：df1有2行，df2有1行
发现数据差异: 1442410
行数不一致：df1有2行，df2有1行
发现数据差异: 1441477
行数不一致：df1有2行，df2有1行
发现数据差异: 1442626
发现以下差异：
行 0, 列 'max_fl':
  df_kf_data: 24600
  df_parse_data: 23600

发现数据差异: 1440953
全部任务处理完成

Process finished with exit code 0
