(['/Users/<USER>/PycharmProjects/workPro/新FOC/不断连接vpn.py'],
 ['/Users/<USER>/PycharmProjects/workPro'],
 [],
 [('/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/numpy/_pyinstaller',
   0),
  ('/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.3 (v3.11.3:f3909b8bc8, Apr  4 2023, 20:12:10) [Clang 13.0.0 '
 '(clang-1300.0.29.30)]',
 [('pyi_rth_inspect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('不断连接vpn',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/不断连接vpn.py',
   'PYSOURCE')],
 [('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/subprocess.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/selectors.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xmlrpc/__init__.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gzip.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/textwrap.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/copy.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gettext.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/request.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fnmatch.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/netrc.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shlex.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/mimetypes.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getopt.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/utils.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/encoders.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/quopri.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/calendar.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/__init__.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/error.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/string.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hashlib.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/parser.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/feedparser.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/header.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bisect.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/parse.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextvars.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/numbers.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/base64.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hmac.py',
   'PYMODULE'),
  ('struct',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/struct.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/socket.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shutil.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_text.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/csv.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tokenize.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/token.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pathlib.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/util.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tarfile.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lzma.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bz2.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dataclasses.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/inspect.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dis.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/opcode.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/heap.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('queue',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/runpy.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/__init__.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_py_abc.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/commondialog.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/exc.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/elements.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/result.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/row.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/ddl.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/base.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/impl.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/events.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/registry.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/base.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/api.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/legacy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/url.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/reflection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/log.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/util.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/default.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/sqltypes.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/uuid.py',
   'PYMODULE'),
  ('platform',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/plistlib.py',
   'PYMODULE'),
  ('json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/scanner.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/functions.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/inspection.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/visitors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/base.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/compiler.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/constants.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/naming.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/dependency.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/compat.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/schema.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/context.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/future/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/future/engine.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/state.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/exc.py',
   'PYMODULE'),
  ('__future__',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/__future__.py',
   'PYMODULE'),
  ('sqlalchemy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/pysqlite.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('sqlite3',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sqlite3/dump.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/connectors/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/connectors/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/aiomysql.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/__init__.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/optionfile.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/configparser.py',
   'PYMODULE'),
  ('pymysql.cursors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/cursors.py',
   'PYMODULE'),
  ('pymysql.charset',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/times.py',
   'PYMODULE'),
  ('pymysql.err',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/err.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/compiler.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/baked.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/types.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_threading_local.py',
   'PYMODULE'),
  ('tkinter',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/constants.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/statistics.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fractions.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_strptime.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/datetime.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.11/Python',
   '/Library/Frameworks/Python.framework/Versions/3.11/Python',
   'BINARY'),
  ('lib-dynload/grp.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/grp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/math.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/select.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/fcntl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixshmem.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multiprocessing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/zlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/pyexpat.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_scproxy.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/termios.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/binascii.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ssl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_hashlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha3.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_blake2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha512.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha256.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_md5.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha1.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bisect.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/unicodedata.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_contextvars.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_decimal.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_struct.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/array.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_socket.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_typing.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_typing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_csv.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/resource.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_lzma.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bz2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_opcode.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_pickle.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/mmap.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ctypes.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_queue.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multibytecodec.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_jp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_kr.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_cn.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_tw.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_hk.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_heapq.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/resultproxy.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/resultproxy.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/util.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/util.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/processors.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/processors.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/immutabledict.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/immutabledict.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/collections.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/collections.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_uuid.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_json.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_asyncio.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sqlite3.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_tkinter.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_statistics.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_random.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_datetime.cpython-311-darwin.so',
   'EXTENSION'),
  ('libcrypto.1.1.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libcrypto.1.1.dylib',
   'BINARY'),
  ('libssl.1.1.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libssl.1.1.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libtk8.6.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libtcl8.6.dylib',
   'BINARY')],
 [],
 [],
 [('_tcl_data/encoding/macCentEuro.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tk_data/ttk/winTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/winTheme.tcl',
   'DATA'),
  ('_tk_data/choosedir.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/choosedir.tcl',
   'DATA'),
  ('_tk_data/palette.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/palette.tcl',
   'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/it_ch.msg',
   'DATA'),
  ('_tcl_data/msgs/es_bo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_bo.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_cl.msg',
   'DATA'),
  ('_tk_data/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/pl.msg',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/aquaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tcl_data/msgs/fa_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fa_in.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/hi_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hi_in.msg',
   'DATA'),
  ('_tcl_data/msgs/kw_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kw_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/kok.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kok.msg',
   'DATA'),
  ('_tk_data/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tk_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/ru.msg',
   'DATA'),
  ('_tk_data/ttk/fonts.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/fonts.tcl',
   'DATA'),
  ('_tk_data/focus.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/focus.tcl',
   'DATA'),
  ('_tk_data/console.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/console.tcl',
   'DATA'),
  ('_tk_data/optMenu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/optMenu.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/da.msg',
   'DATA'),
  ('_tk_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/es.msg',
   'DATA'),
  ('_tk_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/msgs/sl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sl.msg',
   'DATA'),
  ('_tcl_data/msgs/ko.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ko.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/fr_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr_ch.msg',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tk_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/da.msg',
   'DATA'),
  ('_tk_data/mkpsenc.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/mkpsenc.tcl',
   'DATA'),
  ('_tk_data/xmfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/xmfbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/gv_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gv_gb.msg',
   'DATA'),
  ('_tcl_data/safe.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/safe.tcl',
   'DATA'),
  ('_tcl_data/parray.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/parray.tcl',
   'DATA'),
  ('_tcl_data/encoding/tis-620.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('_tcl_data/msgs/nn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nn.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_tw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_tw.msg',
   'DATA'),
  ('_tcl_data/msgs/ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ca.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_lb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_lb.msg',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tk_data/ttk/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/ja.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ja.msg',
   'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tk_data/ttk/ttk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/ttk.tcl',
   'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tk_data/fontchooser.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/fontchooser.tcl',
   'DATA'),
  ('_tcl_data/msgs/bg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/bg.msg',
   'DATA'),
  ('_tcl_data/msgs/ms_my.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ms_my.msg',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('_tcl_data/msgs/bn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/bn.msg',
   'DATA'),
  ('_tk_data/msgs/en.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/en.msg',
   'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tk_data/bgerror.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/bgerror.tcl',
   'DATA'),
  ('_tcl_data/msgs/ro.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ro.msg',
   'DATA'),
  ('_tcl_data/msgs/he.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/he.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tcl_data/msgs/es_cr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_cr.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_pa.msg',
   'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tcl_data/msgs/zh_cn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_be.msg',
   'DATA'),
  ('_tcl_data/msgs/es_co.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_co.msg',
   'DATA'),
  ('_tcl_data/msgs/sq.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sq.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/msgs/en_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_ie.msg',
   'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tk_data/tk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tk.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr_be.msg',
   'DATA'),
  ('_tcl_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tk_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_sy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_sy.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_in.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_jo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_jo.msg',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/spinbox.tcl',
   'DATA'),
  ('_tk_data/text.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/text.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('_tk_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tk_data/tkAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tkAppInit.c',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('tcl8/8.6/http-2.9.5.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.6/http-2.9.5.tm',
   'DATA'),
  ('tcl8/8.5/tcltest-2.5.3.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.5/tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data/msgs/kl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kl.msg',
   'DATA'),
  ('_tk_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ve.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/de_at.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/de_at.msg',
   'DATA'),
  ('_tcl_data/msgs/vi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/vi.msg',
   'DATA'),
  ('_tk_data/ttk/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/button.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_sg.msg',
   'DATA'),
  ('_tcl_data/msgs/kok_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kok_in.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tcl_data/msgs/is.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/is.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ar.msg',
   'DATA'),
  ('_tcl_data/msgs/ta.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ta.msg',
   'DATA'),
  ('_tcl_data/msgs/es_uy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_uy.msg',
   'DATA'),
  ('_tk_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/de.msg',
   'DATA'),
  ('_tk_data/ttk/utils.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/utils.tcl',
   'DATA'),
  ('_tcl_data/msgs/ms.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ms.msg',
   'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tcl_data/msgs/be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/be.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tk_data/images/tai-ku.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/tai-ku.gif',
   'DATA'),
  ('_tcl_data/msgs/lt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/lt.msg',
   'DATA'),
  ('_tcl_data/msgs/uk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/uk.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/clamTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/mr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mr.msg',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ec.msg',
   'DATA'),
  ('_tcl_data/msgs/sw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sw.msg',
   'DATA'),
  ('_tcl_data/msgs/fa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fa.msg',
   'DATA'),
  ('_tk_data/images/logo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logo.eps',
   'DATA'),
  ('_tcl_data/word.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/word.tcl',
   'DATA'),
  ('_tcl_data/http1.0/http.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/http1.0/http.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/msgs/ga_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ga_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gl.msg',
   'DATA'),
  ('_tk_data/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/msgs/mr_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mr_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tk_data/images/logo64.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logo64.gif',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.5/msgcat-1.6.1.tm',
   'DATA'),
  ('tcl8/8.4/platform-1.0.18.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.4/platform-1.0.18.tm',
   'DATA'),
  ('_tk_data/listbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/listbox.tcl',
   'DATA'),
  ('_tk_data/menu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/menu.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tk_data/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/spinbox.tcl',
   'DATA'),
  ('_tk_data/iconlist.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/iconlist.tcl',
   'DATA'),
  ('_tcl_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/auto.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/auto.tcl',
   'DATA'),
  ('_tcl_data/msgs/mk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mk.msg',
   'DATA'),
  ('_tcl_data/msgs/hi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hi.msg',
   'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ni.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ni.msg',
   'DATA'),
  ('_tk_data/ttk/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/nb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nb.msg',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/de_be.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tcl_data/msgs/ru_ua.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ru_ua.msg',
   'DATA'),
  ('_tcl_data/msgs/th.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/th.msg',
   'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/fa_ir.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fa_ir.msg',
   'DATA'),
  ('_tcl_data/tm.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/tm.tcl',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/scrollbar.tcl',
   'DATA'),
  ('_tk_data/msgbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgbox.tcl',
   'DATA'),
  ('_tk_data/images/README',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/README',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_zw.msg',
   'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/eu_es.msg',
   'DATA'),
  ('_tcl_data/msgs/fo_fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fo_fo.msg',
   'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tcl_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sv.msg',
   'DATA'),
  ('_tk_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tclIndex',
   'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tk_data/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_au.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_au.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pe.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_pe.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tcl_data/msgs/bn_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/bn_in.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_sg.msg',
   'DATA'),
  ('_tcl_data/msgs/en_bw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_bw.msg',
   'DATA'),
  ('_tcl_data/msgs/en_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_za.msg',
   'DATA'),
  ('_tcl_data/msgs/zh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh.msg',
   'DATA'),
  ('_tk_data/images/logo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/af_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/af_za.msg',
   'DATA'),
  ('_tk_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/el.msg',
   'DATA'),
  ('_tk_data/dialog.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/dialog.tcl',
   'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tk_data/comdlg.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/comdlg.tcl',
   'DATA'),
  ('_tk_data/ttk/cursors.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/cursors.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_sv.msg',
   'DATA'),
  ('_tcl_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/init.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/init.tcl',
   'DATA'),
  ('_tk_data/icons.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/icons.tcl',
   'DATA'),
  ('_tk_data/tkfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tkfbox.tcl',
   'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tcl_data/msgs/en_nz.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_nz.msg',
   'DATA'),
  ('_tcl_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/af.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/af.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('tcl8/8.6/tdbc/sqlite3-1.1.3.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.6/tdbc/sqlite3-1.1.3.tm',
   'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tcl_data/msgs/gv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gv.msg',
   'DATA'),
  ('_tk_data/ttk/sizegrip.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/sizegrip.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tcl_data/msgs/es_do.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_do.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_ca.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tk_data/ttk/xpTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/xpTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_ph.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_ph.msg',
   'DATA'),
  ('_tk_data/unsupported.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/unsupported.tcl',
   'DATA'),
  ('_tcl_data/msgs/pt_br.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/pt_br.msg',
   'DATA'),
  ('_tcl_data/msgs/ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tcl_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/tclIndex',
   'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tk_data/tearoff.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tearoff.tcl',
   'DATA'),
  ('_tcl_data/msgs/ta_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ta_in.msg',
   'DATA'),
  ('_tcl_data/msgs/te_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/te_in.msg',
   'DATA'),
  ('_tcl_data/msgs/id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/id.msg',
   'DATA'),
  ('_tcl_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/gl_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gl_es.msg',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/treeview.tcl',
   'DATA'),
  ('_tcl_data/msgs/et.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/et.msg',
   'DATA'),
  ('_tcl_data/msgs/es_hn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_hn.msg',
   'DATA'),
  ('_tcl_data/msgs/es_py.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_py.msg',
   'DATA'),
  ('_tcl_data/msgs/tr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/tr.msg',
   'DATA'),
  ('_tk_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tk_data/ttk/progress.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/progress.tcl',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tcl_data/msgs/sh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sh.msg',
   'DATA'),
  ('_tk_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nl_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tcl_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/msgs/te.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/te.msg',
   'DATA'),
  ('_tcl_data/msgs/fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fo.msg',
   'DATA'),
  ('_tk_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/sv.msg',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tcl_data/msgs/sk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sk.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr.msg',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/notebook.tcl',
   'DATA'),
  ('_tcl_data/msgs/lv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/lv.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tk_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/eo.msg',
   'DATA'),
  ('_tk_data/scrlbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/scrlbar.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_mx.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_mx.msg',
   'DATA'),
  ('_tcl_data/msgs/es_gt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_gt.msg',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data/msgs/fr_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr_ca.msg',
   'DATA'),
  ('_tcl_data/msgs/ga.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ga.msg',
   'DATA'),
  ('_tk_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/history.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/history.tcl',
   'DATA'),
  ('_tcl_data/package.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/package.tcl',
   'DATA'),
  ('_tk_data/megawidget.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/megawidget.tcl',
   'DATA'),
  ('_tcl_data/msgs/id_id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/id_id.msg',
   'DATA'),
  ('_tcl_data/msgs/hr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hr.msg',
   'DATA'),
  ('_tk_data/ttk/altTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/altTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es.msg',
   'DATA'),
  ('_tk_data/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/button.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tcl_data/msgs/eu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/eu.msg',
   'DATA'),
  ('_tk_data/ttk/defaults.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/defaults.tcl',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tcl_data/msgs/es_pr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_pr.msg',
   'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tcl_data/msgs/sr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sr.msg',
   'DATA'),
  ('_tcl_data/msgs/kl_gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kl_gl.msg',
   'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tcl_data/msgs/mt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mt.msg',
   'DATA'),
  ('_tcl_data/clock.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/clock.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tcl_data/tclAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/tclAppInit.c',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tcl_data/msgs/kw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kw.msg',
   'DATA'),
  ('_tk_data/clrpick.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/clrpick.tcl',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tk_data/safetk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/safetk.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tcl_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ko_kr.msg',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('_tk_data/obsolete.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/obsolete.tcl',
   'DATA'),
  ('_tcl_data/msgs/fi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fi.msg',
   'DATA'),
  ('_tk_data/ttk/combobox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/combobox.tcl',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.11/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.11/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.11/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.11', 'SYMLINK')])
