('/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/PYZ-00.pyz',
 [('__future__',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compression.py',
   'PYMODULE'),
  ('_py_abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/argparse.py',
   'PYMODULE'),
  ('ast',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ast.py',
   'PYMODULE'),
  ('asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.log',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/windows_utils.py',
   'PYMODULE'),
  ('base64',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/base64.py',
   'PYMODULE'),
  ('bisect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/calendar.py',
   'PYMODULE'),
  ('concurrent',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/thread.py',
   'PYMODULE'),
  ('configparser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/configparser.py',
   'PYMODULE'),
  ('contextlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/copy.py',
   'PYMODULE'),
  ('csv',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/csv.py',
   'PYMODULE'),
  ('ctypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/_endian.py',
   'PYMODULE'),
  ('dataclasses',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/decimal.py',
   'PYMODULE'),
  ('dis',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dis.py',
   'PYMODULE'),
  ('email',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fractions.py',
   'PYMODULE'),
  ('ftplib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ftplib.py',
   'PYMODULE'),
  ('getopt',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getopt.py',
   'PYMODULE'),
  ('getpass',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getpass.py',
   'PYMODULE'),
  ('gettext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gettext.py',
   'PYMODULE'),
  ('gzip',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hashlib.py',
   'PYMODULE'),
  ('hmac',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hmac.py',
   'PYMODULE'),
  ('http',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/__init__.py',
   'PYMODULE'),
  ('http.client',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/cookiejar.py',
   'PYMODULE'),
  ('importlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/inspect.py',
   'PYMODULE'),
  ('json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lzma.py',
   'PYMODULE'),
  ('mimetypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/util.py',
   'PYMODULE'),
  ('netrc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/netrc.py',
   'PYMODULE'),
  ('nturl2path',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/nturl2path.py',
   'PYMODULE'),
  ('numbers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/opcode.py',
   'PYMODULE'),
  ('pathlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pathlib.py',
   'PYMODULE'),
  ('pickle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pickle.py',
   'PYMODULE'),
  ('pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pkgutil.py',
   'PYMODULE'),
  ('platform',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/plistlib.py',
   'PYMODULE'),
  ('pprint',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/py_compile.py',
   'PYMODULE'),
  ('pymysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/constants/SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/pymysql/times.py',
   'PYMODULE'),
  ('queue',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/queue.py',
   'PYMODULE'),
  ('quopri',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/quopri.py',
   'PYMODULE'),
  ('random',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py',
   'PYMODULE'),
  ('runpy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/runpy.py',
   'PYMODULE'),
  ('secrets',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/secrets.py',
   'PYMODULE'),
  ('selectors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/selectors.py',
   'PYMODULE'),
  ('shlex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shlex.py',
   'PYMODULE'),
  ('shutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shutil.py',
   'PYMODULE'),
  ('signal',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/signal.py',
   'PYMODULE'),
  ('socket',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/socket.py',
   'PYMODULE'),
  ('sqlalchemy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/connectors/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/connectors/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mssql/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/mysql/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/oracle/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/postgresql/types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/dialects/sqlite/pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/engine/util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/event/registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/ext/compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/future/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/future/engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/orm/writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/pool/impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/sql/visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/util/typing.py',
   'PYMODULE'),
  ('sqlite3',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sqlite3/__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sqlite3/dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sqlite3/dump.py',
   'PYMODULE'),
  ('ssl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ssl.py',
   'PYMODULE'),
  ('statistics',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/statistics.py',
   'PYMODULE'),
  ('string',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/string.py',
   'PYMODULE'),
  ('stringprep',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py',
   'PYMODULE'),
  ('tkinter',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tkinter/messagebox.py',
   'PYMODULE'),
  ('token',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/token.py',
   'PYMODULE'),
  ('tokenize',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/typing.py',
   'PYMODULE'),
  ('typing_extensions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.error',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/error.py',
   'PYMODULE'),
  ('urllib.parse',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/parse.py',
   'PYMODULE'),
  ('urllib.request',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/request.py',
   'PYMODULE'),
  ('urllib.response',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/response.py',
   'PYMODULE'),
  ('uuid',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/uuid.py',
   'PYMODULE'),
  ('xml',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.sax',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xmlrpc/__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xmlrpc/client.py',
   'PYMODULE'),
  ('zipfile',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipfile.py',
   'PYMODULE'),
  ('zipimport',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipimport.py',
   'PYMODULE')])
