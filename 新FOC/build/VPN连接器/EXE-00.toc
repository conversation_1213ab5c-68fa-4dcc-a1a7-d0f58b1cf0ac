('/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/VPN连接器',
 False,
 False,
 True,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 'arm64',
 None,
 None,
 '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/VPN连接器.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_struct.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/zlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('struct',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('不断连接vpn',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/不断连接vpn.py',
   'PYSOURCE')],
 [],
 False,
 False,
 1741275407,
 [('runw',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/PyInstaller/bootloader/Darwin-64bit/runw',
   'EXECUTABLE')],
 '/Library/Frameworks/Python.framework/Versions/3.11/Python')
