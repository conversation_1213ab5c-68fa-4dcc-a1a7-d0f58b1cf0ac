([('VPN连接器',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/VPN连接器',
   'EXECUTABLE'),
  ('lib-dynload/_struct.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_struct.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/zlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('Python.framework/Versions/3.11/Python',
   '/Library/Frameworks/Python.framework/Versions/3.11/Python',
   'BINARY'),
  ('lib-dynload/grp.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/grp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/math.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/select.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/fcntl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixshmem.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multiprocessing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/pyexpat.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_scproxy.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/termios.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/binascii.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ssl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_hashlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha3.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_blake2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha512.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha256.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_md5.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha1.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bisect.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/unicodedata.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_contextvars.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_decimal.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/array.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_socket.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_typing.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_typing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_csv.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/resource.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_lzma.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bz2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_opcode.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_pickle.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/mmap.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ctypes.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_queue.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multibytecodec.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_jp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_kr.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_cn.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_tw.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_hk.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_heapq.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/resultproxy.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/resultproxy.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/util.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/util.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/processors.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/processors.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/immutabledict.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/immutabledict.cpython-311-darwin.so',
   'EXTENSION'),
  ('sqlalchemy/cyextension/collections.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site-packages/sqlalchemy/cyextension/collections.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_uuid.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_uuid.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_json.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_asyncio.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sqlite3.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sqlite3.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_tkinter.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_tkinter.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_statistics.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_random.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-311-darwin.so',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_datetime.cpython-311-darwin.so',
   'EXTENSION'),
  ('libcrypto.1.1.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libcrypto.1.1.dylib',
   'BINARY'),
  ('libssl.1.1.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libssl.1.1.dylib',
   'BINARY'),
  ('libtk8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libtk8.6.dylib',
   'BINARY'),
  ('libtcl8.6.dylib',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/libtcl8.6.dylib',
   'BINARY'),
  ('_tcl_data/encoding/macCentEuro.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macCentEuro.enc',
   'DATA'),
  ('_tk_data/ttk/winTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/winTheme.tcl',
   'DATA'),
  ('_tk_data/choosedir.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/choosedir.tcl',
   'DATA'),
  ('_tk_data/palette.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/palette.tcl',
   'DATA'),
  ('_tcl_data/encoding/dingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/dingbats.enc',
   'DATA'),
  ('_tcl_data/msgs/it_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/it_ch.msg',
   'DATA'),
  ('_tcl_data/msgs/es_bo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_bo.msg',
   'DATA'),
  ('_tcl_data/msgs/es_cl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_cl.msg',
   'DATA'),
  ('_tk_data/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/pl.msg',
   'DATA'),
  ('_tk_data/ttk/aquaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/aquaTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/encoding/macIceland.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macIceland.enc',
   'DATA'),
  ('_tcl_data/msgs/fa_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fa_in.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_hk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_hk.msg',
   'DATA'),
  ('_tcl_data/msgs/hi_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hi_in.msg',
   'DATA'),
  ('_tcl_data/msgs/kw_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kw_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/kok.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kok.msg',
   'DATA'),
  ('_tk_data/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/encoding/macRomania.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macRomania.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data/encoding/macDingbats.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macDingbats.enc',
   'DATA'),
  ('_tk_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/ru.msg',
   'DATA'),
  ('_tk_data/ttk/fonts.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/fonts.tcl',
   'DATA'),
  ('_tk_data/focus.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/focus.tcl',
   'DATA'),
  ('_tk_data/console.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/console.tcl',
   'DATA'),
  ('_tk_data/optMenu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/optMenu.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp860.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp860.enc',
   'DATA'),
  ('_tcl_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/da.msg',
   'DATA'),
  ('_tk_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/es.msg',
   'DATA'),
  ('_tk_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/msgs/sl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sl.msg',
   'DATA'),
  ('_tcl_data/msgs/ko.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ko.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso2022-jp.enc',
   'DATA'),
  ('_tcl_data/msgs/fr_ch.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr_ch.msg',
   'DATA'),
  ('_tcl_data/encoding/cp861.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp861.enc',
   'DATA'),
  ('_tk_data/msgs/da.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/da.msg',
   'DATA'),
  ('_tk_data/mkpsenc.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/mkpsenc.tcl',
   'DATA'),
  ('_tk_data/xmfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/xmfbox.tcl',
   'DATA'),
  ('_tcl_data/msgs/gv_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gv_gb.msg',
   'DATA'),
  ('_tcl_data/safe.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/safe.tcl',
   'DATA'),
  ('_tcl_data/parray.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/parray.tcl',
   'DATA'),
  ('_tcl_data/encoding/tis-620.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/tis-620.enc',
   'DATA'),
  ('_tcl_data/msgs/nn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nn.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_tw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_tw.msg',
   'DATA'),
  ('_tcl_data/msgs/ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ca.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_lb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_lb.msg',
   'DATA'),
  ('_tcl_data/msgs/en_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp866.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp866.enc',
   'DATA'),
  ('_tk_data/ttk/entry.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/entry.tcl',
   'DATA'),
  ('_tcl_data/msgs/ja.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ja.msg',
   'DATA'),
  ('_tcl_data/encoding/gb2312.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb2312.enc',
   'DATA'),
  ('_tk_data/ttk/ttk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/ttk.tcl',
   'DATA'),
  ('_tcl_data/encoding/macUkraine.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macUkraine.enc',
   'DATA'),
  ('_tk_data/fontchooser.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/fontchooser.tcl',
   'DATA'),
  ('_tcl_data/msgs/bg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/bg.msg',
   'DATA'),
  ('_tcl_data/msgs/ms_my.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ms_my.msg',
   'DATA'),
  ('_tcl_data/encoding/macRoman.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macRoman.enc',
   'DATA'),
  ('_tcl_data/msgs/bn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/bn.msg',
   'DATA'),
  ('_tk_data/msgs/en.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/en.msg',
   'DATA'),
  ('_tcl_data/encoding/cp855.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp855.enc',
   'DATA'),
  ('_tk_data/bgerror.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/bgerror.tcl',
   'DATA'),
  ('_tcl_data/msgs/ro.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ro.msg',
   'DATA'),
  ('_tcl_data/msgs/he.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/he.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-4.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-4.enc',
   'DATA'),
  ('_tcl_data/msgs/es_cr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_cr.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_pa.msg',
   'DATA'),
  ('_tk_data/images/logoMed.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logoMed.gif',
   'DATA'),
  ('_tcl_data/msgs/zh_cn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_cn.msg',
   'DATA'),
  ('_tcl_data/encoding/jis0201.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/jis0201.enc',
   'DATA'),
  ('_tcl_data/encoding/cp850.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp850.enc',
   'DATA'),
  ('_tcl_data/msgs/en_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_be.msg',
   'DATA'),
  ('_tcl_data/msgs/es_co.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_co.msg',
   'DATA'),
  ('_tcl_data/msgs/sq.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sq.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-6.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-6.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-10.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-10.enc',
   'DATA'),
  ('_tcl_data/msgs/en_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_ie.msg',
   'DATA'),
  ('_tk_data/ttk/menubutton.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/menubutton.tcl',
   'DATA'),
  ('_tk_data/tk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tk.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr_be.msg',
   'DATA'),
  ('_tcl_data/msgs/ru.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ru.msg',
   'DATA'),
  ('_tcl_data/encoding/cp737.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp737.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo200.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data/encoding/macCyrillic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macCyrillic.enc',
   'DATA'),
  ('_tk_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_sy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_sy.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-9.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-9.enc',
   'DATA'),
  ('_tcl_data/encoding/cp862.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp862.enc',
   'DATA'),
  ('_tk_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_in.msg',
   'DATA'),
  ('_tcl_data/msgs/ar_jo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar_jo.msg',
   'DATA'),
  ('_tk_data/ttk/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/spinbox.tcl',
   'DATA'),
  ('_tk_data/text.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/text.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1256.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1256.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo175.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo175.gif',
   'DATA'),
  ('_tk_data/ttk/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/panedwindow.tcl',
   'DATA'),
  ('_tk_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/fr.msg',
   'DATA'),
  ('_tcl_data/encoding/ksc5601.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/ksc5601.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1257.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1257.enc',
   'DATA'),
  ('_tk_data/tkAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tkAppInit.c',
   'DATA'),
  ('_tcl_data/encoding/cp865.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp865.enc',
   'DATA'),
  ('tcl8/8.6/http-2.9.5.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.6/http-2.9.5.tm',
   'DATA'),
  ('tcl8/8.5/tcltest-2.5.3.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.5/tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data/msgs/kl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kl.msg',
   'DATA'),
  ('_tk_data/msgs/pl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/pl.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ve.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ve.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-3.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-3.enc',
   'DATA'),
  ('_tcl_data/msgs/it.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/it.msg',
   'DATA'),
  ('_tcl_data/msgs/de_at.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/de_at.msg',
   'DATA'),
  ('_tcl_data/msgs/vi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/vi.msg',
   'DATA'),
  ('_tk_data/ttk/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/button.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_sg.msg',
   'DATA'),
  ('_tcl_data/msgs/kok_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kok_in.msg',
   'DATA'),
  ('_tcl_data/encoding/iso2022.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso2022.enc',
   'DATA'),
  ('_tcl_data/msgs/is.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/is.msg',
   'DATA'),
  ('_tcl_data/msgs/es_ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ar.msg',
   'DATA'),
  ('_tcl_data/msgs/ta.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ta.msg',
   'DATA'),
  ('_tcl_data/msgs/es_uy.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_uy.msg',
   'DATA'),
  ('_tk_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/de.msg',
   'DATA'),
  ('_tk_data/ttk/utils.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/utils.tcl',
   'DATA'),
  ('_tcl_data/msgs/ms.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ms.msg',
   'DATA'),
  ('_tcl_data/encoding/ebcdic.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/ebcdic.enc',
   'DATA'),
  ('_tcl_data/encoding/cp775.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp775.enc',
   'DATA'),
  ('_tcl_data/msgs/be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/be.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-16.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-16.enc',
   'DATA'),
  ('_tk_data/images/tai-ku.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/tai-ku.gif',
   'DATA'),
  ('_tcl_data/msgs/lt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/lt.msg',
   'DATA'),
  ('_tcl_data/msgs/uk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/uk.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo150.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data/encoding/gb1988.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb1988.enc',
   'DATA'),
  ('_tk_data/images/pwrdLogo75.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo75.gif',
   'DATA'),
  ('_tk_data/ttk/clamTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/clamTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/mr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mr.msg',
   'DATA'),
  ('_tcl_data/encoding/cp869.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp869.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ec.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ec.msg',
   'DATA'),
  ('_tcl_data/msgs/sw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sw.msg',
   'DATA'),
  ('_tcl_data/msgs/fa.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fa.msg',
   'DATA'),
  ('_tk_data/images/logo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logo.eps',
   'DATA'),
  ('_tcl_data/word.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/word.tcl',
   'DATA'),
  ('_tcl_data/http1.0/http.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/http1.0/http.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1250.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1250.enc',
   'DATA'),
  ('_tcl_data/msgs/ga_ie.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ga_ie.msg',
   'DATA'),
  ('_tcl_data/msgs/gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gl.msg',
   'DATA'),
  ('_tk_data/panedwindow.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/panedwindow.tcl',
   'DATA'),
  ('_tcl_data/msgs/mr_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mr_in.msg',
   'DATA'),
  ('_tcl_data/encoding/cp863.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp863.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-14.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-14.enc',
   'DATA'),
  ('_tk_data/images/logo64.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logo64.gif',
   'DATA'),
  ('tcl8/8.5/msgcat-1.6.1.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.5/msgcat-1.6.1.tm',
   'DATA'),
  ('tcl8/8.4/platform-1.0.18.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.4/platform-1.0.18.tm',
   'DATA'),
  ('_tk_data/listbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/listbox.tcl',
   'DATA'),
  ('_tk_data/menu.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/menu.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-7.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-7.enc',
   'DATA'),
  ('_tk_data/ttk/vistaTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/vistaTheme.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp949.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp949.enc',
   'DATA'),
  ('_tk_data/spinbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/spinbox.tcl',
   'DATA'),
  ('_tk_data/iconlist.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/iconlist.tcl',
   'DATA'),
  ('_tcl_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/auto.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/auto.tcl',
   'DATA'),
  ('_tcl_data/msgs/mk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mk.msg',
   'DATA'),
  ('_tcl_data/msgs/hi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hi.msg',
   'DATA'),
  ('_tcl_data/encoding/big5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/big5.enc',
   'DATA'),
  ('_tcl_data/msgs/es_ni.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_ni.msg',
   'DATA'),
  ('_tk_data/ttk/scale.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/scale.tcl',
   'DATA'),
  ('_tcl_data/msgs/nb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nb.msg',
   'DATA'),
  ('_tcl_data/msgs/de_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/de_be.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-13.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-13.enc',
   'DATA'),
  ('_tcl_data/msgs/ru_ua.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ru_ua.msg',
   'DATA'),
  ('_tcl_data/msgs/th.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/th.msg',
   'DATA'),
  ('_tcl_data/encoding/macThai.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macThai.enc',
   'DATA'),
  ('_tk_data/ttk/classicTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/classicTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/fa_ir.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fa_ir.msg',
   'DATA'),
  ('_tcl_data/tm.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/tm.tcl',
   'DATA'),
  ('_tk_data/ttk/scrollbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/scrollbar.tcl',
   'DATA'),
  ('_tk_data/msgbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgbox.tcl',
   'DATA'),
  ('_tk_data/images/README',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/README',
   'DATA'),
  ('_tcl_data/msgs/en_zw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_zw.msg',
   'DATA'),
  ('_tcl_data/encoding/gb12345.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb12345.enc',
   'DATA'),
  ('_tcl_data/encoding/cp936.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp936.enc',
   'DATA'),
  ('_tcl_data/msgs/eu_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/eu_es.msg',
   'DATA'),
  ('_tcl_data/msgs/fo_fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fo_fo.msg',
   'DATA'),
  ('_tcl_data/encoding/cp950.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp950.enc',
   'DATA'),
  ('_tcl_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sv.msg',
   'DATA'),
  ('_tk_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tclIndex',
   'DATA'),
  ('_tcl_data/opt0.4/optparse.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/opt0.4/optparse.tcl',
   'DATA'),
  ('_tcl_data/encoding/koi8-r.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/koi8-r.enc',
   'DATA'),
  ('_tk_data/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_au.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_au.msg',
   'DATA'),
  ('_tcl_data/msgs/es_pe.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_pe.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-1.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-1.enc',
   'DATA'),
  ('_tcl_data/encoding/cp437.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp437.enc',
   'DATA'),
  ('_tcl_data/msgs/bn_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/bn_in.msg',
   'DATA'),
  ('_tcl_data/msgs/zh_sg.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh_sg.msg',
   'DATA'),
  ('_tcl_data/msgs/en_bw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_bw.msg',
   'DATA'),
  ('_tcl_data/msgs/en_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_za.msg',
   'DATA'),
  ('_tcl_data/msgs/zh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/zh.msg',
   'DATA'),
  ('_tk_data/images/logo100.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logo100.gif',
   'DATA'),
  ('_tcl_data/encoding/gb2312-raw.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/gb2312-raw.enc',
   'DATA'),
  ('_tcl_data/msgs/af_za.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/af_za.msg',
   'DATA'),
  ('_tk_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/el.msg',
   'DATA'),
  ('_tk_data/dialog.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/dialog.tcl',
   'DATA'),
  ('_tcl_data/encoding/ascii.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/ascii.enc',
   'DATA'),
  ('_tk_data/comdlg.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/comdlg.tcl',
   'DATA'),
  ('_tk_data/ttk/cursors.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/cursors.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_sv.msg',
   'DATA'),
  ('_tcl_data/msgs/hu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hu.msg',
   'DATA'),
  ('_tcl_data/init.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/init.tcl',
   'DATA'),
  ('_tk_data/icons.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/icons.tcl',
   'DATA'),
  ('_tk_data/tkfbox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tkfbox.tcl',
   'DATA'),
  ('_tcl_data/opt0.4/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/opt0.4/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/encoding/euc-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/euc-kr.enc',
   'DATA'),
  ('_tcl_data/msgs/en_nz.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_nz.msg',
   'DATA'),
  ('_tcl_data/msgs/en_gb.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_gb.msg',
   'DATA'),
  ('_tcl_data/msgs/af.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/af.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-cn.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/euc-cn.enc',
   'DATA'),
  ('tcl8/8.6/tdbc/sqlite3-1.1.3.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.6/tdbc/sqlite3-1.1.3.tm',
   'DATA'),
  ('_tcl_data/encoding/cp1254.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1254.enc',
   'DATA'),
  ('_tcl_data/msgs/gv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gv.msg',
   'DATA'),
  ('_tk_data/ttk/sizegrip.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/sizegrip.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp852.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp852.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-11.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-11.enc',
   'DATA'),
  ('_tcl_data/encoding/macGreek.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macGreek.enc',
   'DATA'),
  ('_tcl_data/msgs/es_do.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_do.msg',
   'DATA'),
  ('_tcl_data/msgs/en_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_ca.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1255.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1255.enc',
   'DATA'),
  ('_tcl_data/encoding/cp932.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp932.enc',
   'DATA'),
  ('_tk_data/ttk/xpTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/xpTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/en_ph.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/en_ph.msg',
   'DATA'),
  ('_tk_data/unsupported.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/unsupported.tcl',
   'DATA'),
  ('_tcl_data/msgs/pt_br.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/pt_br.msg',
   'DATA'),
  ('_tcl_data/msgs/ar.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ar.msg',
   'DATA'),
  ('_tk_data/images/pwrdLogo.eps',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/pwrdLogo.eps',
   'DATA'),
  ('_tcl_data/encoding/cp874.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp874.enc',
   'DATA'),
  ('_tcl_data/tclIndex',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/tclIndex',
   'DATA'),
  ('_tcl_data/encoding/jis0212.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/jis0212.enc',
   'DATA'),
  ('_tk_data/tearoff.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/tearoff.tcl',
   'DATA'),
  ('_tcl_data/msgs/ta_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ta_in.msg',
   'DATA'),
  ('_tcl_data/msgs/te_in.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/te_in.msg',
   'DATA'),
  ('_tcl_data/msgs/id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/id.msg',
   'DATA'),
  ('_tcl_data/msgs/el.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/el.msg',
   'DATA'),
  ('_tcl_data/msgs/gl_es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/gl_es.msg',
   'DATA'),
  ('_tk_data/ttk/treeview.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/treeview.tcl',
   'DATA'),
  ('_tcl_data/msgs/et.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/et.msg',
   'DATA'),
  ('_tcl_data/msgs/es_hn.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_hn.msg',
   'DATA'),
  ('_tcl_data/msgs/es_py.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_py.msg',
   'DATA'),
  ('_tcl_data/msgs/tr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/tr.msg',
   'DATA'),
  ('_tk_data/msgs/cs.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/cs.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1251.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1251.enc',
   'DATA'),
  ('_tk_data/ttk/progress.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/progress.tcl',
   'DATA'),
  ('_tcl_data/encoding/jis0208.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/jis0208.enc',
   'DATA'),
  ('_tcl_data/msgs/sh.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sh.msg',
   'DATA'),
  ('_tk_data/msgs/nl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/nl.msg',
   'DATA'),
  ('_tcl_data/msgs/nl_be.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/nl_be.msg',
   'DATA'),
  ('_tcl_data/encoding/cp1252.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1252.enc',
   'DATA'),
  ('_tcl_data/encoding/cp1253.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1253.enc',
   'DATA'),
  ('_tcl_data/encoding/symbol.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/symbol.enc',
   'DATA'),
  ('_tcl_data/msgs/de.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/de.msg',
   'DATA'),
  ('_tcl_data/msgs/te.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/te.msg',
   'DATA'),
  ('_tcl_data/msgs/fo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fo.msg',
   'DATA'),
  ('_tk_data/msgs/sv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/sv.msg',
   'DATA'),
  ('_tcl_data/encoding/macTurkish.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macTurkish.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-2.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-2.enc',
   'DATA'),
  ('_tcl_data/msgs/sk.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sk.msg',
   'DATA'),
  ('_tcl_data/encoding/iso8859-15.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-15.enc',
   'DATA'),
  ('_tcl_data/encoding/macCroatian.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macCroatian.enc',
   'DATA'),
  ('_tcl_data/http1.0/pkgIndex.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/http1.0/pkgIndex.tcl',
   'DATA'),
  ('_tcl_data/msgs/fr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr.msg',
   'DATA'),
  ('_tk_data/ttk/notebook.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/notebook.tcl',
   'DATA'),
  ('_tcl_data/msgs/lv.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/lv.msg',
   'DATA'),
  ('_tcl_data/encoding/euc-jp.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/euc-jp.enc',
   'DATA'),
  ('_tk_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/eo.msg',
   'DATA'),
  ('_tk_data/scrlbar.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/scrlbar.tcl',
   'DATA'),
  ('_tcl_data/msgs/es_mx.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_mx.msg',
   'DATA'),
  ('_tcl_data/msgs/es_gt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_gt.msg',
   'DATA'),
  ('tcl8/8.4/platform/shell-1.1.4.tm',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8/8.4/platform/shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data/msgs/fr_ca.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fr_ca.msg',
   'DATA'),
  ('_tcl_data/msgs/ga.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ga.msg',
   'DATA'),
  ('_tk_data/msgs/pt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/msgs/pt.msg',
   'DATA'),
  ('_tcl_data/history.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/history.tcl',
   'DATA'),
  ('_tcl_data/package.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/package.tcl',
   'DATA'),
  ('_tk_data/megawidget.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/megawidget.tcl',
   'DATA'),
  ('_tcl_data/msgs/id_id.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/id_id.msg',
   'DATA'),
  ('_tcl_data/msgs/hr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/hr.msg',
   'DATA'),
  ('_tk_data/ttk/altTheme.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/altTheme.tcl',
   'DATA'),
  ('_tcl_data/msgs/es.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es.msg',
   'DATA'),
  ('_tk_data/button.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/button.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp857.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp857.enc',
   'DATA'),
  ('_tcl_data/msgs/eu.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/eu.msg',
   'DATA'),
  ('_tk_data/ttk/defaults.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/defaults.tcl',
   'DATA'),
  ('_tk_data/images/logoLarge.gif',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/images/logoLarge.gif',
   'DATA'),
  ('_tcl_data/msgs/es_pr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/es_pr.msg',
   'DATA'),
  ('_tcl_data/encoding/macJapan.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/macJapan.enc',
   'DATA'),
  ('_tcl_data/msgs/sr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/sr.msg',
   'DATA'),
  ('_tcl_data/msgs/kl_gl.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kl_gl.msg',
   'DATA'),
  ('_tcl_data/encoding/cns11643.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cns11643.enc',
   'DATA'),
  ('_tcl_data/msgs/mt.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/mt.msg',
   'DATA'),
  ('_tcl_data/clock.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/clock.tcl',
   'DATA'),
  ('_tcl_data/encoding/cp1258.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp1258.enc',
   'DATA'),
  ('_tcl_data/encoding/cp864.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/cp864.enc',
   'DATA'),
  ('_tcl_data/tclAppInit.c',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/tclAppInit.c',
   'DATA'),
  ('_tcl_data/encoding/iso2022-kr.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso2022-kr.enc',
   'DATA'),
  ('_tcl_data/encoding/iso8859-8.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-8.enc',
   'DATA'),
  ('_tcl_data/msgs/kw.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/kw.msg',
   'DATA'),
  ('_tk_data/clrpick.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/clrpick.tcl',
   'DATA'),
  ('_tcl_data/encoding/koi8-u.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/koi8-u.enc',
   'DATA'),
  ('_tk_data/safetk.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/safetk.tcl',
   'DATA'),
  ('_tcl_data/encoding/iso8859-5.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/iso8859-5.enc',
   'DATA'),
  ('_tcl_data/msgs/eo.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/eo.msg',
   'DATA'),
  ('_tcl_data/msgs/ko_kr.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/ko_kr.msg',
   'DATA'),
  ('_tcl_data/encoding/shiftjis.enc',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/encoding/shiftjis.enc',
   'DATA'),
  ('_tk_data/obsolete.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/obsolete.tcl',
   'DATA'),
  ('_tcl_data/msgs/fi.msg',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tcl8.6/msgs/fi.msg',
   'DATA'),
  ('_tk_data/ttk/combobox.tcl',
   '/Library/Frameworks/Python.framework/Versions/3.11/lib/tk8.6/ttk/combobox.tcl',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.11/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/PycharmProjects/workPro/新FOC/build/VPN连接器/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.11/Resources/Info.plist',
   '/Library/Frameworks/Python.framework/Versions/3.11/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.11', 'SYMLINK')],)
