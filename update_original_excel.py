#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接在原Excel文件中添加冲突分析结果到第三列
"""

import pandas as pd
import re
from typing import List, Tuple
import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON>ill, <PERSON>ont

def parse_green_interval(interval_str: str) -> List[Tuple[float, float, str]]:
    """解析green_interval列的区间表达式"""
    if pd.isna(interval_str) or not isinstance(interval_str, str):
        return []
    
    intervals = []
    interval_str = str(interval_str).strip()
    
    # 跳过包含变量的表达式
    if re.search(r'[A-Za-z]', interval_str):
        return []
    
    # 处理各种格式
    if interval_str.startswith('≤') or interval_str.startswith('<='):
        match = re.match(r'[≤<=](\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((float('-inf'), val, '<='))
    elif interval_str.startswith('≥') or interval_str.startswith('>='):
        match = re.match(r'[≥>=](\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((val, float('inf'), '>='))
    elif interval_str.startswith('<'):
        match = re.match(r'<(\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((float('-inf'), val, '<'))
    elif interval_str.startswith('>'):
        match = re.match(r'>(\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((val, float('inf'), '>'))
    elif '[' in interval_str and ']' in interval_str:
        match = re.match(r'\[([^,\-\]]+)[\-,]([^\]]+)\]', interval_str)
        if match:
            try:
                min_val = float(match.group(1))
                max_val = float(match.group(2))
                intervals.append((min_val, max_val, '[]'))
            except ValueError:
                pass
    
    return intervals

def parse_over_interval(interval_str: str) -> List[Tuple[float, float, str]]:
    """解析overInterval列的复杂区间表达式"""
    if pd.isna(interval_str) or not isinstance(interval_str, str):
        return []
    
    intervals = []
    interval_str = str(interval_str).strip()
    
    # 提取所有数值比较条件
    patterns = [
        r'测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',
        r'(\d+(?:\.\d+)?)\s*([<>]=?)\s*测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, interval_str)
        for match in matches:
            if len(match) == 2:
                operator, value = match
                val = float(value)
                if operator == '<':
                    intervals.append((float('-inf'), val, '<'))
                elif operator == '<=':
                    intervals.append((float('-inf'), val, '<='))
                elif operator == '>':
                    intervals.append((val, float('inf'), '>'))
                elif operator == '>=':
                    intervals.append((val, float('inf'), '>='))
            elif len(match) == 4:
                min_val, min_op, max_op, max_val = match
                min_val = float(min_val)
                max_val = float(max_val)
                intervals.append((min_val, max_val, f'{min_op}..{max_op}'))
    
    return intervals

def check_conflicts(green_intervals: List[Tuple[float, float, str]], 
                   over_intervals: List[Tuple[float, float, str]]) -> str:
    """检查冲突并返回简洁的结果描述"""
    if not green_intervals and not over_intervals:
        return "无法解析"
    
    if not green_intervals:
        return "绿色区间包含变量"
    
    if not over_intervals:
        return "超标区间无法解析"
    
    # 检查是否有重叠
    for g_min, g_max, g_op in green_intervals:
        for o_min, o_max, o_op in over_intervals:
            overlap_start = max(g_min, o_min)
            overlap_end = min(g_max, o_max)
            if overlap_start < overlap_end:
                return "有冲突"
    
    return "无冲突"

def main():
    excel_path = "./交集判定/111.xlsx"
    
    try:
        # 加载数据
        data = pd.read_excel(excel_path)
        workbook = openpyxl.load_workbook(excel_path)
        worksheet = workbook.active
        
        print(f"成功加载数据，共 {len(data)} 行")
        
        # 找到green_interval和overInterval列
        green_col_idx = None
        over_col_idx = None
        
        for idx, col_name in enumerate(data.columns):
            if 'green_interval' in str(col_name):
                green_col_idx = idx
            elif 'overInterval' in str(col_name):
                over_col_idx = idx
        
        if green_col_idx is None or over_col_idx is None:
            print("未找到green_interval或overInterval列")
            return
        
        # 确定结果列位置（第三列，即C列）
        result_col = 3
        
        # 添加表头
        worksheet.cell(row=1, column=result_col, value="冲突分析结果")
        worksheet.cell(row=1, column=result_col).font = Font(bold=True)
        
        # 定义样式
        conflict_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")  # 红色
        no_conflict_fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")  # 绿色
        warning_fill = PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")  # 黄色
        
        conflict_count = 0
        no_conflict_count = 0
        warning_count = 0
        
        # 分析每一行
        for index, row in data.iterrows():
            excel_row = index + 2  # Excel行号从2开始
            
            green_val = row.iloc[green_col_idx]
            over_val = row.iloc[over_col_idx]
            
            # 解析区间
            green_intervals = parse_green_interval(green_val)
            over_intervals = parse_over_interval(over_val)
            
            # 检查冲突
            result = check_conflicts(green_intervals, over_intervals)
            
            # 写入结果
            cell = worksheet.cell(row=excel_row, column=result_col, value=result)
            
            # 设置样式和统计
            if result == "有冲突":
                cell.fill = conflict_fill
                conflict_count += 1
            elif result == "无冲突":
                cell.fill = no_conflict_fill
                no_conflict_count += 1
            else:
                cell.fill = warning_fill
                warning_count += 1
        
        # 调整列宽
        worksheet.column_dimensions['C'].width = 20
        
        # 保存文件
        output_path = excel_path.replace('.xlsx', '_结果.xlsx')
        workbook.save(output_path)
        
        print(f"\n✅ 分析完成并保存到: {output_path}")
        print(f"📊 统计结果:")
        print(f"   🔴 有冲突: {conflict_count} 行")
        print(f"   🟢 无冲突: {no_conflict_count} 行") 
        print(f"   🟡 其他: {warning_count} 行")
        print(f"   📈 冲突比例: {conflict_count/(len(data))*100:.1f}%")
        
    except Exception as e:
        print(f"处理失败: {e}")

if __name__ == "__main__":
    main()
