# -*- coding: utf-8 -*-
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
import requests

Session = sessionmaker(bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/sf_floms_fpmp_test'))
session = Session()


def get_flight_plan_interface(data, token):
    url = "http://10.162.22.59:29009/api/fpmp/flightPlanAirLineEvaluation/testCalculate"
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json().get("content")
    except requests.RequestException as e:
        print(f"请求接口时发生错误: {e}")
        return None


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


def shift_last_second_value(lst):
    result = []
    temp_turnaround_time = 0
    for x in lst:
        plan_id, act_no, act_type, dep_icao, arr_icao, flight_no, dep_time, arr_time, turnaround_time, standard_time, id, cycle = x
        tt = turnaround_time
        turnaround_time = temp_turnaround_time
        result.append(
            [plan_id, act_no, act_type, dep_icao, arr_icao, flight_no, str(dep_time), str(arr_time), turnaround_time,
             standard_time, id, cycle])
        temp_turnaround_time = tt
    return result


def get_airline_list(plan_id, id):
    sql = f'''
    SELECT fpa.plan_id,fpa.act_no,fpa.act_type,fpa.dep_icao,fpa.arr_icao,fpa.flight_no,fpa.dep_time,fpa.arr_time,fpa.turnaround_time,fpa.standard_time,fpa.id,fpa.cycle
FROM fpmp_flight_plan_air_line fpa
JOIN (
    SELECT act_no,cycle
    FROM fpmp_flight_plan_air_line
    WHERE id = {id}
) AS sub ON fpa.act_no = sub.act_no and fpa.cycle = sub.cycle
WHERE fpa.plan_id = {plan_id} AND fpa.deleted = 0 and fpa.applicated = 1
ORDER BY fpa.dep_time;

    '''

    airline_list = query_sql(sql)
    format_list = shift_last_second_value(airline_list)
    for x in format_list:
        if x[10] == id:
            print(x)
            return x


def normal_rate_score(normal_rate):
    """
    根据正常率计算对应分数
    :param normal_rate: 正常率的值（范围0到1之间）
    :return: 计算出的分数
    """
    # print(f"正常率:{normal_rate}")
    base_score = 0
    standard = 0.85
    diff = normal_rate - standard
    return round(base_score + diff / 0.01 * 5, 2)


def execution_rate_score(execution_rate):
    """
    根据执行率计算对应分数
    :param execution_rate: 执行率的值（范围0到1之间）
    :return: 计算出的分数
    """
    base_score = 0
    standard = 0.985
    diff = execution_rate - standard
    return round(base_score + diff / 0.002 * 5, 2)


def airworthiness_probability_score(airworthiness_probability):
    """
    根据适航概率计算对应分数
    :param airworthiness_probability: 适航概率的值（范围0到1之间）
    :return: 计算出的分数
    """
    base_score = 0
    standard = 0.97
    diff = airworthiness_probability - standard
    return round(base_score + diff / 0.005 * 5, 2)


def is_Limited(limited_value):
    if limited_value == 1:
        return -10
    else:
        return 0


def pass_station_score(turnaroundTime, act_type):
    standard_time = 0
    min_time = 0
    if act_type in ['A20N', 'A320']:
        standard_time = 70
        min_time = 65
    elif act_type == 'AJ27':
        standard_time = 65
        min_time = 55
    elif act_type == 'CRJ9':
        standard_time = 60
        min_time = 55
    # print(standard_time)
    print(turnaroundTime, standard_time, min_time)
    if turnaroundTime == 0:
        return 10
    elif turnaroundTime < min_time:
        # print(turnaroundTime, standard_time)
        return '否决'
    elif min_time <= turnaroundTime < 120:
        return 5 * (turnaroundTime - standard_time) / 5
    else:
        return 10


def get_final_score(zcl, zxl, shl, xnpg, gzsjpf, isSatisfy):
    if isSatisfy == 1:
        if gzsjpf == "否决":
            final_score = 0
        else:
            final_score = round(80 + zcl + zxl + shl + xnpg + gzsjpf, 2)
    else:
        final_score = 0
        xnpg = '否决'
    print(f"航线品质评分：{final_score}")
    print(f"正常率评分：{zcl}")
    print(f"执行率评分：{zxl}")
    print(f"计划过站时间评分：{gzsjpf}")
    print(f"适航概率评分：{shl}")
    print(f"航线限载评分：{xnpg}")


if __name__ == '__main__':
    token = "bfdca0b5-cbca-4b91-b2f0-1881757ba5db"
    data = {"depSafeScore": 0, "arrSafeScore": 0, "totalFuel": 12395, "isLimited": 0, "isSatisfy": 1,
            "depWeatherWorthRate": 1, "arrWeatherWorthRate": 0.9947, "safeWeatherWorthRate": 1, "depNormalRate": 0.6962,
            "hourlyEarning": 60000, "seatLimitRate": 1, "historyAvgCost": 78075.91, "airLineId": 55606, "planId": 737,
            "saveFlag": 0}
planId = data.get("planId")
airLineId = data.get("airLineId")
dt = get_flight_plan_interface(data, token)
airline_list = get_airline_list(planId, airLineId)
turnaroundTime = airline_list[8]
act_type = airline_list[2]
predictExecutRate = dt.get("predictExecutRate")
predictNormalRate = dt.get("predictNormalRate")
isLimited = dt.get("isLimited")
isSatisfy = dt.get("isSatisfy")
zcl = normal_rate_score(predictNormalRate)
zxl = execution_rate_score(predictExecutRate)
shl = airworthiness_probability_score(predictExecutRate)
xnpg = is_Limited(isLimited)
gzsjpf = pass_station_score(turnaroundTime, act_type)
get_final_score(zcl, zxl, shl, xnpg, gzsjpf, isSatisfy)
