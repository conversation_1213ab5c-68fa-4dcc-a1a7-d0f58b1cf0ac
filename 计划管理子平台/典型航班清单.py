# -*- coding: utf-8 -*-
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
from datetime import timedelta

Session = sessionmaker(bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/sf_floms_fpmp_test'))
session = Session()


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


def get_org_data(start_time, end_time):
    sql = f"SELECT * from fpmp_flight a WHERE a.flight_date >= '{start_time}' AND a.flight_date <= '{end_time}' and a.deleted = 0 "
    org_list = query_sql(sql)
    df = pd.DataFrame(org_list)
    # print(df)
    return df


def get_fz(org_data):
    filtered_df = org_data[(org_data['stc'].isin(['C', 'G', 'J'])) &
                           (org_data['flight_status'] == 'ATA') &
                           (org_data['div_reason'].isnull()) &
                           (org_data['dla_reason'].isnull()) &
                           (org_data['ctr_reason'].isnull()) &
                           (org_data['legno'].str.endswith('00'))]
    result = filtered_df.groupby(['flight_no', 'dep_iata', 'ori_arr_iata'])['foc_flight_id'].count().reset_index()
    result.rename(columns={'foc_flight_id': 'fz'}, inplace=True)

    return result


def get_fm(org_data):
    filtered_df = org_data[(org_data['stc'].isin(['C', 'G', 'J'])) &
                           ((org_data['flight_status'] == 'ATA') | ((org_data['flight_status'] == 'CNL') & (
                               org_data['div_reason'].isin(
                                   ['11', '12', '13', '42', '24', '17', '14', '15', '22', '0', '16', '25', '28', '19',
                                    '18', '29', '51', '21', '53', '20',
                                    '23']))))]

    # filter_df = filtered_df[(filtered_df['flight_no'] == 'G54062') & (filtered_df['dep_iata'] == 'AKU') & (
    #             filtered_df['arr_iata'] == 'JBK')]
    # print(filter_df.to_string())
    result = filtered_df.groupby(['flight_no', 'dep_iata', 'ori_arr_iata'])['foc_flight_id'].count().reset_index()
    result.rename(columns={'foc_flight_id': 'fm'}, inplace=True)

    return result


def get_bb(org_data):
    letter_to_number = {'Z': '0', 'Y': '1', 'X': '2', 'W': '3', 'V': '4',
                        'U': '5', 'T': '6', 'S': '7', 'R': '8', 'Q': '9'}
    d_rows = org_data[org_data['stc'] == 'D']
    for index, row in d_rows.iterrows():
        flight_no = row['flight_no']
        last_char = flight_no[-1]
        if last_char in letter_to_number:
            restored_number = letter_to_number[last_char]
            flight_no_restored = flight_no[:-1] + restored_number
        else:
            flight_no_restored = flight_no
        org_data.at[index, 'flight_no'] = flight_no_restored
        org_data.at[index, 'stc'] = "J"
        flight_date = row['flight_date']
        previous_day = flight_date - timedelta(days=1)
        air_line = row['air_line']
        org_data = org_data[~((org_data['flight_no'] == flight_no_restored) &
                              (org_data['air_line'] == air_line) &
                              (org_data['flight_date'] == previous_day))]
        org_data.at[index, 'flight_date'] = previous_day
    filtered_df = org_data[(org_data['stc'].isin(['C', 'G', 'J'])) &
                           (org_data['flight_status'] == 'ATA')]
    # filter_df = filtered_df[(filtered_df['flight_no'] == 'G54062') & (filtered_df['dep_iata'] == 'AKU') & (
    #         filtered_df['ori_arr_iata'] == 'JBK')]
    # print(filter_df.to_string())
    result = filtered_df.groupby(['flight_no', 'dep_iata', 'ori_arr_iata'])['foc_flight_id'].count().reset_index()
    result.rename(columns={'foc_flight_id': 'bb'}, inplace=True)

    return result


def get_bj_df(start_time, end_time):
    sql = f'''
    SELECT
	D.flight_no,
	D.dep_iata,
	D.ori_arr_iata,
  COUNT(DISTINCT a.foc_flight_id) AS bj_air

FROM
	fpmp_flight a 
	LEFT JOIN fpmp_flight_ext C ON a.foc_flight_id = C.foc_flight_id
	RIGHT JOIN fpmp_flight D ON D.foc_flight_id = C.dla_duty_flt
WHERE
	a.flight_date >= '{start_time}' 
	AND a.flight_date <= '{end_time}' 
	AND a.deleted = 0 
	AND a.stc IN ('C', 'G', 'J') 
	GROUP BY
	D.flight_no,
	D.dep_iata,
	D.ori_arr_iata
    '''
    bj_list = query_sql(sql)
    df = pd.DataFrame(bj_list)
    return df


def get_avg_delay(start_time, end_time):
    sql = f'''
        SELECT
	a.flight_no,
	a.dep_iata,
	a.ori_arr_iata,
	AVG(
		CASE
				WHEN a.dla_reason IS NOT NULL 
				AND a.flight_status = 'ATA' THEN
					TIMESTAMPDIFF(MINUTE, a.std, a.atd) - (CASE WHEN b.slide_time IS NULL AND a.dep_icao LIKE 'Z%' THEN 15 ELSE IFNULL(b.slide_time, 30) END) 
				END) AS avg_delay_time 
		FROM
			fpmp_flight a
			LEFT JOIN fpmp_airport_slide_time b ON a.dep_icao = b.airport_code 
			AND b.deleted = 0 
		WHERE
			a.deleted = 0 
			AND a.flight_date BETWEEN '{start_time}' AND '{end_time}'
			and a.dla_reason is not NULL
	GROUP BY
	a.flight_no,
	a.dep_iata,
	a.ori_arr_iata
        '''
    delay_list = query_sql(sql)
    df = pd.DataFrame(delay_list)
    df['avg_delay_time'] = df['avg_delay_time'].astype(float).round(4)
    return df


def get_avg_pass(start_time, end_time):
    sql = f'''
     SELECT
	a.flight_no,
	a.dep_iata,
	a.ori_arr_iata,
	AVG(b.act_pass_time) as pass_time
FROM
	fpmp_flight a
	RIGHT JOIN fpmp_pass_station_analysis b ON a.foc_flight_id = b.foc_flight_id 
	AND b.deleted = 0 
WHERE
	a.flight_date >= '{start_time}' 
	AND a.flight_date <= '{end_time}' 
	AND a.deleted = 0 
	AND a.legno LIKE '%00' 
	AND a.stc IN ('C', 'G', 'J') 
	AND a.div_reason IS NULL 
	AND a.ctr_reason IS NULL 
	AND ((b.act_type = 'A320' AND b.sch_pass_time <= 90) OR (b.act_type <> 'A320' AND b.sch_pass_time <= 60))
GROUP BY
	a.flight_no,
	a.dep_iata,
	a.ori_arr_iata;
            '''
    pass_list = query_sql(sql)
    df = pd.DataFrame(pass_list)
    df['pass_time'] = df['pass_time'].astype(float).round(2)
    return df


def get_first(start_time, end_time):
    org_data = get_org_data(start_time, end_time)
    fz = get_fz(org_data)
    fm = get_fm(org_data)
    bb = get_bb(org_data)
    temp_df = pd.merge(fz, fm, on=['flight_no', 'dep_iata', 'ori_arr_iata'])
    result_df = pd.merge(temp_df, bb, on=['flight_no', 'dep_iata', 'ori_arr_iata'])
    result_df['normal_rate'] = (result_df['fz'] / result_df['fm']).round(4)
    result_df['execute_rate'] = (result_df['bb'] / result_df['fm']).round(4)
    result_df_sorted = result_df.sort_values(by='normal_rate', ascending=True).head(20)
    # result_df_sorted = result_df.sort_values(by='normal_rate', ascending=True)
    bj_df = get_bj_df(start_time, end_time)
    temp_df1 = pd.merge(result_df_sorted, bj_df, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    delay = get_avg_delay(start_time, end_time)
    temp_df2 = pd.merge(temp_df1, delay, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    pass_time = get_avg_pass(start_time, end_time)
    temp_df3 = pd.merge(temp_df2, pass_time, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    dd = temp_df3.fillna(0)
    return dd


def get_last(start_time, end_time):
    org_data = get_org_data(start_time, end_time)
    fz = get_fz(org_data)
    fm = get_fm(org_data)
    bb = get_bb(org_data)
    temp_df = pd.merge(fz, fm, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='outer')
    result_df = pd.merge(temp_df, bb, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='outer')
    result_df['normal_rate'] = (result_df['fz'] / result_df['fm']).round(4)
    result_df['execute_rate'] = (result_df['bb'] / result_df['fm']).round(4)
    result_df_sorted = result_df.sort_values(by='normal_rate', ascending=True)
    bj_df = get_bj_df(start_time, end_time)
    temp_df1 = pd.merge(result_df_sorted, bj_df, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    delay = get_avg_delay(start_time, end_time)
    temp_df2 = pd.merge(temp_df1, delay, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    pass_time = get_avg_pass(start_time, end_time)
    temp_df3 = pd.merge(temp_df2, pass_time, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    dd = temp_df3.fillna(0)

    return dd


if __name__ == '__main__':
    start_time1 = '2024-10-01'
    end_time1 = '2024-10-15'
    start_time2 = '2024-10-16'
    end_time2 = '2024-10-30'
    first = get_first(start_time1, end_time1)
    last = get_last(start_time2, end_time2)
    temp_df1 = pd.merge(first, last, on=['flight_no', 'dep_iata', 'ori_arr_iata'], how='left')
    dd = temp_df1.fillna(0)
    print(dd.to_string())
    # print(last.to_string())
