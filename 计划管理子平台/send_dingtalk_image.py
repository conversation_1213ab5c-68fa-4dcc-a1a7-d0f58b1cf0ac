import requests
import json

# 钉钉机器人Webhook地址
webhook_url = "https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55"

import os
from urllib.parse import urlparse, parse_qs

# 图片URL和保存路径
image_url = "https://static.bmwax.cn/common/logo-bamuwu-v2.png"
image_path = os.path.join(os.path.dirname(__file__), "downloaded_image.png")
message_title = "测试"

# 下载图片
print(f"正在下载图片: {image_url}")
response = requests.get(image_url, stream=True)
response.raise_for_status()  # 检查下载是否成功

with open(image_path, 'wb') as f:
    for chunk in response.iter_content(chunk_size=8192):
        f.write(chunk)
print(f"图片下载成功: {image_path}")

# 检查图片文件是否存在
if not os.path.exists(image_path):
    raise FileNotFoundError(f"图片文件不存在: {image_path}")

# 上传图片获取media_id
# 解析access_token
parsed_url = urlparse(webhook_url)
query_params = parse_qs(parsed_url.query)
access_token = query_params.get('access_token', [None])[0]
print(f"提取到的access_token: {access_token[:10]}...{access_token[-10:]}")  # 打印部分token用于调试
if not access_token:
    raise ValueError("无法从webhook_url中提取access_token")

upload_url = f"https://oapi.dingtalk.com/media/upload?access_token={access_token}&type=image"
files = {'media': open(image_path, 'rb')}
upload_response = requests.post(upload_url, files=files)
print(f"上传请求URL: {upload_url}")
print(f"上传响应状态码: {upload_response.status_code}")
print(f"上传响应内容: {upload_response.text}")

# 检查上传是否成功
if upload_response.status_code != 200:
    raise Exception(f"图片上传失败: HTTP状态码{upload_response.status_code}, 响应内容:{upload_response.text}")

upload_result = upload_response.json()
print(f"钉钉API返回结果: {upload_result}")
if upload_result.get('errcode') != 0:
    raise Exception(f"钉钉API错误: 错误码{upload_result.get('errcode')}, 错误信息:{upload_result.get('errmsg')}")

media_id = upload_result.get('media_id')
if not media_id:
    raise Exception("未能获取media_id")

# 构造图片消息
payload = {
    "msgtype": "image",
    "image": {
        "media_id": media_id
    }
}

# 发送POST请求
headers = {'Content-Type': 'application/json'}
response = requests.post(webhook_url, headers=headers, data=json.dumps(payload))

# 输出结果
print(f"发送状态: {response.status_code}")
print(f"响应内容: {response.text}")