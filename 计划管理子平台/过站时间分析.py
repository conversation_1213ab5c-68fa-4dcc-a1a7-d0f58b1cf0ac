# -*- coding: utf-8 -*-
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
import requests

Session = sessionmaker(bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/sf_floms_fpmp_test'))
session = Session()


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


def get_airline_list(plan_id, id, start_t, end_t):
    sql = f'''
    SELECT fpa.plan_id,fpa.act_no,fpa.act_type,fpa.dep_icao,fpa.arr_icao,fpa.flight_no,fpa.id,fpa.cycle,fpa.dep_time,fpa.arr_time,fpa.plan_start_date,fpa.plan_end_date
FROM fpmp_flight_plan_air_line fpa
JOIN (
    SELECT act_no,cycle
    FROM fpmp_flight_plan_air_line
    WHERE id = {id}
) AS sub ON fpa.act_no = sub.act_no and fpa.cycle = sub.cycle
WHERE fpa.plan_id = {plan_id} AND fpa.deleted = 0 and fpa.applicated = 1
ORDER BY fpa.dep_time;

    '''

    airline_list = query_sql(sql)
    df = pd.DataFrame(airline_list)
    df['turnaround_time'] = 0

    for index in range(1, len(df)):
        if df.loc[index, 'dep_time'] == df.loc[index - 1, 'dep_time'] and df.loc[index, 'arr_time'] == df.loc[
            index - 1, 'arr_time']:
            df.loc[index, 'turnaround_time'] = df.loc[index - 1, 'turnaround_time']
        else:
            df.loc[index, 'turnaround_time'] = df.loc[index, 'dep_time'] - df.loc[index - 1, 'arr_time']

    df['turnaround_time'] = df['turnaround_time'].apply(lambda x: str(x).split(' ')[-1])

    # 查看结果
    print(df.to_string())
    new_flights = []

    # 遍历每一行
    for _, row in df.iterrows():
        start_date = row['plan_start_date']
        end_date = row['plan_end_date']
        cycle = [int(day) for day in row['cycle'].split(',')]

        # 生成日期范围内的所有日期
        date_range = pd.date_range(start=start_date, end=end_date)

        # 筛选出符合 cycle 中周几的日期
        for date in date_range:
            if (date.weekday() + 1) in cycle:
                new_row = row.copy()
                new_row['flight_date'] = date
                new_flights.append(new_row)

    new_df = pd.DataFrame(new_flights)
    # print(new_df.to_string())
    start_time = pd.to_datetime(start_t)
    end_time = pd.to_datetime(end_t)
    filtered_df = new_df[(new_df['flight_date'] >= start_time) & (new_df['flight_date'] <= end_time)]
    filtered_df = filtered_df[filtered_df['turnaround_time'] != '0']

    print(filtered_df.to_string())

    def time_to_minutes(time_str):
        try:
            hours, minutes, seconds = map(int, str(time_str).split(':'))
            return hours * 60 + minutes + seconds / 60
        except ValueError:
            print(f"Invalid time format: {time_str}. Skipping this entry.")
            return None

    filtered_df['turnaround_time_minutes'] = filtered_df['turnaround_time'].apply(time_to_minutes)

    average_turnaround = filtered_df.groupby('dep_icao')['turnaround_time_minutes'].mean()

    return average_turnaround


if __name__ == '__main__':
    start_t = '2025-04-01'
    end_t = '2025-04-30'
    a = get_airline_list(738, 55651, start_t, end_t)
    # a = get_airline_list(739, 55673, start_t, end_t)
    print(a)
