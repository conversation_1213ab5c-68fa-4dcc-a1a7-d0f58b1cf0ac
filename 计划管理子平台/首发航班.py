# -*- coding: utf-8 -*-
import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
import requests

Session = sessionmaker(bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/sf_floms_fpmp_test'))
session = Session()


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


def get_pjwqfsj(start_t1, end_t1, start_t2, end_t2, icao_act_type, dep_icao, arr_icao, std, sta):
    sql = f'''
    SELECT
	sum(TIMESTAMPDIFF(SECOND,a.std, a.atd ) / 60) / COUNT(a.foc_flight_id) as pjwqfsj,
	sum(TIMESTAMPDIFF(SECOND, a.sta,a.ata) / 60) / COUNT(a.foc_flight_id) as lswdsj
FROM
	fpmp_flight a 
WHERE
	a.deleted = 0 
	AND a.stc IN ('C', 'J', 'G') 
	AND a.flight_status = 'ATA' 
	AND a.div_reason IS NULL 
	AND a.dla_reason IS NULL 
	AND a.ctr_reason IS NULL 
	AND a.icao_act_type = '{icao_act_type}' 
	AND a.dep_icao = '{dep_icao}' 
	AND a.arr_icao = '{arr_icao}' 
	AND ((a.flight_date >= '{start_t1}' AND a.flight_date <= '{end_t1}') OR (a.flight_date >= '{start_t2}' AND a.flight_date <= '{end_t2}')) 
	AND CAST(a.std AS TIME) = '{std}' 
	AND CAST(a.sta AS TIME) = '{sta}' 
GROUP BY
	a.icao_act_type,
	a.dep_icao,
	a.arr_icao;
    '''

    pjwqfsj = query_sql(sql)
    r = round(pjwqfsj[0][0], 2)
    r1 = round(pjwqfsj[0][1], 2)
    print(f"平均晚起飞时间:{r}")
    print(f"历史晚到时间:{r1}")
    return r


def get_airline_list(plan_id, id):
    sql = f'''
    SELECT fpa.plan_id,fpa.act_no,fpa.act_type,fpa.dep_icao,fpa.arr_icao,fpa.flight_no,fpa.id,fpa.cycle,fpa.dep_time,fpa.arr_time,fpa.plan_start_date,fpa.plan_end_date,fpa.qar_time,TIMESTAMPDIFF(SECOND, fpa.dep_time, fpa.arr_time) / 60 as jhhdsj
FROM fpmp_flight_plan_air_line fpa
JOIN (
    SELECT act_no,cycle
    FROM fpmp_flight_plan_air_line
    WHERE id = {id}
) AS sub ON fpa.act_no = sub.act_no and fpa.cycle = sub.cycle
WHERE fpa.plan_id = {plan_id} AND fpa.deleted = 0 and fpa.applicated = 1
ORDER BY fpa.dep_time;

    '''

    airline_list = query_sql(sql)[0]
    dict_a = {
        "plan_id": airline_list[0],
        "act_no": airline_list[1],
        "act_type": airline_list[2],
        "dep_icao": airline_list[3],
        "arr_icao": airline_list[4],
        "flight_no": airline_list[5],
        "id": airline_list[6],
        "cycle": airline_list[7],
        "dep_time": airline_list[8],
        "arr_time": airline_list[9],
        "plan_start_date": airline_list[10],
        "plan_end_date": airline_list[11],
        "qar_time": airline_list[12],
        "jhhdsj": airline_list[13]

    }
    return dict_a


def get_ycwdsj(plan_id, id, pjwqfsj):
    airline = get_airline_list(740, 56195)
    qar = airline.get("qar_time")
    jhhdsj = airline.get("jhhdsj")
    # print(pjwqfsj, qar, jhhdsj)
    ycwdsj =pjwqfsj + qar - jhhdsj
    print(f"历史晚到时间:{ycwdsj}")
    return ycwdsj


if __name__ == '__main__':
    start_t1 = '2024-04-01'
    end_t1 = '2024-04-30'
    start_t2 = '2023-04-01'
    end_t2 = '2023-04-30'
    icao_act_type = 'CRJ9'
    dep_icao = 'ZUCK'
    arr_icao = 'ZBDS'
    std = '06:45:00'
    sta = '08:40:00'
    pjwqfsj = get_pjwqfsj(start_t1, end_t1, start_t2, end_t2, icao_act_type, dep_icao, arr_icao, std, sta)
    plan_id = 740
    id = 56195
    airline = get_airline_list(plan_id, id)
    ycwdsj = get_ycwdsj(plan_id, id, pjwqfsj)
