import csv
import requests
import json

# API配置
API_URL = 'http://10.162.22.59:29012/api/upms/sysRole/add'
HEADERS = {
    'Authorization': 'b6b4cf00-720f-44ea-90b1-8a5888d10665',
    'Content-Type': 'application/json'
}

# 固定的权限ID列表
PERMISSION_IDS = ["2","51","41","47","84","74","75","76","52","182","111","128","129","190","196","192","197","194","200","195","199","193","204","205","207","209","210","211","212","213","40","42","3","8","25","26","27","28","5","13","44","157","14","15","16","35","6","17","18","19","20","36","37","43","38","39","4","9","10","11","12","34","169","170","171","172","173","176","177","178","179","180"]

# CSV文件路径
CSV_FILE_PATH = '/Users/<USER>/PycharmProjects/workPro/计划管理子平台/席位列表.csv'

# 读取CSV文件并提取角色名称
role_names = []
with open(CSV_FILE_PATH, 'r', encoding='utf-8') as file:
    reader = csv.reader(file)
    for row in reader:
        if len(row) >= 2:
            role_name = row[1]
            role_names.append(role_name)

# 从8开始的排序值
start_sort = 8

def add_role(role_name, sort):
    """添加单个角色到系统"""
    payload = {
        "roleName": role_name,
        "duty": 1,
        "permissionIds": PERMISSION_IDS,
        "dataPermissionList": [],
        "remark": "",
        "sort": sort
    }
    
    try:
        response = requests.post(
            API_URL,
            headers=HEADERS,
            data=json.dumps(payload)
        )
        response.raise_for_status()
        return True, f"成功添加角色: {role_name} (排序: {sort})"
    except Exception as e:
        return False, f"添加角色 {role_name} 失败: {str(e)}"

# 主函数
if __name__ == "__main__":
    results = []
    for index, role_name in enumerate(role_names):
        sort_value = start_sort + index
        success, message = add_role(role_name, sort_value)
        results.append(f"{index + 1}. {message}")
        
    # 打印结果
    for result in results:
        print(result)

    print("\n角色添加完成!")