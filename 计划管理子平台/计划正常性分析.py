# -*- coding: utf-8 -*-
from sqlalchemy import create_engine
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker
import requests

Session = sessionmaker(bind=create_engine('mysql+pymysql://hxyr_test:yU6)RLLqa2b@10.162.31.42:3306/sf_floms_fpmp_test'))
session = Session()


def get_flight_plan_interface(data, token):
    url = "http://10.162.22.59:29009/api/fpmp/flightPlanAirLineEvaluation/testCalculate"
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        return response.json().get("content")
    except requests.RequestException as e:
        print(f"请求接口时发生错误: {e}")
        return None


def query_sql(sql_str):
    query = session.execute(text(sql_str))
    results = query.all()
    session.close()
    return results


if __name__ == '__main__':
    token = "efa6359c-6ae5-4aeb-b1d6-6d2a05db5d41"
    data = {"depSafeScore": 80.11, "arrSafeScore": 65.52, "totalFuel": 6255, "isLimited": 0, "isSatisfy": 1,
            "depWeatherWorthRate": 1, "arrWeatherWorthRate": 1, "safeWeatherWorthRate": 1, "depNormalRate": None,
            "hourlyEarning": 43056.43, "seatLimitRate": 1, "historyAvgCost": 30801.79, "airLineId": 55636,
            "planId": 738, "saveFlag": 0}
    result = get_flight_plan_interface(data, token)
    print(result.get("depNormalRate"))
