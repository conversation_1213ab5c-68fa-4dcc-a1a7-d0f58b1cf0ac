#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区间冲突汇总报告生成器
"""

import pandas as pd
import re
from typing import List, Tuple, Dict, Any

class ConflictSummaryReporter:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.data = None
        self.conflicts = []
        
    def load_data(self):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(self.excel_path)
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def parse_green_interval(self, interval_str: str) -> List[Tuple[float, float, str]]:
        """解析green_interval列的区间表达式"""
        if pd.isna(interval_str) or not isinstance(interval_str, str):
            return []
        
        intervals = []
        interval_str = str(interval_str).strip()
        
        # 跳过包含变量的表达式
        if re.search(r'[A-Za-z]', interval_str):
            return []
        
        # 处理各种格式
        if interval_str.startswith('≤') or interval_str.startswith('<='):
            match = re.match(r'[≤<=](\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((float('-inf'), val, '<='))
        elif interval_str.startswith('≥') or interval_str.startswith('>='):
            match = re.match(r'[≥>=](\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((val, float('inf'), '>='))
        elif interval_str.startswith('<'):
            match = re.match(r'<(\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((float('-inf'), val, '<'))
        elif interval_str.startswith('>'):
            match = re.match(r'>(\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((val, float('inf'), '>'))
        elif '[' in interval_str and ']' in interval_str:
            match = re.match(r'\[([^,\-\]]+)[\-,]([^\]]+)\]', interval_str)
            if match:
                try:
                    min_val = float(match.group(1))
                    max_val = float(match.group(2))
                    intervals.append((min_val, max_val, '[]'))
                except ValueError:
                    pass
        
        return intervals
    
    def parse_over_interval(self, interval_str: str) -> List[Tuple[float, float, str]]:
        """解析overInterval列的复杂区间表达式"""
        if pd.isna(interval_str) or not isinstance(interval_str, str):
            return []
        
        intervals = []
        interval_str = str(interval_str).strip()
        
        # 提取所有数值比较条件
        patterns = [
            r'测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*([<>]=?)\s*测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, interval_str)
            for match in matches:
                if len(match) == 2:
                    operator, value = match
                    val = float(value)
                    if operator == '<':
                        intervals.append((float('-inf'), val, '<'))
                    elif operator == '<=':
                        intervals.append((float('-inf'), val, '<='))
                    elif operator == '>':
                        intervals.append((val, float('inf'), '>'))
                    elif operator == '>=':
                        intervals.append((val, float('inf'), '>='))
                elif len(match) == 4:
                    min_val, min_op, max_op, max_val = match
                    min_val = float(min_val)
                    max_val = float(max_val)
                    intervals.append((min_val, max_val, f'{min_op}..{max_op}'))
        
        return intervals
    
    def check_conflicts(self, green_intervals: List[Tuple[float, float, str]], 
                       over_intervals: List[Tuple[float, float, str]]) -> bool:
        """检查两个区间列表之间是否有冲突"""
        for g_min, g_max, g_op in green_intervals:
            for o_min, o_max, o_op in over_intervals:
                overlap_start = max(g_min, o_min)
                overlap_end = min(g_max, o_max)
                if overlap_start < overlap_end:
                    return True
        return False
    
    def analyze_conflicts(self):
        """分析所有行的冲突并生成汇总报告"""
        if self.data is None:
            print("请先加载数据")
            return
        
        print("=== 区间冲突汇总报告 ===\n")
        
        conflict_rows = []
        
        for index, row in self.data.iterrows():
            green_col = 'green_interval'
            over_col = 'overInterval'
            
            if green_col not in self.data.columns or over_col not in self.data.columns:
                continue
            
            green_val = row[green_col]
            over_val = row[over_col]
            
            # 解析区间
            green_intervals = self.parse_green_interval(green_val)
            over_intervals = self.parse_over_interval(over_val)
            
            # 检查冲突
            if green_intervals and over_intervals and self.check_conflicts(green_intervals, over_intervals):
                conflict_info = {
                    'row': index + 1,
                    'monitor_name': row.get('monitor_name', ''),
                    'green_interval': green_val,
                    'over_interval': over_val,
                    'green_parsed': green_intervals,
                    'over_parsed': over_intervals
                }
                conflict_rows.append(conflict_info)
        
        # 输出汇总报告
        print(f"总数据行数: {len(self.data)}")
        print(f"发现冲突的行数: {len(conflict_rows)}")
        print(f"冲突比例: {len(conflict_rows)/len(self.data)*100:.1f}%\n")
        
        print("=== 具体冲突详情 ===\n")
        
        for i, conflict in enumerate(conflict_rows, 1):
            print(f"冲突 {i}: 第 {conflict['row']} 行")
            print(f"监控项目: {conflict['monitor_name']}")
            print(f"绿色区间: {conflict['green_interval']}")
            print(f"超标区间: {conflict['over_interval']}")
            print(f"解析结果:")
            print(f"  - 绿色区间: {conflict['green_parsed']}")
            print(f"  - 超标区间: {conflict['over_parsed']}")
            print("-" * 80)
        
        return conflict_rows

def main():
    excel_path = "./交集判定/111.xlsx"
    
    reporter = ConflictSummaryReporter(excel_path)
    
    if reporter.load_data():
        conflicts = reporter.analyze_conflicts()
        
        # 保存冲突详情到CSV文件
        if conflicts:
            import pandas as pd
            conflict_df = pd.DataFrame([
                {
                    '行号': c['row'],
                    '监控项目': c['monitor_name'],
                    '绿色区间': c['green_interval'],
                    '超标区间': c['over_interval']
                }
                for c in conflicts
            ])
            conflict_df.to_csv('区间冲突详情.csv', index=False, encoding='utf-8-sig')
            print(f"\n冲突详情已保存到 '区间冲突详情.csv' 文件")
    else:
        print("无法加载数据文件")

if __name__ == "__main__":
    main()
