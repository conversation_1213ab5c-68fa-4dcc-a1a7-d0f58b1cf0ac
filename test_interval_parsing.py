#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试区间解析功能
"""

import re
from typing import List, <PERSON><PERSON>

def parse_green_interval(interval_str: str) -> List[Tuple[float, float, str]]:
    """解析green_interval列的区间表达式"""
    if not isinstance(interval_str, str):
        return []
    
    intervals = []
    interval_str = str(interval_str).strip()
    
    print(f"解析绿色区间: '{interval_str}'")
    
    # 跳过包含变量的表达式
    if re.search(r'[A-Za-z]', interval_str):
        print("  -> 包含变量，跳过")
        return []
    
    # 处理各种格式
    if interval_str.startswith('≤') or interval_str.startswith('<='):
        match = re.match(r'[≤<=](\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((float('-inf'), val, '<='))
            print(f"  -> 解析为: (-∞, {val}]")
    elif interval_str.startswith('≥') or interval_str.startswith('>='):
        match = re.match(r'[≥>=](\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((val, float('inf'), '>='))
            print(f"  -> 解析为: [{val}, +∞)")
    elif interval_str.startswith('<'):
        match = re.match(r'<(\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((float('-inf'), val, '<'))
            print(f"  -> 解析为: (-∞, {val})")
    elif interval_str.startswith('>'):
        match = re.match(r'>(\d+(?:\.\d+)?)', interval_str)
        if match:
            val = float(match.group(1))
            intervals.append((val, float('inf'), '>'))
            print(f"  -> 解析为: ({val}, +∞)")
    elif '[' in interval_str and ']' in interval_str:
        # 处理 [-20,20] 或 [1.8,3] 格式
        match = re.match(r'\[(-?\d+(?:\.\d+)?)[\-,](-?\d+(?:\.\d+)?)\]', interval_str)
        if match:
            try:
                min_val = float(match.group(1))
                max_val = float(match.group(2))
                intervals.append((min_val, max_val, '[]'))
                print(f"  -> 解析为: [{min_val}, {max_val}]")
            except ValueError:
                print("  -> 解析失败")
        else:
            print(f"  -> 正则匹配失败: {interval_str}")
    else:
        print("  -> 未匹配任何格式")
    
    return intervals

def parse_over_interval(interval_str: str) -> List[Tuple[float, float, str]]:
    """解析overInterval列的复杂区间表达式"""
    if not isinstance(interval_str, str):
        return []
    
    intervals = []
    interval_str = str(interval_str).strip()
    
    print(f"解析超标区间: '{interval_str}'")
    
    # 提取所有数值比较条件
    patterns = [
        r'测量值\s*([<>]=?)\s*(-?\d+(?:\.\d+)?)',
        r'(-?\d+(?:\.\d+)?)\s*([<>]=?)\s*测量值\s*([<>]=?)\s*(-?\d+(?:\.\d+)?)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, interval_str)
        for match in matches:
            if len(match) == 2:
                operator, value = match
                val = float(value)
                if operator == '<':
                    intervals.append((float('-inf'), val, '<'))
                    print(f"  -> 找到条件: 测量值 < {val}")
                elif operator == '<=':
                    intervals.append((float('-inf'), val, '<='))
                    print(f"  -> 找到条件: 测量值 <= {val}")
                elif operator == '>':
                    intervals.append((val, float('inf'), '>'))
                    print(f"  -> 找到条件: 测量值 > {val}")
                elif operator == '>=':
                    intervals.append((val, float('inf'), '>='))
                    print(f"  -> 找到条件: 测量值 >= {val}")
            elif len(match) == 4:
                min_val, min_op, max_op, max_val = match
                min_val = float(min_val)
                max_val = float(max_val)
                intervals.append((min_val, max_val, f'{min_op}..{max_op}'))
                print(f"  -> 找到区间条件: {min_val} {min_op} 测量值 {max_op} {max_val}")
    
    return intervals

def test_cases():
    """测试用例"""
    test_data = [
        ("[-20,20]", "测量值<20.00 扣2.00分;测量值>-20.00 扣2.00分"),
        ("[1.8,3]", "测量值>1.80 扣1.00分;测量值<4.00 扣5.00分"),
        ("≤30", "测量值<40.00 扣2.00分;30.00<测量值<=40.00 扣1.00分"),
        ("≥47°", "测量值>47.00 扣5.00分"),
        ("[V2,V2+30]", "测量值<40.00 扣2.00分;35.00<测量值<=40.00 扣1.00分"),
    ]
    
    for green, over in test_data:
        print("=" * 80)
        print(f"测试用例: 绿色区间='{green}', 超标区间='{over}'")
        print("-" * 40)
        
        green_intervals = parse_green_interval(green)
        over_intervals = parse_over_interval(over)
        
        print(f"绿色区间解析结果: {green_intervals}")
        print(f"超标区间解析结果: {over_intervals}")
        
        # 检查冲突
        if green_intervals and over_intervals:
            has_conflict = False
            for g_min, g_max, g_op in green_intervals:
                for o_min, o_max, o_op in over_intervals:
                    overlap_start = max(g_min, o_min)
                    overlap_end = min(g_max, o_max)
                    if overlap_start < overlap_end:
                        has_conflict = True
                        print(f"发现冲突: 绿色{(g_min, g_max, g_op)} 与 超标{(o_min, o_max, o_op)} 重叠")
            
            if not has_conflict:
                print("✅ 无冲突")
        else:
            print("⚠️ 无法比较（包含变量或解析失败）")
        
        print()

if __name__ == "__main__":
    test_cases()
