# -*- coding: utf-8 -*-
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from datetime import datetime

# 数据库配置
DATABASE_URL = 'mysql+pymysql://root:tanxi219.@localhost:3306/data_base'
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)


def read_and_process_json(file_path):
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    
    df = pd.read_json(file_path, lines=True)
    print(f"原始数据行数: {len(df)}")
    
    required_columns = ['request_host', 'client_ip', 'request_url']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"缺少必要字段: {missing_columns}")
        return None
    
    df['request_url'] = df['request_url'].str.split('?').str[0]
    print("已处理request_url字段，移除查询参数")
    
    # # // 处理.css结尾的URL，只保留/css
    df['request_url'] = df['request_url'].where(~df['request_url'].str.endswith('.css'), '/' + df['request_url'].str.split('/').str[1])
    print("已处理request_url字段，对.css结尾URL只保留/css路径")
    
    # # // 处理.js结尾的URL，只保留/js
    df['request_url'] = df['request_url'].where(~df['request_url'].str.endswith('.js'), '/' + df['request_url'].str.split('/').str[1])
    print("已处理request_url字段，对.js结尾URL只保留/js路径")

      # # // 处理.js结尾的URL，只保留/js
    df['request_url'] = df['request_url'].where(~df['request_url'].str.endswith('.png'), '/' + df['request_url'].str.split('/').str[1])
    print("已处理request_url字段，对.png结尾URL只保留/png路径")
    
    df_unique = df.drop_duplicates(subset=required_columns)
    print(f"去重后数据行数: {len(df_unique)}")
    
    df_final = df_unique[required_columns].copy()
    df_final['system_name'] = None
    
    import_time = datetime.now().strftime('%Y%m%d%H%M')
    df_final['import_time'] = import_time
    
    return df_final


def import_to_database(df):
    if df is None or df.empty:
        print("没有数据需要导入")
        return

    try:
        df.to_sql(
            name='abi_foc_data',
            con=engine,
            if_exists='append',
            index=False,
            method='multi'
        )
        print(f"成功导入 {len(df)} 条数据")
    except Exception as e:
        print(f"导入数据时出错: {e}")


def update_system_names():
    update_sql = """
    UPDATE abi_foc_data a
    INNER JOIN ip_system_mapping m ON a.client_ip = m.client_ip
    SET a.system_name = m.system_name
    WHERE a.system_name IS NULL OR a.system_name = ''
    """

    try:
        with engine.connect() as conn:
            result = conn.execute(text(update_sql))
            conn.commit()
            print(f"成功更新 {result.rowcount} 条记录的系统名称")
    except Exception as e:
        print(f"更新系统名称时出错: {e}")


def main():
    json_file = "/Users/<USER>/PycharmProjects/workPro/hcs-new-foc.2025.07.21"
    print("开始处理数据...")
    print("1. 读取并处理JSON文件...")
    df = read_and_process_json(json_file)
    print("2. 导入数据到数据库...")
    import_to_database(df)
    print("3. 根据ip_system_mapping表更新系统名称...")
    update_system_names()
    print("数据处理完成！")


if __name__ == "__main__":
    main()
