import os
import subprocess
import sys

# 检查socat是否安装
def check_socat_installed():
    try:
        subprocess.run(['socat', '-h'], capture_output=True, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

# 安装socat
def install_socat():
    print('socat未安装，正在尝试安装...')
    try:
        subprocess.run(['brew', 'install', 'socat'], check=True)
        print('socat安装成功！')
        return True
    except subprocess.SubprocessError:
        print('安装失败，请手动安装socat: brew install socat')
        return False

# 创建COM端口映射
def create_com_mappings():
    # 假设我们之前创建的虚拟串口是/dev/ttys032和/dev/ttys033
    # 您可能需要根据实际情况修改这些路径
    virtual_ports = ['/dev/ttys032', '/dev/ttys033']
    com_ports = ['COM4', 'COM8']
    processes = []

    for com, virtual in zip(com_ports, virtual_ports):
        print(f'创建 {com} 到 {virtual} 的映射...')
        # 创建从COM端口到虚拟串口的映射
        cmd = [
            'socat',
            f'pty,link=/tmp/{com},raw,echo=0',
            f'pty,link={virtual},raw,echo=0'
        ]
        try:
            proc = subprocess.Popen(cmd)
            processes.append(proc)
            print(f'{com} 映射成功！')
        except subprocess.SubprocessError as e:
            print(f'创建 {com} 映射失败: {e}')

    return processes

if __name__ == '__main__':
    if not check_socat_installed():
        if not install_socat():
            sys.exit(1)

    processes = create_com_mappings()

    print('串口映射已创建，请按Ctrl+C停止')
    try:
        while True:
            pass
    except KeyboardInterrupt:
        print('正在停止所有映射...')
        for proc in processes:
            proc.terminate()
        print('所有映射已停止')