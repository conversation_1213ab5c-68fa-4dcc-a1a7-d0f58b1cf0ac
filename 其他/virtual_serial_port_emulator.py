import pty
import os
import sys
import threading

def read_port(master, port_name):
    """读取串口数据并打印"""
    while True:
        try:
            data = os.read(master, 1024)
            if data:
                print(f"从{port_name}接收: {data.decode('utf-8', errors='ignore')}")
        except Exception as e:
            print(f"读取{port_name}时发生错误: {str(e)}")
            break

def create_virtual_serial_ports():
    """创建虚拟串口对"""
    print("正在创建虚拟串口对...")

    # 创建主从伪终端对1
    master1, slave1 = pty.openpty()
    # 创建主从伪终端对2
    master2, slave2 = pty.openpty()

    # 获取伪终端设备路径
    port1 = os.ttyname(slave1)
    port2 = os.ttyname(slave2)

    print(f"虚拟串口对创建成功!")
    print(f"串口1: {port1}")
    print(f"串口2: {port2}")
    print("提示: 应用程序可以连接到这些串口路径进行通信")
    print("按Ctrl+C停止模拟...")

    # 创建线程来转发数据
    def forward_data(master_from, master_to, name_from, name_to):
        while True:
            try:
                data = os.read(master_from, 1024)
                if data:
                    os.write(master_to, data)
                    print(f"从{name_from}转发到{name_to}: {data.decode('utf-8', errors='ignore')}")
            except Exception as e:
                print(f"数据转发时发生错误: {str(e)}")
                break

    # 启动数据转发线程
    thread1 = threading.Thread(target=forward_data, args=(master1, master2, port1, port2))
    thread2 = threading.Thread(target=forward_data, args=(master2, master1, port2, port1))

    thread1.daemon = True
    thread2.daemon = True

    thread1.start()
    thread2.start()

    # 保持脚本运行
    try:
        while True:
            # 读取用户输入并发送到串口1
            input_data = sys.stdin.readline()
            if input_data:
                os.write(master1, input_data.encode())
    except KeyboardInterrupt:
        print("停止虚拟串口模拟...")
        # 关闭伪终端
        os.close(master1)
        os.close(slave1)
        os.close(master2)
        os.close(slave2)
        print("虚拟串口已关闭。")

if __name__ == "__main__":
    create_virtual_serial_ports()