import pty
import os
import sys
import time

# 创建虚拟串口对
master, slave = pty.openpty()
slave_path = os.ttyn<PERSON>(slave)

print(f"虚拟串口已创建: {slave_path}")
print("请在JAR文件中使用此串口路径")
print("按Ctrl+C退出模拟")

try:
    # 模拟串口通信
    while True:
        # 读取从虚拟串口发来的数据
        data = os.read(master, 1024)
        if data:
            print(f"接收到数据: {data.decode('utf-8', errors='ignore')}")
            # 这里可以添加响应逻辑
            # 例如，发送一个简单的响应
            response = "模拟串口响应\r\n"
            os.write(master, response.encode('utf-8'))
            print(f"发送响应: {response}")
        time.sleep(0.1)
except KeyboardInterrupt:
    print("退出串口模拟")
finally:
    os.close(master)
    os.close(slave)