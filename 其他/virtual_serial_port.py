import subprocess
import os
import sys
import time

def check_socat_installed():
    """检查系统是否安装了socat"""
    try:
        subprocess.run(["socat", "-h"], capture_output=True, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

def create_virtual_serial_ports():
    """创建虚拟串口对"""
    # 确保socat已安装
    if not check_socat_installed():
        print("错误: 未找到socat工具。请先安装socat，例如使用brew install socat。")
        sys.exit(1)

    # 创建临时目录用于存放串口设备文件
    temp_dir = os.path.join(os.path.expanduser("~"), "virtual_serial_ports")
    os.makedirs(temp_dir, exist_ok=True)

    port1 = os.path.join(temp_dir, "ttyS0")
    port2 = os.path.join(temp_dir, "ttyS1")

    # 确保之前的串口文件已删除
    for port in [port1, port2]:
        if os.path.exists(port):
            os.remove(port)

    print(f"正在创建虚拟串口对: {port1} 和 {port2}")

    # 使用socat创建虚拟串口对
    try:
        # 启动socat进程
        proc = subprocess.Popen([
            "socat",
            f"PTY,link={port1},raw,echo=0",
            f"PTY,link={port2},raw,echo=0"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        print("虚拟串口对创建成功!")
        print(f"串口1: {port1}")
        print(f"串口2: {port2}")
        print("按Ctrl+C停止模拟...")

        # 保持脚本运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("停止虚拟串口模拟...")
        proc.terminate()
        # 清理临时文件
        for port in [port1, port2]:
            if os.path.exists(port):
                os.remove(port)
        print("虚拟串口已清理。")
    except Exception as e:
        print(f"创建虚拟串口时发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    create_virtual_serial_ports()