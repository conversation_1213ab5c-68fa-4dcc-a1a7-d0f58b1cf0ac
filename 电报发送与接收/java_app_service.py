#!/usr/bin/env python3
"""
独立的Java应用程序服务
可以单独启动和停止Java应用程序
"""

import subprocess
import os
import sys
import time
import signal
import atexit
import json
import threading
from typing import Optional, Dict


class JavaAppService:
    """Java应用程序服务管理器"""
    
    def __init__(self, jar_path: str):
        self.jar_path = jar_path
        self.java_process: Optional[subprocess.Popen] = None
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.status_file = '/tmp/java_app_service_status.json'
        
        # 验证JAR文件存在
        if not os.path.exists(jar_path):
            raise FileNotFoundError(f'JAR文件不存在: {jar_path}')
        
        # 注册清理函数
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f'\n收到信号 {signum}，正在停止Java应用服务...')
        self.stop_service()
        sys.exit(0)
    
    def _get_serial_port_info(self) -> Optional[str]:
        """从串口服务获取COM4端口信息"""
        status_file = '/tmp/serial_port_service_status.json'
        
        try:
            if os.path.exists(status_file):
                with open(status_file, 'r') as f:
                    status = json.load(f)
                
                if status.get('service_running') and 'ports' in status:
                    com4_info = status['ports'].get('COM4')
                    if com4_info and com4_info.get('running'):
                        return com4_info.get('device_path')
            
            print('✗ 无法获取COM4端口信息，请确保串口服务正在运行')
            return None
            
        except Exception as e:
            print(f'读取串口服务状态失败: {e}')
            return None
    
    def start_service(self, serial_port: Optional[str] = None) -> bool:
        """启动Java应用服务"""
        if self.running:
            print('Java应用服务已经在运行中')
            return True
        
        print('=' * 50)
        print('启动Java应用程序服务...')
        print('=' * 50)
        
        # 如果没有指定串口，尝试从串口服务获取
        if not serial_port:
            serial_port = self._get_serial_port_info()
            if not serial_port:
                print('✗ 无法获取串口信息，Java应用启动失败')
                return False
        
        # 构建Java命令
        command = [
            'java', '-jar', self.jar_path,
            f'--serial.port={serial_port}',
            '--serial.baudRate=9600',
            '--serial.dataBits=8',
            '--serial.stopBits=1',
            '--serial.parity=N'
        ]
        
        try:
            print(f'启动命令: {" ".join(command)}')
            print(f'使用串口: {serial_port}')
            
            # 启动Java进程
            self.java_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.running = True
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_process, daemon=True)
            self.monitor_thread.start()
            
            self._save_status()
            
            print(f'✓ Java应用程序已启动 (PID: {self.java_process.pid})')
            return True
            
        except Exception as e:
            print(f'✗ Java应用程序启动失败: {e}')
            return False
    
    def stop_service(self):
        """停止Java应用服务"""
        if not self.running:
            print('Java应用服务未运行')
            return
        
        print('正在停止Java应用程序...')
        
        self.running = False
        
        if self.java_process and self.java_process.poll() is None:
            try:
                # 尝试优雅终止
                self.java_process.terminate()
                self.java_process.wait(timeout=5)
                print('✓ Java进程已优雅停止')
            except subprocess.TimeoutExpired:
                # 强制终止
                print('强制终止Java进程...')
                self.java_process.kill()
                self.java_process.wait()
                print('✓ Java进程已强制终止')
        
        # 等待监控线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2)
        
        self._remove_status_file()
        print('✓ Java应用服务已停止')
    
    def restart_service(self, serial_port: Optional[str] = None) -> bool:
        """重启Java应用服务"""
        print('重启Java应用程序服务...')
        self.stop_service()
        time.sleep(2)
        return self.start_service(serial_port)
    
    def get_service_status(self) -> Dict:
        """获取服务状态"""
        status = {
            'service_running': self.running,
            'jar_path': self.jar_path,
            'process_id': self.java_process.pid if self.java_process else None,
            'process_alive': self.java_process.poll() is None if self.java_process else False
        }
        return status
    
    def _monitor_process(self):
        """监控Java进程"""
        if not self.java_process:
            return
        
        print('开始监控Java进程输出...')
        
        try:
            while self.running and self.java_process.poll() is None:
                # 读取标准输出
                if self.java_process.stdout:
                    output = self.java_process.stdout.readline()
                    if output:
                        print(f'[JAVA] {output.strip()}')
                
                # 读取错误输出
                if self.java_process.stderr:
                    error = self.java_process.stderr.readline()
                    if error:
                        print(f'[JAVA ERROR] {error.strip()}')
                
                time.sleep(0.1)
            
            # 进程结束处理
            if self.java_process.poll() is not None:
                # 获取剩余输出
                stdout, stderr = self.java_process.communicate()
                if stdout:
                    print(f'[JAVA FINAL] {stdout}')
                if stderr:
                    print(f'[JAVA ERROR FINAL] {stderr}')
                
                exit_code = self.java_process.returncode
                print(f'Java进程已结束，退出码: {exit_code}')
                
                # 如果进程异常退出，标记服务为停止状态
                if exit_code != 0:
                    self.running = False
                    self._remove_status_file()
        
        except Exception as e:
            print(f'监控Java进程时发生错误: {e}')
    
    def _save_status(self):
        """保存状态到文件"""
        try:
            status = self.get_service_status()
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
        except Exception as e:
            print(f'保存状态文件失败: {e}')
    
    def _remove_status_file(self):
        """删除状态文件"""
        try:
            if os.path.exists(self.status_file):
                os.remove(self.status_file)
        except Exception as e:
            print(f'删除状态文件失败: {e}')
    
    def cleanup(self):
        """清理资源"""
        if self.running:
            self.stop_service()
    
    def run_daemon(self):
        """以守护进程模式运行"""
        if not self.start_service():
            sys.exit(1)
        
        print('Java应用程序服务正在运行...')
        print('按 Ctrl+C 停止服务')
        
        try:
            while self.running:
                time.sleep(1)
                # 定期更新状态文件
                if self.running:
                    self._save_status()
                
                # 检查进程是否还活着
                if self.java_process and self.java_process.poll() is not None:
                    print('Java进程已退出，停止服务')
                    self.running = False
                    break
        
        except KeyboardInterrupt:
            print('\n用户中断服务')
        finally:
            self.stop_service()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Java应用程序服务')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status'], 
                       help='服务操作: start(启动), stop(停止), restart(重启), status(状态)')
    parser.add_argument('--jar', default='sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar',
                       help='JAR文件路径 (默认: sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar)')
    parser.add_argument('--serial-port', help='指定串口路径 (可选，默认从串口服务获取)')
    
    args = parser.parse_args()
    
    # 获取JAR文件的完整路径
    if not os.path.isabs(args.jar):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        jar_path = os.path.join(current_dir, args.jar)
    else:
        jar_path = args.jar
    
    try:
        service = JavaAppService(jar_path)
    except FileNotFoundError as e:
        print(f'错误: {e}')
        sys.exit(1)
    
    if args.action == 'start':
        if service.start_service(args.serial_port):
            service.run_daemon()
        else:
            sys.exit(1)
    
    elif args.action == 'stop':
        service.stop_service()
    
    elif args.action == 'restart':
        if service.restart_service(args.serial_port):
            service.run_daemon()
        else:
            sys.exit(1)
    
    elif args.action == 'status':
        status = service.get_service_status()
        print('Java应用程序服务状态:')
        print(f'服务运行: {"是" if status["service_running"] else "否"}')
        print(f'JAR文件: {status["jar_path"]}')
        if status['process_id']:
            print(f'进程ID: {status["process_id"]}')
            print(f'进程存活: {"是" if status["process_alive"] else "否"}')


if __name__ == "__main__":
    main()
