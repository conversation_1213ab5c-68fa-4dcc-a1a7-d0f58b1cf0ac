#!/usr/bin/env python3
"""
虚拟串口测试脚本
用于测试COM4和COM8虚拟串口是否正常工作
"""

import serial
import time
import threading
import sys
from virtual_serial_service import VirtualSerialPort


def test_serial_communication(device_path, port_name):
    """测试串口通信"""
    try:
        # 打开串口
        ser = serial.Serial(
            port=device_path,
            baudrate=9600,
            bytesize=8,
            parity='N',
            stopbits=1,
            timeout=1
        )
        
        print(f"✓ {port_name} 串口打开成功: {device_path}")
        
        # 发送测试数据
        test_data = b"Hello from " + port_name.encode() + b"\n"
        ser.write(test_data)
        print(f"[{port_name}] 发送: {test_data}")
        
        # 读取响应
        time.sleep(0.1)
        if ser.in_waiting > 0:
            response = ser.read(ser.in_waiting)
            print(f"[{port_name}] 接收: {response}")
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"✗ {port_name} 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("虚拟串口测试程序")
    print("=" * 40)
    
    # 创建虚拟串口
    com4 = VirtualSerialPort('COM4')
    com8 = VirtualSerialPort('COM8')
    
    ports = []
    
    try:
        # 启动虚拟串口
        if com4.start():
            ports.append(com4)
        if com8.start():
            ports.append(com8)
        
        if not ports:
            print("✗ 没有成功创建任何虚拟串口")
            return
        
        print(f"✓ 成功创建 {len(ports)} 个虚拟串口")
        
        # 等待端口稳定
        time.sleep(1)
        
        # 测试串口通信
        print("\n开始测试串口通信...")
        
        # 检查是否安装了pyserial
        try:
            import serial
        except ImportError:
            print("✗ 需要安装pyserial库")
            print("请运行: pip install pyserial")
            return
        
        # 测试每个端口
        for port in ports:
            device_path = port.get_device_path()
            if device_path:
                test_serial_communication(device_path, port.com_port)
        
        print("\n测试完成，按Ctrl+C退出...")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断测试")
    
    finally:
        # 清理资源
        print("正在清理资源...")
        for port in ports:
            port.stop()
        print("✓ 测试程序结束")


if __name__ == "__main__":
    main()
