#!/usr/bin/env python3
"""
服务测试脚本
测试虚拟串口服务和Java应用程序服务是否正常工作
"""

import subprocess
import os
import sys
import time
import json
from service_manager import ServiceManager


def test_serial_service():
    """测试虚拟串口服务"""
    print('=' * 50)
    print('测试虚拟串口服务')
    print('=' * 50)
    
    manager = ServiceManager()
    
    # 1. 启动串口服务
    print('1. 启动串口服务...')
    if not manager.start_serial_service():
        print('✗ 串口服务启动失败')
        return False
    
    # 2. 检查服务状态
    print('2. 检查服务状态...')
    time.sleep(1)
    status = manager.get_services_status()
    
    if status['serial_service']['running']:
        print('✓ 串口服务运行正常')
        
        # 显示端口信息
        details = status['serial_service']['details']
        if 'ports' in details:
            print('端口信息:')
            for com_port, port_info in details['ports'].items():
                device_path = port_info.get('device_path')
                running = port_info.get('running')
                print(f'  {com_port}: {device_path} ({"运行中" if running else "已停止"})')
        
        # 3. 测试串口通信（如果安装了pyserial）
        print('3. 测试串口通信...')
        try:
            import serial
            
            # 获取COM4设备路径
            com4_info = details['ports'].get('COM4')
            if com4_info and com4_info.get('device_path'):
                device_path = com4_info['device_path']
                
                # 尝试打开串口
                ser = serial.Serial(
                    port=device_path,
                    baudrate=9600,
                    bytesize=8,
                    parity='N',
                    stopbits=1,
                    timeout=1
                )
                
                # 发送测试数据
                test_data = b"Hello COM4\n"
                ser.write(test_data)
                print(f'发送测试数据: {test_data}')
                
                # 读取响应
                time.sleep(0.1)
                if ser.in_waiting > 0:
                    response = ser.read(ser.in_waiting)
                    print(f'接收响应数据: {response}')
                
                ser.close()
                print('✓ 串口通信测试成功')
            else:
                print('⚠ 无法获取COM4设备路径')
                
        except ImportError:
            print('⚠ 未安装pyserial，跳过串口通信测试')
        except Exception as e:
            print(f'⚠ 串口通信测试失败: {e}')
        
        # 4. 停止服务
        print('4. 停止串口服务...')
        if manager.stop_serial_service():
            print('✓ 串口服务停止成功')
            return True
        else:
            print('✗ 串口服务停止失败')
            return False
    else:
        print('✗ 串口服务未正常运行')
        return False


def test_java_service():
    """测试Java应用程序服务"""
    print('=' * 50)
    print('测试Java应用程序服务')
    print('=' * 50)
    
    manager = ServiceManager()
    
    # 1. 先启动串口服务（Java服务需要串口）
    print('1. 启动串口服务...')
    if not manager.start_serial_service():
        print('✗ 串口服务启动失败，无法测试Java服务')
        return False
    
    time.sleep(2)
    
    # 2. 启动Java服务
    print('2. 启动Java应用程序服务...')
    if not manager.start_java_service():
        print('✗ Java服务启动失败')
        manager.stop_serial_service()
        return False
    
    # 3. 检查服务状态
    print('3. 检查服务状态...')
    time.sleep(2)
    status = manager.get_services_status()
    
    if status['java_service']['running']:
        print('✓ Java服务运行正常')
        
        # 显示进程信息
        details = status['java_service']['details']
        if 'process_id' in details:
            print(f'进程ID: {details["process_id"]}')
            print(f'进程存活: {"是" if details.get("process_alive") else "否"}')
        
        # 4. 等待一段时间观察Java程序运行
        print('4. 观察Java程序运行状态...')
        for i in range(5):
            time.sleep(1)
            status = manager.get_services_status()
            if not status['java_service']['running']:
                print('⚠ Java服务在运行过程中停止了')
                break
            print(f'  运行中... ({i+1}/5)')
        
        # 5. 停止服务
        print('5. 停止所有服务...')
        if manager.stop_all_services():
            print('✓ 所有服务停止成功')
            return True
        else:
            print('✗ 服务停止时出现问题')
            return False
    else:
        print('✗ Java服务未正常运行')
        manager.stop_serial_service()
        return False


def test_service_manager():
    """测试服务管理器"""
    print('=' * 50)
    print('测试服务管理器')
    print('=' * 50)
    
    manager = ServiceManager()
    
    # 1. 测试启动所有服务
    print('1. 启动所有服务...')
    if not manager.start_all_services():
        print('✗ 启动所有服务失败')
        return False
    
    # 2. 检查状态
    print('2. 检查所有服务状态...')
    time.sleep(2)
    manager.show_status()
    
    # 3. 等待一段时间
    print('3. 等待服务运行...')
    time.sleep(5)
    
    # 4. 停止所有服务
    print('4. 停止所有服务...')
    if manager.stop_all_services():
        print('✓ 服务管理器测试成功')
        return True
    else:
        print('✗ 停止服务时出现问题')
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='服务测试脚本')
    parser.add_argument('test', choices=['serial', 'java', 'manager', 'all'], 
                       help='测试类型: serial(串口服务), java(Java服务), manager(服务管理器), all(全部)')
    
    args = parser.parse_args()
    
    print('服务测试开始...')
    print(f'当前目录: {os.getcwd()}')
    
    success = True
    
    if args.test == 'serial' or args.test == 'all':
        if not test_serial_service():
            success = False
        print()
    
    if args.test == 'java' or args.test == 'all':
        if not test_java_service():
            success = False
        print()
    
    if args.test == 'manager' or args.test == 'all':
        if not test_service_manager():
            success = False
        print()
    
    print('=' * 50)
    if success:
        print('✓ 所有测试通过')
    else:
        print('✗ 部分测试失败')
    print('=' * 50)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
