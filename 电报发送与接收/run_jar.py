import subprocess
import os
import subprocess
import sys
import pty
import os
import fcntl
import termios
import select
import threading

current_dir = os.path.dirname(os.path.abspath(__file__))
jar_file = os.path.join(current_dir, "sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar")

# 使用Python的pty模块创建虚拟串口对
class VirtualSerialPort:
    def __init__(self, com_port):
        self.com_port = com_port
        self.master = None
        self.slave = None
        self.slave_name = None
        self.link_path = f'/tmp/{com_port}'
        self.running = False
        self.thread = None

    def start(self):
        # 清理可能存在的链接
        if os.path.exists(self.link_path):
            try:
                if os.path.islink(self.link_path) or os.path.isfile(self.link_path):
                    os.unlink(self.link_path)
                elif os.path.isdir(self.link_path):
                    os.rmdir(self.link_path)
                print(f'已清理旧的 {self.link_path}')
            except Exception as e:
                print(f'清理旧的 {self.link_path} 失败: {e}')
                return False

        # 创建新的pty
        try:
            self.master, self.slave = pty.openpty()
            self.slave_name = os.ttyname(self.slave)
            print(f'创建了pty设备: {self.slave_name}')
        except Exception as e:
            print(f'创建pty设备失败: {e}')
            return False

        # 不需要创建符号链接，直接使用pty设备
        self.running = True
        self.thread = threading.Thread(target=self._forward_data)
        self.thread.daemon = True
        self.thread.start()

        print(f'虚拟串口 {self.com_port} 已启动，使用设备: {self.slave_name}')
        return True

    def _forward_data(self):
        while self.running:
            r, w, e = select.select([self.master], [], [], 1)
            if self.master in r:
                try:
                    data = os.read(self.master, 1024)
                    if not data:
                        break
                    # 回显数据
                    os.write(self.master, data)
                except Exception as e:
                    print(f'数据转发错误: {e}')
                    break

    def stop(self):
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(1)
        if self.master:
            try:
                os.close(self.master)
            except:
                pass
        if self.slave:
            try:
                os.close(self.slave)
            except:
                pass
        print(f'虚拟串口 {self.com_port} 已停止')

    def get_device_path(self):
        return self.slave_name

# 创建虚拟串口
def create_virtual_ports():
    com_ports = ['COM4', 'COM8']
    ports = []

    for com in com_ports:
        print(f'创建虚拟串口 {com}...')
        port = VirtualSerialPort(com)
        if not port.start():
            print(f'虚拟串口 {com} 创建失败')
            # 清理已创建的端口
            for p in ports:
                p.stop()
            return None
        ports.append(port)
        print(f'虚拟串口 {com} 创建成功: /tmp/{com}')

    return ports

# 添加COM端口映射功能
def map_com_ports():
    # 定义COM端口到macOS设备的映射
    com_mappings = {
        'COM4': '/dev/ttys032',
        'COM8': '/dev/ttys033'
    }

    # 创建符号链接
    for com_port, device_path in com_mappings.items():
        link_path = f'/tmp/{com_port}'
        # 检查链接是否已存在
        if os.path.exists(link_path):
            # 如果存在且是符号链接，则删除
            if os.path.islink(link_path):
                os.unlink(link_path)
            else:
                print(f'错误: {link_path} 已存在且不是符号链接')
                return False
        # 创建符号链接
        try:
            os.symlink(device_path, link_path)
            print(f'已创建 {com_port} 到 {device_path} 的映射')
            # 设置权限
            os.chmod(link_path, 0o777)
            os.chmod(device_path, 0o777)
        except OSError as e:
            print(f'创建 {com_port} 映射失败: {e}')
            return False
    return True

# 构建命令，添加虚拟串口参数
command = ["java", "-jar", jar_file, "--serial.port=/tmp/COM4", "--serial.baudRate=9600", "--serial.dataBits=8", "--serial.stopBits=1", "--serial.parity=N"]

try:
    # 执行命令
    print(f"正在执行命令: {' '.join(command)}")
    result = subprocess.run(command, check=True, capture_output=True, text=True)
    print("命令执行成功!")
    print("输出:")
    print(result.stdout)
except subprocess.CalledProcessError as e:
    print(f"命令执行失败，退出码: {e.returncode}")
    print("标准输出:")
    print(e.stdout)
    print("错误输出:")
    print(e.stderr)
except Exception as e:
    print(f"发生错误: {str(e)}")

if __name__ == "__main__":
    # 注册清理函数
    import atexit
    atexit.register(cleanup)

    # 创建虚拟串口
    com4 = VirtualSerialPort('COM4')
    com8 = VirtualSerialPort('COM8')

    if not com4.start() or not com8.start():
        print("虚拟串口创建失败，无法启动JAR文件")
        sys.exit(1)

    virtual_ports = [com4, com8]

    # 获取实际的设备路径
    com4_path = com4.get_device_path()
    com8_path = com8.get_device_path()

    print(f'COM4 实际设备路径: {com4_path}')
    print(f'COM8 实际设备路径: {com8_path}')

    try:
        # 执行命令
        command = ["java", "-jar", jar_file, f"--serial.port={com4_path}", "--serial.baudRate=9600", "--serial.dataBits=8", "--serial.stopBits=1", "--serial.parity=N"]
        print(f"正在执行命令: {' '.join(command)}")
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print("命令执行成功!")
        print("输出:")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败，退出码: {e.returncode}")
        print("标准输出:")
        print(e.stdout)
        print("错误输出:")
        print(e.stderr)
        cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {str(e)}")
        cleanup()
        sys.exit(1)

    cleanup()
    # 停止虚拟串口
    for port in virtual_ports:
        port.stop()
    print('所有虚拟串口已停止')