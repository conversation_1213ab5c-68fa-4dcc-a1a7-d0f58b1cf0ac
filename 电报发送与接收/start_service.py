#!/usr/bin/env python3
"""
简化的启动脚本
快速启动虚拟串口服务和Java应用程序
"""

import os
import sys
from virtual_serial_service import VirtualSerialService


def main():
    """主函数"""
    print("虚拟串口服务启动器")
    print("=" * 40)
    
    # 检查JAR文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    jar_file = os.path.join(current_dir, "sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar")
    
    if not os.path.exists(jar_file):
        print(f"错误: 找不到JAR文件")
        print(f"期望位置: {jar_file}")
        print("请确保JAR文件在当前目录下")
        sys.exit(1)
    
    print(f"找到JAR文件: {os.path.basename(jar_file)}")
    
    # 启动服务
    try:
        service = VirtualSerialService()
        service.run(jar_file)
    except KeyboardInterrupt:
        print("\n用户中断服务")
    except Exception as e:
        print(f"服务运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
