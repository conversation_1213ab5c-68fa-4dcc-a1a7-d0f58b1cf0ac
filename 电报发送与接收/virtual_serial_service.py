#!/usr/bin/env python3
"""
虚拟串口服务
用于模拟COM4和COM8串口，并启动Java应用程序
"""

import subprocess
import os
import sys
import pty
import select
import threading
import time
import signal
import atexit
from typing import List, Optional


class VirtualSerialPort:
    """虚拟串口类"""
    
    def __init__(self, com_port: str):
        self.com_port = com_port
        self.master = None
        self.slave = None
        self.slave_name = None
        self.running = False
        self.thread = None
        
    def start(self) -> bool:
        """启动虚拟串口"""
        try:
            # 创建pty对
            self.master, self.slave = pty.openpty()
            self.slave_name = os.ttyname(self.slave)
            
            # 设置串口参数（9600, 8, N, 1）
            import termios
            attrs = termios.tcgetattr(self.slave)
            attrs[4] = attrs[5] = termios.B9600  # 波特率
            attrs[2] &= ~termios.CSIZE
            attrs[2] |= termios.CS8  # 8位数据位
            attrs[2] &= ~termios.PARENB  # 无校验
            attrs[2] &= ~termios.CSTOPB  # 1位停止位
            termios.tcsetattr(self.slave, termios.TCSANOW, attrs)
            
            self.running = True
            self.thread = threading.Thread(target=self._data_handler, daemon=True)
            self.thread.start()
            
            print(f'✓ 虚拟串口 {self.com_port} 已启动: {self.slave_name}')
            return True
            
        except Exception as e:
            print(f'✗ 虚拟串口 {self.com_port} 启动失败: {e}')
            return False
    
    def _data_handler(self):
        """数据处理线程"""
        while self.running:
            try:
                # 使用select监听数据
                r, _, _ = select.select([self.master], [], [], 1.0)
                if self.master in r:
                    data = os.read(self.master, 1024)
                    if data:
                        # 简单回显处理
                        print(f'[{self.com_port}] 接收数据: {data.hex()}')
                        # 可以在这里添加数据处理逻辑
                        os.write(self.master, data)  # 回显
            except Exception as e:
                if self.running:
                    print(f'[{self.com_port}] 数据处理错误: {e}')
                break
    
    def stop(self):
        """停止虚拟串口"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)
        
        if self.master:
            try:
                os.close(self.master)
            except:
                pass
        if self.slave:
            try:
                os.close(self.slave)
            except:
                pass
        
        print(f'✓ 虚拟串口 {self.com_port} 已停止')
    
    def get_device_path(self) -> Optional[str]:
        """获取设备路径"""
        return self.slave_name


class VirtualSerialService:
    """虚拟串口服务管理器"""
    
    def __init__(self):
        self.ports: List[VirtualSerialPort] = []
        self.java_process: Optional[subprocess.Popen] = None
        self.running = False
        
        # 注册清理函数
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f'\n收到信号 {signum}，正在清理资源...')
        self.cleanup()
        sys.exit(0)
    
    def create_virtual_ports(self) -> bool:
        """创建虚拟串口COM4和COM8"""
        com_ports = ['COM4', 'COM8']
        
        print('正在创建虚拟串口...')
        for com_port in com_ports:
            port = VirtualSerialPort(com_port)
            if port.start():
                self.ports.append(port)
            else:
                # 如果创建失败，清理已创建的端口
                self.cleanup_ports()
                return False
        
        print(f'✓ 成功创建 {len(self.ports)} 个虚拟串口')
        return True
    
    def start_java_application(self, jar_path: str) -> bool:
        """启动Java应用程序"""
        if not self.ports:
            print('✗ 没有可用的虚拟串口')
            return False
        
        # 获取COM4的设备路径用于Java应用
        com4_port = next((p for p in self.ports if p.com_port == 'COM4'), None)
        if not com4_port:
            print('✗ 找不到COM4端口')
            return False
        
        device_path = com4_port.get_device_path()
        
        # 构建Java命令
        command = [
            'java', '-jar', jar_path,
            f'--serial.port={device_path}',
            '--serial.baudRate=9600',
            '--serial.dataBits=8',
            '--serial.stopBits=1',
            '--serial.parity=N'
        ]
        
        try:
            print(f'正在启动Java应用程序...')
            print(f'命令: {" ".join(command)}')
            
            # 启动Java进程（非阻塞）
            self.java_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print(f'✓ Java应用程序已启动 (PID: {self.java_process.pid})')
            return True
            
        except Exception as e:
            print(f'✗ Java应用程序启动失败: {e}')
            return False
    
    def monitor_java_process(self):
        """监控Java进程"""
        if not self.java_process:
            return
        
        print('开始监控Java进程输出...')
        print('按 Ctrl+C 停止服务')
        
        try:
            # 实时读取输出
            while self.java_process.poll() is None:
                output = self.java_process.stdout.readline()
                if output:
                    print(f'[JAVA] {output.strip()}')
                
                # 检查错误输出
                if self.java_process.stderr:
                    error = self.java_process.stderr.readline()
                    if error:
                        print(f'[JAVA ERROR] {error.strip()}')
                
                time.sleep(0.1)
            
            # 进程结束，获取最终输出
            stdout, stderr = self.java_process.communicate()
            if stdout:
                print(f'[JAVA FINAL] {stdout}')
            if stderr:
                print(f'[JAVA ERROR FINAL] {stderr}')
            
            print(f'Java进程已结束，退出码: {self.java_process.returncode}')
            
        except KeyboardInterrupt:
            print('\n用户中断，正在停止服务...')
        except Exception as e:
            print(f'监控Java进程时发生错误: {e}')
    
    def cleanup_ports(self):
        """清理虚拟串口"""
        for port in self.ports:
            port.stop()
        self.ports.clear()
    
    def cleanup_java_process(self):
        """清理Java进程"""
        if self.java_process and self.java_process.poll() is None:
            print('正在停止Java进程...')
            self.java_process.terminate()
            try:
                self.java_process.wait(timeout=5)
                print('✓ Java进程已停止')
            except subprocess.TimeoutExpired:
                print('强制终止Java进程...')
                self.java_process.kill()
                self.java_process.wait()
                print('✓ Java进程已强制终止')
    
    def cleanup(self):
        """清理所有资源"""
        if not self.running:
            return
        
        self.running = False
        print('\n正在清理资源...')
        
        self.cleanup_java_process()
        self.cleanup_ports()
        
        print('✓ 资源清理完成')
    
    def run(self, jar_path: str):
        """运行服务"""
        self.running = True
        
        print('=' * 50)
        print('虚拟串口服务启动中...')
        print('=' * 50)
        
        # 1. 创建虚拟串口
        if not self.create_virtual_ports():
            print('✗ 虚拟串口创建失败')
            return False
        
        # 显示端口信息
        print('\n端口信息:')
        for port in self.ports:
            print(f'  {port.com_port}: {port.get_device_path()}')
        
        # 2. 启动Java应用程序
        if not self.start_java_application(jar_path):
            print('✗ Java应用程序启动失败')
            self.cleanup()
            return False
        
        # 3. 监控Java进程
        self.monitor_java_process()
        
        # 4. 清理资源
        self.cleanup()
        return True


def main():
    """主函数"""
    # 获取JAR文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    jar_file = os.path.join(current_dir, "sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar")
    
    if not os.path.exists(jar_file):
        print(f'✗ JAR文件不存在: {jar_file}')
        sys.exit(1)
    
    # 创建并运行服务
    service = VirtualSerialService()
    success = service.run(jar_file)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
