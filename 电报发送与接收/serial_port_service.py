#!/usr/bin/env python3
"""
独立的虚拟串口服务
可以单独启动和停止虚拟串口COM4和COM8
"""

import os
import sys
import pty
import select
import threading
import time
import signal
import atexit
import json
import termios
from typing import List, Optional, Dict


class VirtualSerialPort:
    """虚拟串口类"""
    
    def __init__(self, com_port: str):
        self.com_port = com_port
        self.master = None
        self.slave = None
        self.slave_name = None
        self.running = False
        self.thread = None
        
    def start(self) -> bool:
        """启动虚拟串口"""
        try:
            # 创建pty对
            self.master, self.slave = pty.openpty()
            self.slave_name = os.ttyname(self.slave)
            
            # 设置串口参数（9600, 8, N, 1）
            attrs = termios.tcgetattr(self.slave)
            attrs[4] = attrs[5] = termios.B9600  # 波特率
            attrs[2] &= ~termios.CSIZE
            attrs[2] |= termios.CS8  # 8位数据位
            attrs[2] &= ~termios.PARENB  # 无校验
            attrs[2] &= ~termios.CSTOPB  # 1位停止位
            termios.tcsetattr(self.slave, termios.TCSANOW, attrs)
            
            self.running = True
            self.thread = threading.Thread(target=self._data_handler, daemon=True)
            self.thread.start()
            
            print(f'✓ 虚拟串口 {self.com_port} 已启动: {self.slave_name}')
            return True
            
        except Exception as e:
            print(f'✗ 虚拟串口 {self.com_port} 启动失败: {e}')
            return False
    
    def _data_handler(self):
        """数据处理线程"""
        while self.running:
            try:
                # 使用select监听数据
                r, _, _ = select.select([self.master], [], [], 1.0)
                if self.master in r:
                    data = os.read(self.master, 1024)
                    if data:
                        # 记录接收到的数据
                        print(f'[{self.com_port}] 接收数据: {data.hex()} ({len(data)} bytes)')
                        # 简单回显处理
                        os.write(self.master, data)
            except Exception as e:
                if self.running:
                    print(f'[{self.com_port}] 数据处理错误: {e}')
                break
    
    def stop(self):
        """停止虚拟串口"""
        self.running = False
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2)
        
        if self.master:
            try:
                os.close(self.master)
            except:
                pass
        if self.slave:
            try:
                os.close(self.slave)
            except:
                pass
        
        print(f'✓ 虚拟串口 {self.com_port} 已停止')
    
    def get_device_path(self) -> Optional[str]:
        """获取设备路径"""
        return self.slave_name
    
    def is_running(self) -> bool:
        """检查串口是否正在运行"""
        return self.running
    
    def get_status(self) -> Dict:
        """获取串口状态信息"""
        return {
            'com_port': self.com_port,
            'device_path': self.slave_name,
            'running': self.running,
            'thread_alive': self.thread.is_alive() if self.thread else False
        }


class SerialPortService:
    """虚拟串口服务管理器"""
    
    def __init__(self):
        self.ports: Dict[str, VirtualSerialPort] = {}
        self.running = False
        self.status_file = '/tmp/serial_port_service_status.json'
        
        # 注册清理函数
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f'\n收到信号 {signum}，正在停止串口服务...')
        self.stop_service()
        sys.exit(0)
    
    def start_service(self) -> bool:
        """启动串口服务"""
        if self.running:
            print('串口服务已经在运行中')
            return True
        
        print('=' * 50)
        print('启动虚拟串口服务...')
        print('=' * 50)
        
        # 创建COM4和COM8端口
        com_ports = ['COM4', 'COM8']
        success_count = 0
        
        for com_port in com_ports:
            port = VirtualSerialPort(com_port)
            if port.start():
                self.ports[com_port] = port
                success_count += 1
            else:
                print(f'✗ {com_port} 启动失败')
        
        if success_count > 0:
            self.running = True
            self._save_status()
            print(f'✓ 成功启动 {success_count}/{len(com_ports)} 个虚拟串口')
            self._display_port_info()
            return True
        else:
            print('✗ 所有虚拟串口启动失败')
            return False
    
    def stop_service(self):
        """停止串口服务"""
        if not self.running:
            print('串口服务未运行')
            return
        
        print('正在停止虚拟串口服务...')
        
        for port in self.ports.values():
            port.stop()
        
        self.ports.clear()
        self.running = False
        self._remove_status_file()
        
        print('✓ 虚拟串口服务已停止')
    
    def restart_service(self) -> bool:
        """重启串口服务"""
        print('重启虚拟串口服务...')
        self.stop_service()
        time.sleep(1)
        return self.start_service()
    
    def get_service_status(self) -> Dict:
        """获取服务状态"""
        status = {
            'service_running': self.running,
            'ports': {}
        }
        
        for com_port, port in self.ports.items():
            status['ports'][com_port] = port.get_status()
        
        return status
    
    def get_port_info(self) -> Dict[str, str]:
        """获取端口信息（用于其他服务）"""
        port_info = {}
        for com_port, port in self.ports.items():
            if port.is_running():
                port_info[com_port] = port.get_device_path()
        return port_info
    
    def _display_port_info(self):
        """显示端口信息"""
        print('\n端口信息:')
        for com_port, port in self.ports.items():
            device_path = port.get_device_path()
            status = '运行中' if port.is_running() else '已停止'
            print(f'  {com_port}: {device_path} ({status})')
    
    def _save_status(self):
        """保存状态到文件"""
        try:
            status = self.get_service_status()
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
        except Exception as e:
            print(f'保存状态文件失败: {e}')
    
    def _remove_status_file(self):
        """删除状态文件"""
        try:
            if os.path.exists(self.status_file):
                os.remove(self.status_file)
        except Exception as e:
            print(f'删除状态文件失败: {e}')
    
    def cleanup(self):
        """清理资源"""
        if self.running:
            self.stop_service()
    
    def run_daemon(self):
        """以守护进程模式运行"""
        if not self.start_service():
            sys.exit(1)
        
        print('虚拟串口服务正在运行...')
        print('按 Ctrl+C 停止服务')
        
        try:
            while self.running:
                time.sleep(1)
                # 定期更新状态文件
                if self.running:
                    self._save_status()
        except KeyboardInterrupt:
            print('\n用户中断服务')
        finally:
            self.stop_service()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='虚拟串口服务')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status'], 
                       help='服务操作: start(启动), stop(停止), restart(重启), status(状态)')
    
    args = parser.parse_args()
    
    service = SerialPortService()
    
    if args.action == 'start':
        if service.start_service():
            service.run_daemon()
        else:
            sys.exit(1)
    
    elif args.action == 'stop':
        service.stop_service()
    
    elif args.action == 'restart':
        if service.restart_service():
            service.run_daemon()
        else:
            sys.exit(1)
    
    elif args.action == 'status':
        status = service.get_service_status()
        print('虚拟串口服务状态:')
        print(f'服务运行: {"是" if status["service_running"] else "否"}')
        
        if status['ports']:
            print('端口状态:')
            for com_port, port_status in status['ports'].items():
                running = "运行中" if port_status['running'] else "已停止"
                print(f'  {com_port}: {port_status["device_path"]} ({running})')
        else:
            print('没有活动的端口')


if __name__ == "__main__":
    main()
