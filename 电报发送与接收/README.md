# 虚拟串口服务系统

这个项目提供了独立的虚拟串口服务和Java应用程序服务，可以单独启动和停止。

## 文件说明

### 独立服务
- `serial_port_service.py` - 独立的虚拟串口服务
- `java_app_service.py` - 独立的Java应用程序服务
- `service_manager.py` - 服务管理器（统一管理两个服务）
- `test_services.py` - 服务测试脚本

### 原有文件
- `virtual_serial_service.py` - 原始的一体化服务类
- `start_service.py` - 简化的启动脚本
- `test_serial.py` - 串口测试脚本
- `run_jar.py` - 原始的运行脚本
- `sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar` - Java应用程序

## 功能特性

- ✅ **独立服务架构**：虚拟串口和Java应用程序分别独立运行
- ✅ **单独控制**：可以单独启动、停止、重启每个服务
- ✅ **状态监控**：实时监控服务状态和进程信息
- ✅ **自动配置**：自动配置串口参数（9600, 8, N, 1）
- ✅ **服务通信**：Java服务自动获取串口服务的端口信息
- ✅ **优雅清理**：支持优雅的资源清理和错误处理
- ✅ **统一管理**：提供服务管理器统一管理所有服务

## 使用方法

### 方法1：使用服务管理器（推荐）

```bash
# 启动所有服务
python3 service_manager.py start-all

# 停止所有服务
python3 service_manager.py stop-all

# 查看服务状态
python3 service_manager.py status

# 重启所有服务
python3 service_manager.py restart-all
```

### 方法2：单独管理服务

#### 虚拟串口服务
```bash
# 启动串口服务
python3 serial_port_service.py start

# 停止串口服务
python3 serial_port_service.py stop

# 查看串口服务状态
python3 serial_port_service.py status

# 重启串口服务
python3 serial_port_service.py restart
```

#### Java应用程序服务
```bash
# 启动Java服务
python3 java_app_service.py start

# 停止Java服务
python3 java_app_service.py stop

# 查看Java服务状态
python3 java_app_service.py status

# 重启Java服务
python3 java_app_service.py restart
```

### 方法3：测试服务

```bash
# 测试所有服务
python3 test_services.py all

# 只测试串口服务
python3 test_services.py serial

# 只测试Java服务
python3 test_services.py java

# 测试服务管理器
python3 test_services.py manager
```

## 依赖要求

### 系统要求
- macOS 或 Linux
- Python 3.6+
- Java 运行环境

### Python依赖
```bash
# 基础功能（系统自带）
# - subprocess, os, sys, pty, select, threading, time, signal, atexit

# 串口测试功能（可选）
pip install pyserial
```

## 工作原理

1. **虚拟串口创建**：使用Python的`pty`模块创建伪终端对，模拟真实串口设备
2. **串口配置**：自动设置波特率9600、8位数据位、无校验、1位停止位
3. **数据处理**：在后台线程中处理串口数据的收发
4. **Java集成**：将虚拟串口路径传递给Java应用程序
5. **进程监控**：实时显示Java应用程序的输出

## 端口映射

| 虚拟端口 | 实际设备路径 | 说明 |
|---------|-------------|------|
| COM4 | /dev/pts/X | 主要串口，传递给Java应用 |
| COM8 | /dev/pts/Y | 辅助串口 |

*注：实际设备路径由系统动态分配*

## 故障排除

### 1. 权限问题
```bash
# 如果遇到权限问题，可能需要：
sudo python3 start_service.py
```

### 2. 端口占用
如果提示端口被占用，请检查是否有其他程序在使用虚拟串口。

### 3. Java应用启动失败
- 检查JAR文件是否存在
- 确认Java环境是否正确安装
- 查看错误输出信息

### 4. 串口测试失败
```bash
# 安装pyserial
pip install pyserial

# 或者使用conda
conda install pyserial
```

## 高级用法

### 自定义串口参数

可以修改`VirtualSerialPort`类中的串口配置：

```python
# 在virtual_serial_service.py中修改
attrs[4] = attrs[5] = termios.B115200  # 改为115200波特率
```

### 添加数据处理逻辑

在`_data_handler`方法中添加自定义的数据处理逻辑：

```python
def _data_handler(self):
    while self.running:
        # ... 现有代码 ...
        if data:
            # 添加自定义数据处理
            processed_data = self.process_data(data)
            os.write(self.master, processed_data)
```

## 注意事项

1. 虚拟串口在程序结束时会自动清理
2. 建议在虚拟环境中运行
3. 如果Java应用程序异常退出，虚拟串口服务也会停止
4. 支持优雅的中断处理（Ctrl+C）

## 日志输出

服务运行时会显示详细的状态信息：
- ✓ 成功操作
- ✗ 失败操作
- [COM4/COM8] 串口数据
- [JAVA] Java应用输出
- [JAVA ERROR] Java错误输出
