#!/usr/bin/env python3
"""
服务管理器
统一管理虚拟串口服务和Java应用程序服务
"""

import subprocess
import os
import sys
import time
import json
from typing import Dict, Optional


class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.serial_service_script = os.path.join(self.current_dir, 'serial_port_service.py')
        self.java_service_script = os.path.join(self.current_dir, 'java_app_service.py')
        self.jar_file = os.path.join(self.current_dir, 'sf-nfoc-tgsp-2.0.0-SNAPSHOT.jar')
    
    def _run_command(self, command: list, capture_output: bool = True) -> subprocess.CompletedProcess:
        """执行命令"""
        try:
            result = subprocess.run(
                command,
                capture_output=capture_output,
                text=True,
                cwd=self.current_dir
            )
            return result
        except Exception as e:
            print(f'执行命令失败: {e}')
            print(f'命令: {" ".join(command)}')
            raise
    
    def start_serial_service(self) -> bool:
        """启动串口服务"""
        print('启动虚拟串口服务...')
        try:
            # 使用后台进程启动串口服务
            process = subprocess.Popen(
                [sys.executable, self.serial_service_script, 'start'],
                cwd=self.current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一段时间让服务启动
            time.sleep(2)
            
            # 检查服务状态
            status_result = self._run_command([sys.executable, self.serial_service_script, 'status'])
            if status_result.returncode == 0:
                print('✓ 虚拟串口服务启动成功')
                print(status_result.stdout)
                return True
            else:
                print('✗ 虚拟串口服务启动失败')
                print(status_result.stderr)
                return False
                
        except Exception as e:
            print(f'启动串口服务时发生错误: {e}')
            return False
    
    def stop_serial_service(self) -> bool:
        """停止串口服务"""
        print('停止虚拟串口服务...')
        try:
            result = self._run_command([sys.executable, self.serial_service_script, 'stop'])
            if result.returncode == 0:
                print('✓ 虚拟串口服务已停止')
                return True
            else:
                print('✗ 停止虚拟串口服务失败')
                print(result.stderr)
                return False
        except Exception as e:
            print(f'停止串口服务时发生错误: {e}')
            return False
    
    def start_java_service(self) -> bool:
        """启动Java应用服务"""
        print('启动Java应用程序服务...')
        try:
            # 使用后台进程启动Java服务
            process = subprocess.Popen(
                [sys.executable, self.java_service_script, 'start', '--jar', self.jar_file],
                cwd=self.current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一段时间让服务启动
            time.sleep(3)
            
            # 检查服务状态
            status_result = self._run_command([sys.executable, self.java_service_script, 'status'])
            if status_result.returncode == 0:
                print('✓ Java应用程序服务启动成功')
                print(status_result.stdout)
                return True
            else:
                print('✗ Java应用程序服务启动失败')
                print(status_result.stderr)
                return False
                
        except Exception as e:
            print(f'启动Java服务时发生错误: {e}')
            return False
    
    def stop_java_service(self) -> bool:
        """停止Java应用服务"""
        print('停止Java应用程序服务...')
        try:
            result = self._run_command([sys.executable, self.java_service_script, 'stop'])
            if result.returncode == 0:
                print('✓ Java应用程序服务已停止')
                return True
            else:
                print('✗ 停止Java应用程序服务失败')
                print(result.stderr)
                return False
        except Exception as e:
            print(f'停止Java服务时发生错误: {e}')
            return False
    
    def get_services_status(self) -> Dict:
        """获取所有服务状态"""
        status = {
            'serial_service': {'running': False, 'details': {}},
            'java_service': {'running': False, 'details': {}}
        }
        
        # 检查串口服务状态
        try:
            result = self._run_command([sys.executable, self.serial_service_script, 'status'])
            if result.returncode == 0:
                status['serial_service']['running'] = True
                # 尝试解析状态文件
                status_file = '/tmp/serial_port_service_status.json'
                if os.path.exists(status_file):
                    with open(status_file, 'r') as f:
                        status['serial_service']['details'] = json.load(f)
        except:
            pass
        
        # 检查Java服务状态
        try:
            result = self._run_command([sys.executable, self.java_service_script, 'status'])
            if result.returncode == 0:
                status['java_service']['running'] = True
                # 尝试解析状态文件
                status_file = '/tmp/java_app_service_status.json'
                if os.path.exists(status_file):
                    with open(status_file, 'r') as f:
                        status['java_service']['details'] = json.load(f)
        except:
            pass
        
        return status
    
    def start_all_services(self) -> bool:
        """启动所有服务"""
        print('=' * 60)
        print('启动所有服务...')
        print('=' * 60)
        
        # 1. 先启动串口服务
        if not self.start_serial_service():
            print('✗ 串口服务启动失败，停止启动流程')
            return False
        
        # 等待串口服务完全启动
        time.sleep(2)
        
        # 2. 启动Java服务
        if not self.start_java_service():
            print('✗ Java服务启动失败，停止串口服务')
            self.stop_serial_service()
            return False
        
        print('✓ 所有服务启动成功')
        return True
    
    def stop_all_services(self) -> bool:
        """停止所有服务"""
        print('=' * 60)
        print('停止所有服务...')
        print('=' * 60)
        
        success = True
        
        # 1. 先停止Java服务
        if not self.stop_java_service():
            success = False
        
        # 2. 停止串口服务
        if not self.stop_serial_service():
            success = False
        
        if success:
            print('✓ 所有服务已停止')
        else:
            print('⚠ 部分服务停止时出现问题')
        
        return success
    
    def restart_all_services(self) -> bool:
        """重启所有服务"""
        print('重启所有服务...')
        self.stop_all_services()
        time.sleep(2)
        return self.start_all_services()
    
    def show_status(self):
        """显示服务状态"""
        print('=' * 60)
        print('服务状态')
        print('=' * 60)
        
        status = self.get_services_status()
        
        # 显示串口服务状态
        print('虚拟串口服务:')
        if status['serial_service']['running']:
            print('  状态: ✓ 运行中')
            details = status['serial_service']['details']
            if 'ports' in details:
                print('  端口信息:')
                for com_port, port_info in details['ports'].items():
                    running = "运行中" if port_info.get('running') else "已停止"
                    device_path = port_info.get('device_path', 'N/A')
                    print(f'    {com_port}: {device_path} ({running})')
        else:
            print('  状态: ✗ 未运行')
        
        print()
        
        # 显示Java服务状态
        print('Java应用程序服务:')
        if status['java_service']['running']:
            print('  状态: ✓ 运行中')
            details = status['java_service']['details']
            if 'process_id' in details:
                print(f'  进程ID: {details["process_id"]}')
                print(f'  JAR文件: {details.get("jar_path", "N/A")}')
                alive = "是" if details.get("process_alive") else "否"
                print(f'  进程存活: {alive}')
        else:
            print('  状态: ✗ 未运行')


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='服务管理器')
    parser.add_argument('action', choices=[
        'start-serial', 'stop-serial',
        'start-java', 'stop-java', 
        'start-all', 'stop-all', 'restart-all',
        'status'
    ], help='服务操作')
    
    args = parser.parse_args()
    
    manager = ServiceManager()
    
    if args.action == 'start-serial':
        success = manager.start_serial_service()
        sys.exit(0 if success else 1)
    
    elif args.action == 'stop-serial':
        success = manager.stop_serial_service()
        sys.exit(0 if success else 1)
    
    elif args.action == 'start-java':
        success = manager.start_java_service()
        sys.exit(0 if success else 1)
    
    elif args.action == 'stop-java':
        success = manager.stop_java_service()
        sys.exit(0 if success else 1)
    
    elif args.action == 'start-all':
        success = manager.start_all_services()
        sys.exit(0 if success else 1)
    
    elif args.action == 'stop-all':
        success = manager.stop_all_services()
        sys.exit(0 if success else 1)
    
    elif args.action == 'restart-all':
        success = manager.restart_all_services()
        sys.exit(0 if success else 1)
    
    elif args.action == 'status':
        manager.show_status()


if __name__ == "__main__":
    main()
