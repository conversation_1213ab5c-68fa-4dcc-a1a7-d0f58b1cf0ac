import pandas as pd
from pymongo import MongoClient
import datetime
import re
import numpy as np
import pytz


def get_years_list(start_time, end_time):
    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
    end_date = datetime.datetime.strptime(end_time, '%Y-%m-%d')
    start_year = start_date.year
    end_year = end_date.year
    return list(range(start_year, end_year + 1))


# def date_timestamp(date_str):
#     """
#         将日期字符串转换为减去8小时后的毫秒级时间戳。
#
#         参数:
#         date_str (str): 格式为 "YYYY-MM-DD" 的日期字符串。
#
#         返回:
#         int: 减去8小时后对应的毫秒级时间戳。
#         """
#     # 将日期字符串解析为datetime对象
#     date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d")
#     # 减去8小时，通过 timedelta来实现时间的增减操作
#     new_date_obj = date_obj - datetime.timedelta(hours=0)
#     # 获取对应的时间戳（秒），再乘以1000转换为毫秒级时间戳
#     timestamp = int(new_date_obj.timestamp() * 1000)
#     print(timestamp)
#     return timestamp

def date_timestamp(date_str):
    date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    timezone_utc = pytz.utc
    date_obj_utc = timezone_utc.localize(date_obj)
    # 转换为时间戳
    timestamp = int(date_obj_utc.timestamp() * 1000)
    print(timestamp)
    return timestamp


def formate_df(df):
    df = df.copy()  # 显式地创建副本，避免视图问题

    df['org_publish_time'] = df['publish_time']
    df['publish_time'] = df['publish_time'] / 1000
    df['new_time_column'] = pd.to_datetime(df['publish_time'], unit='s', utc=True).dt.strftime('%Y-%m-%d %H:%M:%S')
    df['new_time_column'] = pd.to_datetime(df['new_time_column'])
    # df['new_time_column'] = df['new_time_column'] + pd.Timedelta(hours=8)
    df = df.drop_duplicates(subset='new_time_column', keep='first')
    df = df[(df['new_time_column'].dt.minute == 0) & (df['new_time_column'].dt.second == 0)]
    df.loc[:, 'year'] = df['new_time_column'].dt.year
    df.loc[:, 'month'] = df['new_time_column'].dt.month
    df.loc[:, 'day'] = df['new_time_column'].dt.day
    df.loc[:, 'hour'] = df['new_time_column'].dt.hour
    df.to_csv('list.csv')
    return df


def get_mongodb_data(icao, start_time, end_time):
    year_list = get_years_list(start_time, end_time)
    addresses = "*************"
    port = 27017
    username = "hx_wsp_test"
    database = "hx_wsp_test_db"
    password = "LP1sY$SZsmY"
    mongo_uri = f"mongodb://{username}:{password}@{addresses}:{port}/{database}"
    client = MongoClient(mongo_uri)
    db = client[database]
    data_list = []

    for x in year_list:
        collection = db[f"metar_decode_{x}"]
        results = collection.find({
            "airport_code": f"{icao}",
            "publish_time": {
                "$gte": date_timestamp(start_time),
                "$lte": date_timestamp(end_time)
            }
        }).sort({
            "publish_time": 1
        })
        for result in results:
            data_list.append(result)

    df = pd.DataFrame(data_list)
    df = df.drop(df.index[len(df) - 1])
    return df


# 按照年、月、airport_code计算平均温度和样本数
def get_temperature_1_1(df):
    result = df.groupby(['year', 'month', 'airport_code']).agg({
        'temperature': 'mean',
        'airport_code': 'size'
    }).rename(columns={'airport_code': 'sample_count'}).reset_index()
    result['temperature'] = result['temperature'].round(2)
    return result


# 按照月、airport_code计算平均温度和样本数
def get_temperature_1_2(df):
    result = df.groupby(['month', 'airport_code']).agg({
        'temperature': 'mean',
        'airport_code': 'size'
    }).rename(columns={'airport_code': 'sample_count'}).reset_index()
    result['temperature'] = result['temperature'].round(2)
    return result


# 各年度最值
def get_temperature_2_1(df):
    result = df.groupby(['year', 'month', 'airport_code']).agg({
        'temperature': ['min', 'max']
    }).reset_index()

    result.columns = ['year', 'month', 'airport_code', 'min_temperature', 'max_temperature']
    result['min_temperature'] = result['min_temperature'].round(2)
    result['max_temperature'] = result['max_temperature'].round(2)
    return result


# 跨年度最值
def get_temperature_2_2(df):
    result = df.groupby(['month', 'airport_code']).agg({
        'temperature': ['min', 'max']
    }).reset_index()

    result.columns = ['month', 'airport_code', 'min_temperature', 'max_temperature']
    result['min_temperature'] = result['min_temperature'].round(2)
    result['max_temperature'] = result['max_temperature'].round(2)
    return result


# 跨年度最值的平均直
def get_temperature_2_3(df):
    result = get_temperature_2_2(df)
    new_result = result.groupby(['month', 'airport_code']).agg({
        'min_temperature': 'mean',
        'max_temperature': 'mean'
    }).reset_index()
    new_result['min_temperature'] = new_result['min_temperature'].round(2)
    new_result['max_temperature'] = new_result['max_temperature'].round(2)
    return new_result


# 按照年、月、airport_code计算平均温度和样本数
def get_temperature_3_1(df):
    result = df.groupby(['year', 'month', 'hour', 'airport_code']).agg({
        'temperature': 'mean',
        'airport_code': 'size'
    }).rename(columns={'airport_code': 'sample_count'}).reset_index()
    result['temperature'] = result['temperature'].round(2)
    return result


# 按照年、月、airport_code计算平均温度和样本数
def get_temperature_3_2(df):
    result = df.groupby(['month', 'hour', 'airport_code']).agg({
        'temperature': 'mean',
        'airport_code': 'size'
    }).rename(columns={'airport_code': 'sample_count'}).reset_index()
    result['temperature'] = result['temperature'].round(2)
    return result


# 各年度最值
def get_temperature_4_1(df):
    result = df.groupby(['year', 'month', 'hour', 'airport_code']).agg({
        'temperature': ['min', 'max']
    }).reset_index()

    result.columns = ['year', 'month', 'hour', 'airport_code', 'min_temperature', 'max_temperature']
    result['min_temperature'] = result['min_temperature'].round(2)
    result['max_temperature'] = result['max_temperature'].round(2)
    return result


# 跨年度最值，精确到小时
def get_temperature_4_2(df):
    result = df.groupby(['month', 'hour', 'airport_code']).agg({
        'temperature': ['min', 'max']
    }).reset_index()

    result.columns = ['month', 'hour', 'airport_code', 'min_temperature', 'max_temperature']
    result['min_temperature'] = result['min_temperature'].round(2)
    result['max_temperature'] = result['max_temperature'].round(2)
    return result


# 各年度最值均值
def get_temperature_4_3(df):
    result = get_temperature_4_1(df)
    new_result = result.groupby(['month', 'hour', 'airport_code']).agg({
        'min_temperature': 'mean',
        'max_temperature': 'mean'
    }).reset_index()
    new_result['min_temperature'] = new_result['min_temperature'].round(2)
    new_result['max_temperature'] = new_result['max_temperature'].round(2)
    return new_result


# 统计选定周期内各月超过/低于某温度的次数和频率
def get_temperature_5_1(df):
    df_above_30 = df[df['temperature'] >= 30]
    count_df = df_above_30.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])

    return result_df


def get_wind_1(df):
    df['windSpeed'] = df['wind_info'].apply(lambda x: x['windSpeed'])
    df['gust'] = df['wind_info'].apply(lambda x: x.get('gust', None))
    df_filtered = df[df['gust'] >= 10]
    # df_filtered = df[(df['windSpeed'] >= 10) | (df['gust'] >= 10)]
    count_df = df_filtered.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    return result_df


def get_wind_2(df, runway, magnetic_needle_deviation=0):
    df['filtered_rvr_list'] = df['runway_rvr_list'].apply(
        lambda rvr_list: (
            [item for item in rvr_list if isinstance(item, dict) and item.get('runwayNO') == runway]
            if isinstance(rvr_list, list)
            else None
        )
    )
    # print(df.to_string())
    df['magnetic_needle_deviation'] = magnetic_needle_deviation
    df['windSpeed'] = df['wind_info'].apply(lambda x: x['windSpeed'])
    df['windDirection'] = df['wind_info'].apply(lambda x: x.get('windDirection', None))
    df['gust'] = df['wind_info'].apply(lambda x: x.get('gust', None))
    # df.to_csv('list1.csv')
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    df.dropna(subset=['filtered_rvr_list'], inplace=True)
    df['runwayNO'] = df['filtered_rvr_list'].apply(
        lambda x: x[0]['runwayNO'] if isinstance(x, list) and len(x) > 0 else None)
    df['runway'] = df['runwayNO'].apply(lambda x: int(re.sub('[^0-9]', '', x)) if x is not None else None)
    df.dropna(subset=['runway'], inplace=True)
    df.dropna(subset=['windDirection'], inplace=True)
    df['cross_wind_speed'] = df['windSpeed'] * np.sin(
        df['windDirection'] - (df['runway'] * 10 + df['magnetic_needle_deviation']))
    df['cross_wind_speed'] = df['cross_wind_speed'].round(2)
    df['cross_wind_gust'] = df['gust'] * np.sin(
        df['windDirection'] - (df['runway'] * 10 + df['magnetic_needle_deviation']))
    df['cross_wind_gust'] = df['cross_wind_gust'].round(2)
    # df.to_csv('list1.csv')
    # df_filtered = df[(df['cross_wind_speed'] >= 10) | (df['gust'] >= 10)]
    df_filtered = df[(df['cross_wind_speed'] >= 1)]
    count_df = df_filtered.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    # df.to_csv('list1.csv')
    # print(df_filtered.to_string())
    return result_df


def get_wind_3(df, runway, magnetic_needle_deviation=0):
    df['filtered_rvr_list'] = df['runway_rvr_list'].apply(
        lambda rvr_list: (
            [item for item in rvr_list if isinstance(item, dict) and item.get('runwayNO') == runway]
            if isinstance(rvr_list, list)
            else None
        )
    )

    df['magnetic_needle_deviation'] = magnetic_needle_deviation
    df['windSpeed'] = df['wind_info'].apply(lambda x: x['windSpeed'])
    df['windDirection'] = df['wind_info'].apply(lambda x: x.get('windDirection', None))
    df['gust'] = df['wind_info'].apply(lambda x: x.get('gust', None))
    # df.to_csv('list1.csv')
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    df.dropna(subset=['filtered_rvr_list'], inplace=True)
    df['runwayNO'] = df['filtered_rvr_list'].apply(
        lambda x: x[0]['runwayNO'] if isinstance(x, list) and len(x) > 0 else None)
    df['runway'] = df['runwayNO'].apply(lambda x: int(re.sub('[^0-9]', '', x)) if x is not None else None)
    df.dropna(subset=['runway'], inplace=True)
    df.dropna(subset=['windDirection'], inplace=True)
    df['tail_wind_speed'] = -1 * df['windSpeed'] * np.cos(
        df['windDirection'] - (df['runway'] * 10 + df['magnetic_needle_deviation']))
    df['tail_wind_speed'] = df['tail_wind_speed'].round(2)
    df['tail_wind_gust'] = -1 * df['gust'] * np.cos(
        df['windDirection'] - (df['runway'] * 10 + df['magnetic_needle_deviation']))
    df['tail_wind_gust'] = df['tail_wind_gust'].round(2)
    df['head_wind_speed'] = -1 * df['tail_wind_speed']
    df['head_wind_gust'] = -1 * df['tail_wind_gust']

    df_filtered = df[(df['tail_wind_speed'] >= 1) | (df['head_wind_speed'] >= -5)]
    # df_filtered = df[(df['tail_wind_speed'] >= 1)]
    # df_filtered = df[(df['tail_wind_gust'] >= -100)]
    # df_filtered = df[(df['head_wind_speed'] >= 1)]
    # df_filtered = df[(df['head_wind_gust'] >= 1)]
    count_df = df_filtered.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    # print(df_filtered.to_string())
    return result_df


# 强风出现的次数和频率
def get_wind_4(df):
    df['windSpeed'] = df['wind_info'].apply(lambda x: x['windSpeed'])
    df['gust'] = df['wind_info'].apply(lambda x: x.get('gust', None))
    df['windDirection'] = df['wind_info'].apply(lambda x: x.get('windDirection', None))
    total_count_df = df.groupby(['windDirection', 'hour', 'airport_code']).size().reset_index(name='total_count')

    df.to_csv('list2.csv')
    # df_filtered = df[(df['windSpeed'] >= 10) & (df['gust'] >= 1)]
    # df_filtered = df[(df['windSpeed'] >= 12) | (df['gust'] >= 1)]
    df_filtered = df[df['windSpeed'] >= 5]

    count_df = df_filtered.groupby(['windDirection', 'hour', 'airport_code']).size().reset_index(name='count')
    result_df = pd.merge(count_df, total_count_df, on=['windDirection', 'hour', 'airport_code'])
    # result_df.to_csv('list1.csv')
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    return result_df


# 按照年、月、airport_code计算平均温度和样本数
def get_altimeter_1(df):
    result = df.groupby(['month', 'hour', 'airport_code']).agg({
        'altimeter': 'mean',
        'airport_code': 'size'
    }).rename(columns={'airport_code': 'sample_count'}).reset_index()
    result['altimeter'] = result['altimeter'].round(2)
    return result


# 选定周期超过某气压的次数和频率

def get_altimeter_2(df):
    df_above = df[df['altimeter'] >= 1025]
    count_df = df_above.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    return result_df


def safe_min(iterable, default=None):
    try:
        return min(iterable)
    except ValueError:
        return default


priority_order = ['VV', 'OVC', 'BKN', 'SCT', 'FEW']


# def get_covering_height(cloud_list, cover):
#     # 检查 cloud_list 是否为列表类型
#     if not isinstance(cloud_list, list):
#         return None  # 如果不是列表，返回 None
#     for priority in priority_order[priority_order.index(cover):]:
#         # 收集所有匹配的 cloudHeight
#         heights = [cloud['cloudHeight'] for cloud in cloud_list if cloud['cloudQuantity'] == priority]
#         if heights:  # 如果有匹配的高度
#             return min(heights)  # 返回最小的 cloudHeight
#     return None  # 如果没有匹配的，返回 None


def get_covering_height(cloud_list, cover):
    # 检查 cloud_list 是否为列表类型
    if not isinstance(cloud_list, list):
        return None  # 如果不是列表，返回 None

    # 获取 cover 及其更低优先级的 cloudQuantity
    cover_index = priority_order.index(cover)
    valid_quantities = priority_order[cover_index:]  # 从 cover 开始到最低优先级

    # 收集所有匹配的 cloudHeight
    heights = [cloud['cloudHeight'] for cloud in cloud_list if cloud['cloudQuantity'] in valid_quantities]

    if heights:  # 如果有匹配的高度
        return min(heights)  # 返回最小的 cloudHeight
    return None  # 如果没有匹配的，返回 None


def get_vis(df):
    df['vis'] = df['vis_info'].apply(lambda x: x.get('vis', None))
    df['vis'] = df['vis'].astype(int)
    df['rvr'] = df['runway_rvr_list'].apply(lambda x: safe_min(
        (item.get('rvrMin') for item in x if
         isinstance(x, list) and isinstance(item, dict) and item.get('rvrMin') is not None)) if isinstance(x,
                                                                                                           list) else None)
    df['Covering'] = df['cloud_list'].apply(lambda x: get_covering_height(x, 'SCT'))
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    # df.to_csv('list3.csv')
    # df_filtered = df[df['vis'] <= 800]
    # df_filtered = df[df['rvr'] <= 176]
    df_filtered = df[df['Covering'] <= 200]

    count_df = df_filtered.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    # print(df_filtered.to_string())
    return result_df


# 定义一个函数来处理 weather_list
def process_weather_list(weather_list):
    if not isinstance(weather_list, list):  # 检查是否为列表
        return []  # 如果不是列表，返回空列表
    weather = []
    for item in weather_list:
        if 'intensity' in item and 'description' in item and 'phenomenon' in item:
            if item['intensity'] == '-' or item['intensity'] == '+':
                weather.append(item['intensity'] + item['description'] + item['phenomenon'])
        elif 'intensity' in item and 'phenomenon' in item:
            if item['intensity'] == '-' or item['intensity'] == '+':
                weather.append(item['intensity'] + item['phenomenon'])
        elif 'description' in item and 'phenomenon' in item:  # 如果有 description 和 phenomenon
            weather.append(item['description'] + item['phenomenon'])
        elif 'phenomenon' in item:  # 如果只有 phenomenon
            weather.append(item['phenomenon'])
    return weather


def filter_weather(weather_list, strength_list):
    return any(item in strength_list for item in weather_list)


def get_weather(df, strength):
    df['vis'] = df['vis_info'].apply(lambda x: x.get('vis', None))
    df['vis'] = df['vis'].astype(int)
    df['weather'] = df['weather_list'].apply(process_weather_list)
    df.to_csv('list4.csv')
    total_count_df = df.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='total_count')
    if strength.startswith('-'):
        # strength_list = [strength]
        strength_list = [strength, strength[1:], '+' + strength[1:]]
    elif strength.startswith('+'):
        # strength_list = [strength, strength[1:], '-' + strength[1:]]
        strength_list = [strength]
    else:
        strength_list = [strength, '+' + strength]
    print(strength_list)
    # df_filtered = df[df['weather'].apply(filter_weather, strength_list=strength_list)]
    df_filtered = df[(df['weather'].apply(filter_weather, strength_list=strength_list)) & (df['vis'] <= 800)]
    count_df = df_filtered.groupby(['month', 'hour', 'airport_code']).size().reset_index(name='count')
    result_df = pd.merge(count_df, total_count_df, on=['month', 'hour', 'airport_code'])
    result_df['frequency'] = result_df['count'] / result_df['total_count']
    result_df['frequency'] = result_df['frequency'].round(2)
    result_df = result_df.drop(columns=['total_count'])
    return result_df


if __name__ == '__main__':
    start_time = '2018-06-01'
    end_time = '2019-08-01'
    # start_time = '2019-08-01'
    # end_time = '2019-09-01'

    tmp_df = get_mongodb_data("ZUCK", start_time, end_time)
    df = formate_df(tmp_df)
    # result = get_temperature_5_1(df)
    # result = get_temperature_5_1(df)
    # result = get_wind_1(df)
    # result = get_wind_2(df, '03', 130)
    # result = get_wind_3(df, '03', 130)
    # result = get_wind_4(df)
    # result = get_altimeter_1(df)
    # result = get_altimeter_2(df)
    # result = get_vis(df)
    result = get_weather(df, 'FG')
    print(result.to_string())
    # print(df.to_string())
