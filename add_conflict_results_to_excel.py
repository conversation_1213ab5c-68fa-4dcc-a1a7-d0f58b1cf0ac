#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将区间冲突分析结果添加到Excel文件的第三列
"""

import pandas as pd
import re
from typing import List, Tuple, Dict, Any
import openpyxl
from openpyxl.styles import PatternFill, Font

class ExcelConflictAnalyzer:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.data = None
        self.workbook = None
        self.worksheet = None
        
    def load_data(self):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(self.excel_path)
            self.workbook = openpyxl.load_workbook(self.excel_path)
            self.worksheet = self.workbook.active
            print(f"成功加载数据，共 {len(self.data)} 行")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def parse_green_interval(self, interval_str: str) -> List[Tuple[float, float, str]]:
        """解析green_interval列的区间表达式"""
        if pd.isna(interval_str) or not isinstance(interval_str, str):
            return []
        
        intervals = []
        interval_str = str(interval_str).strip()
        
        # 跳过包含变量的表达式
        if re.search(r'[A-Za-z]', interval_str):
            return []
        
        # 处理各种格式
        if interval_str.startswith('≤') or interval_str.startswith('<='):
            match = re.match(r'[≤<=](\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((float('-inf'), val, '<='))
        elif interval_str.startswith('≥') or interval_str.startswith('>='):
            match = re.match(r'[≥>=](\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((val, float('inf'), '>='))
        elif interval_str.startswith('<'):
            match = re.match(r'<(\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((float('-inf'), val, '<'))
        elif interval_str.startswith('>'):
            match = re.match(r'>(\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((val, float('inf'), '>'))
        elif '[' in interval_str and ']' in interval_str:
            match = re.match(r'\[([^,\-\]]+)[\-,]([^\]]+)\]', interval_str)
            if match:
                try:
                    min_val = float(match.group(1))
                    max_val = float(match.group(2))
                    intervals.append((min_val, max_val, '[]'))
                except ValueError:
                    pass
        
        return intervals
    
    def parse_over_interval(self, interval_str: str) -> List[Tuple[float, float, str]]:
        """解析overInterval列的复杂区间表达式"""
        if pd.isna(interval_str) or not isinstance(interval_str, str):
            return []
        
        intervals = []
        interval_str = str(interval_str).strip()
        
        # 提取所有数值比较条件
        patterns = [
            r'测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*([<>]=?)\s*测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, interval_str)
            for match in matches:
                if len(match) == 2:
                    operator, value = match
                    val = float(value)
                    if operator == '<':
                        intervals.append((float('-inf'), val, '<'))
                    elif operator == '<=':
                        intervals.append((float('-inf'), val, '<='))
                    elif operator == '>':
                        intervals.append((val, float('inf'), '>'))
                    elif operator == '>=':
                        intervals.append((val, float('inf'), '>='))
                elif len(match) == 4:
                    min_val, min_op, max_op, max_val = match
                    min_val = float(min_val)
                    max_val = float(max_val)
                    intervals.append((min_val, max_val, f'{min_op}..{max_op}'))
        
        return intervals
    
    def check_conflicts(self, green_intervals: List[Tuple[float, float, str]], 
                       over_intervals: List[Tuple[float, float, str]]) -> Tuple[bool, str]:
        """检查两个区间列表之间是否有冲突，返回冲突状态和详细描述"""
        if not green_intervals and not over_intervals:
            return False, "无法解析区间"
        
        if not green_intervals:
            return False, "绿色区间包含变量或无法解析"
        
        if not over_intervals:
            return False, "超标区间无法解析"
        
        conflicts = []
        for g_min, g_max, g_op in green_intervals:
            for o_min, o_max, o_op in over_intervals:
                overlap_start = max(g_min, o_min)
                overlap_end = min(g_max, o_max)
                if overlap_start < overlap_end:
                    if g_min == float('-inf') and g_max == o_min and g_op == '<=' and '>' in o_op:
                        conflicts.append(f"边界值{g_max}冲突")
                    elif g_min == o_max and g_op == '>=' and '<' in o_op:
                        conflicts.append(f"边界值{g_min}冲突")
                    else:
                        if overlap_start == float('-inf'):
                            start_str = "-∞"
                        else:
                            start_str = str(overlap_start)
                        if overlap_end == float('inf'):
                            end_str = "+∞"
                        else:
                            end_str = str(overlap_end)
                        conflicts.append(f"区间[{start_str},{end_str}]重叠")
        
        if conflicts:
            return True, "; ".join(list(set(conflicts)))
        else:
            return False, "无冲突"
    
    def analyze_and_update_excel(self):
        """分析冲突并更新Excel文件"""
        if self.data is None or self.worksheet is None:
            print("请先加载数据")
            return
        
        print("开始分析并更新Excel文件...")
        
        # 添加表头
        self.worksheet.cell(row=1, column=11, value="冲突分析结果")
        self.worksheet.cell(row=1, column=11).font = Font(bold=True)
        
        # 定义样式
        conflict_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")  # 红色背景
        no_conflict_fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")  # 绿色背景
        warning_fill = PatternFill(start_color="FFFFCC", end_color="FFFFCC", fill_type="solid")  # 黄色背景
        
        conflict_count = 0
        no_conflict_count = 0
        warning_count = 0
        
        for index, row in self.data.iterrows():
            excel_row = index + 2  # Excel行号从2开始（第1行是表头）
            
            green_col = 'green_interval'
            over_col = 'overInterval'
            
            if green_col not in self.data.columns or over_col not in self.data.columns:
                continue
            
            green_val = row[green_col]
            over_val = row[over_col]
            
            # 解析区间
            green_intervals = self.parse_green_interval(green_val)
            over_intervals = self.parse_over_interval(over_val)
            
            # 检查冲突
            has_conflict, conflict_desc = self.check_conflicts(green_intervals, over_intervals)
            
            # 写入结果到Excel
            cell = self.worksheet.cell(row=excel_row, column=11, value=conflict_desc)
            
            # 设置样式
            if has_conflict:
                cell.fill = conflict_fill
                conflict_count += 1
            elif "无法解析" in conflict_desc or "包含变量" in conflict_desc:
                cell.fill = warning_fill
                warning_count += 1
            else:
                cell.fill = no_conflict_fill
                no_conflict_count += 1
        
        # 调整列宽
        self.worksheet.column_dimensions['K'].width = 30
        
        print(f"分析完成:")
        print(f"  - 有冲突: {conflict_count} 行")
        print(f"  - 无冲突: {no_conflict_count} 行")
        print(f"  - 警告(无法解析): {warning_count} 行")
        print(f"  - 总计: {conflict_count + no_conflict_count + warning_count} 行")
        
        return conflict_count, no_conflict_count, warning_count
    
    def save_excel(self, output_path: str = None):
        """保存Excel文件"""
        if self.workbook is None:
            print("没有可保存的工作簿")
            return False
        
        try:
            if output_path is None:
                output_path = self.excel_path.replace('.xlsx', '_with_conflict_analysis.xlsx')
            
            self.workbook.save(output_path)
            print(f"Excel文件已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            return False

def main():
    excel_path = "./交集判定/111.xlsx"
    
    analyzer = ExcelConflictAnalyzer(excel_path)
    
    if analyzer.load_data():
        conflict_count, no_conflict_count, warning_count = analyzer.analyze_and_update_excel()
        
        if analyzer.save_excel():
            print("\n✅ 成功完成冲突分析并更新Excel文件!")
            print(f"📊 统计结果:")
            print(f"   🔴 冲突行数: {conflict_count}")
            print(f"   🟢 无冲突行数: {no_conflict_count}")
            print(f"   🟡 警告行数: {warning_count}")
            print(f"   📈 冲突比例: {conflict_count/(conflict_count+no_conflict_count+warning_count)*100:.1f}%")
        else:
            print("❌ 保存文件失败")
    else:
        print("❌ 无法加载数据文件")

if __name__ == "__main__":
    main()
