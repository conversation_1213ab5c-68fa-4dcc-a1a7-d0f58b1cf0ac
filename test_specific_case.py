#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定的区间冲突案例
"""

def analyze_case():
    """分析 [-20,20] 与 测量值<20.00;测量值>-20.00 的冲突"""
    
    print("分析案例: [-20,20] vs 测量值<20.00 扣2.00分;测量值>-20.00 扣2.00分")
    print("=" * 70)
    
    # 绿色区间：-20 ≤ 测量值 ≤ 20
    print("绿色区间: [-20,20] 表示 -20 ≤ 测量值 ≤ 20 (这是好的范围)")
    
    # 超标区间：测量值 < 20 OR 测量值 > -20
    print("超标区间:")
    print("  - 测量值 < 20.00 扣2.00分")
    print("  - 测量值 > -20.00 扣2.00分")
    
    print("\n冲突分析:")
    print("让我们测试几个具体的测量值:")
    
    test_values = [-25, -20, -10, 0, 10, 20, 25]
    
    for value in test_values:
        print(f"\n测量值 = {value}:")
        
        # 检查是否在绿色区间
        in_green = -20 <= value <= 20
        print(f"  在绿色区间内: {in_green}")
        
        # 检查是否触发超标条件
        penalty1 = value < 20  # 测量值<20.00 扣2.00分
        penalty2 = value > -20  # 测量值>-20.00 扣2.00分
        
        print(f"  触发'测量值<20.00': {penalty1}")
        print(f"  触发'测量值>-20.00': {penalty2}")
        
        total_penalty = 0
        if penalty1:
            total_penalty += 2
        if penalty2:
            total_penalty += 2
            
        print(f"  总扣分: {total_penalty}分")
        
        # 冲突判断
        if in_green and total_penalty > 0:
            print(f"  ⚠️ 冲突! 在绿色区间内但被扣{total_penalty}分")
        elif in_green:
            print(f"  ✅ 无冲突: 在绿色区间内且不扣分")
        else:
            print(f"  ℹ️ 不在绿色区间内")
    
    print("\n结论:")
    print("这个配置确实存在严重冲突!")
    print("- 除了边界值-20和20之外，绿色区间内的所有值都会被扣分")
    print("- 测量值=0时，在绿色区间内但会被扣4分(2+2)")
    print("- 这种配置在逻辑上是矛盾的")

if __name__ == "__main__":
    analyze_case()
