<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<data>
		Vte9kXQHWaVNCuck/YBqEktnypg=
		</data>
		<key>Resources/_tcl_data/clock.tcl</key>
		<data>
		DgyNC8fUcr8DImgF8hH9eswKRZM=
		</data>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<data>
		3YMzPcHIOL65EC8GOXHMwgzE/YA=
		</data>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<data>
		njXr89U4DjS5L+J0QST5MkuQHdM=
		</data>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<data>
		QIEYQ6+V587xeVcQF9f6adFErXg=
		</data>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<data>
		+iJxAw25AF1x+q1gtEdnlV1UMt0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<data>
		YEOW2B/S2Q9XNP5sPyg/jxmqu2Q=
		</data>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<data>
		LiEwDgvIqEfQQjZxsI08ZXYe4XI=
		</data>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<data>
		j/mlJqVF0pOCmmeaLs3TOqb5qQ4=
		</data>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<data>
		SEcFppWWydgT6jYWJcOkXGuzEig=
		</data>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<data>
		Zg2+RYOSPL3/9iYbH630NJZYV5w=
		</data>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<data>
		OP7jn0ThTDohmXj4tuTaVIFSz9Y=
		</data>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<data>
		zBA8U7O6F2RxRYfq69ks0bx1GU0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<data>
		YpTkLtItdWef8UZP9B1D2zsYJMI=
		</data>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<data>
		P3JSRcZgUNOdkjS6rOnQR6OEKUQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<data>
		yV6k7T++8BPYEMC/sZOxX6it57g=
		</data>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<data>
		9DAaE0ChYOHygrX5i/n6y/qTsRk=
		</data>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<data>
		Xq03eI0STU7knsS4qhz2qqnChJw=
		</data>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<data>
		kEs1fDBgPfvPihCgVNk5lgixMd8=
		</data>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<data>
		+L1L9tlfZyy2G47KtYCnZb69rqU=
		</data>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<data>
		iISZ2d/fdcYMJ3A4akUA81dTznA=
		</data>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<data>
		xrHpMg7vRvyaI0N8JV5AheopgNs=
		</data>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<data>
		U1KXcupjIreUnbc+667ZHlpbo9o=
		</data>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<data>
		7yc2cdRoFfIplupjLSLMJ+uMpEs=
		</data>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<data>
		f27Ym9DUFcZNC4oDfwikf+rdFMQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<data>
		4MpAC64PZu6+Tf4UfFoY3TsAt4w=
		</data>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<data>
		DrQP7rijglMLaXSOCL9RMSQjJAM=
		</data>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<data>
		QxJwk50+R5v5uaZj2eZ/zrp5QW8=
		</data>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<data>
		6BSfMzsYCdzN5Rz4tjMhA93n/DA=
		</data>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<data>
		2gV+H5P3VSGlHMcl1HEw9B5QnnA=
		</data>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<data>
		0LPesO5lOc5fKKUUZL+7OqA/KOU=
		</data>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<data>
		Ox0HsCrn47QHhIceF/NjMoNCaOY=
		</data>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<data>
		kEqLeEbTRSFjTIwJAT27HTGvR8o=
		</data>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<data>
		ZgX8yyNaCPkDK7RSMbGmMxdkZks=
		</data>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<data>
		aiHVe0SghWq83mGxwWy5P05MPXQ=
		</data>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<data>
		RlUJxybEloCwI3JQGvelLwmrfVU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<data>
		+PyjmFAJos3Tl8s7rjCK8FsNfKw=
		</data>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<data>
		ic2k/mUVycA1UeThly/UeK86QZw=
		</data>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<data>
		XI5pGuPBMwiCD0z2kgbXZc/VCUs=
		</data>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<data>
		ScZjrCbB/k8P0UKMnvJwWK7mypU=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<data>
		26CcZw8k1HuV0S1LuXBDkbgd2po=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<data>
		OeILQc+osmk3evoG+cTWbt2Uass=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<data>
		lMXzklY2artozWfjAl8Xf1Ts050=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<data>
		nW8HdZilqG5utqTuwUgQv1JfvYk=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<data>
		pxtjgOo9I9wN4R07jOqGpMgGPUc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<data>
		zseHxN54+du5e5xEBwzywSokaPc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<data>
		AcVG7nwQsWSnTWY/pvwrx9MhIVU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<data>
		1GKTSgdO4T8sgQRj/QYQhJU/d7w=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<data>
		YsJTqnqGjOMliYaPqzczZUJFepY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<data>
		SfekKIn7ig14yABnveGAlNvpVu4=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<data>
		UP2mxwoTPLZM84qksvMTtU0v2VU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<data>
		/2LrVxD94RB0qH2u6SKbz39m16A=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<data>
		sL6+3sU/+4lNn7DVfyWrKkWbbdU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<data>
		zBwubDWwBcF+t7Gj10SYOoanVzY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<data>
		+fZLtgFAaOLAc3GGxpS4EB3ZV14=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<data>
		uBBCY1TYV3GMyEHUJNoHDvufFE8=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<data>
		eswpSNXomXUMKsbIFM3hfUIRK8k=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<data>
		TqXsUzFUHt5lqc9gH1QY/Uts/Lw=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<data>
		9YXHClWJ3jlVjawBZ0P/heDF8DI=
		</data>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<data>
		eutwjInBePtNVhHCReoafPZq3zo=
		</data>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<data>
		qSKsrODBpKfdyS/l3XoRbTCjaGs=
		</data>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<data>
		FQE0eHYEY6C841d7TWRuzbB2MrU=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<data>
		NmwTfALgabGpP7tdZLkSDqbprR8=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<data>
		2p5n9k7E9qdMYMtlDVoSxEMNz/c=
		</data>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<data>
		wv+kJ0V7STHlqSMm8lHNPWcQWbA=
		</data>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<data>
		FrUdAXABaIoyy3sV3m56SfKLdv0=
		</data>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<data>
		Y/ROgYKEOE3gerDYsM1vfr/gmrk=
		</data>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<data>
		PeF7KlhmJyYC+46cVJMKTNHzsGw=
		</data>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<data>
		pA5tuX1tsok6Bysiddwi4qTWBzc=
		</data>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<data>
		nCrVPWn1B3hToF8JMzMLXW+IpRw=
		</data>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<data>
		xDQlfXap/fgczNjMFCQsjjlA/Yk=
		</data>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<data>
		uhh8UvrpeS2lv/vqp4H9TgcW4PY=
		</data>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<data>
		5Njqag5wu3kzBMoh6xM3p6LCajE=
		</data>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<data>
		4le9Fu80/cKdW2yYWhtFgBk3NUw=
		</data>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<data>
		Yz0ZC14oHPwBePbBHdchxqJm9kM=
		</data>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<data>
		OC40gkrYt57wyY/VFnUGSf2Usgo=
		</data>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<data>
		pIeUbLLv11/XSFA9deSVcgtT5bw=
		</data>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<data>
		LVaWWyQSXZmdECDHw0e4E6lyZHw=
		</data>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<data>
		8MAUttZ/wNwdG7xfBS8Mixxj2L8=
		</data>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<data>
		RvsXtOCFAQIomg0D9PlWmzqkmU0=
		</data>
		<key>Resources/_tcl_data/history.tcl</key>
		<data>
		Lia3l32QDqp9SQjVETgD3280/Fk=
		</data>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<data>
		joZZvvBlr5QwUJu91ftM/g7xQVM=
		</data>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<data>
		YAGligcB3/Il4lEKSq7mSJpTdlc=
		</data>
		<key>Resources/_tcl_data/init.tcl</key>
		<data>
		hWH6x7SgNmWfGo9gtIdYbIHsis8=
		</data>
		<key>Resources/_tcl_data/msgs/af.msg</key>
		<data>
		IImzlI8R74zkvT1XFncVreZYdek=
		</data>
		<key>Resources/_tcl_data/msgs/af_za.msg</key>
		<data>
		Z3OU34HNuvPT5zX0l3FTu1yBsaY=
		</data>
		<key>Resources/_tcl_data/msgs/ar.msg</key>
		<data>
		kINLy9qbkxe5J4bsieINzx8tvSI=
		</data>
		<key>Resources/_tcl_data/msgs/ar_in.msg</key>
		<data>
		dNErTLzfY/3wDlidimBKXFLDk+8=
		</data>
		<key>Resources/_tcl_data/msgs/ar_jo.msg</key>
		<data>
		cJcXux9ipx6U1hBWpwZgxqA7SK4=
		</data>
		<key>Resources/_tcl_data/msgs/ar_lb.msg</key>
		<data>
		rvOKq3NuVDQpXHLBTzgDOq/m7xU=
		</data>
		<key>Resources/_tcl_data/msgs/ar_sy.msg</key>
		<data>
		xrg8AvXUsUBk2Tev2MapK6munvs=
		</data>
		<key>Resources/_tcl_data/msgs/be.msg</key>
		<data>
		dNYj2rYjjQXBjd5X/JVthJdPwtQ=
		</data>
		<key>Resources/_tcl_data/msgs/bg.msg</key>
		<data>
		7FVXoWoCk6v0qo5f1QlAtgqKNqY=
		</data>
		<key>Resources/_tcl_data/msgs/bn.msg</key>
		<data>
		gNsjNoepMUYAMXrTnAFGbGQvPEw=
		</data>
		<key>Resources/_tcl_data/msgs/bn_in.msg</key>
		<data>
		IpYHOujMQheA6KO81YMS1vsvW/w=
		</data>
		<key>Resources/_tcl_data/msgs/ca.msg</key>
		<data>
		jS1T2iCLtnCjNcdS38S0/0UJp5k=
		</data>
		<key>Resources/_tcl_data/msgs/cs.msg</key>
		<data>
		ylxHp2zUUG2OEa7OHqC0plcXYBk=
		</data>
		<key>Resources/_tcl_data/msgs/da.msg</key>
		<data>
		sXHRVUJE0qbtjeF6yAAKoJ0vrek=
		</data>
		<key>Resources/_tcl_data/msgs/de.msg</key>
		<data>
		ar9Rn25IRebxPyctYo3pfy0s1IE=
		</data>
		<key>Resources/_tcl_data/msgs/de_at.msg</key>
		<data>
		t3FK83K0ZioMFd28D4DRJJyx7r0=
		</data>
		<key>Resources/_tcl_data/msgs/de_be.msg</key>
		<data>
		3lGdOobc8ej0aUkJZ6/jULrq/gE=
		</data>
		<key>Resources/_tcl_data/msgs/el.msg</key>
		<data>
		T7nbbnhOHSjmMrVe0x+7tJl79XU=
		</data>
		<key>Resources/_tcl_data/msgs/en_au.msg</key>
		<data>
		Uoku3fp03UyAQPnN0ZqVNr/3K24=
		</data>
		<key>Resources/_tcl_data/msgs/en_be.msg</key>
		<data>
		t6a0v7ZTPMM6Cg9QN+VaVZWMTfw=
		</data>
		<key>Resources/_tcl_data/msgs/en_bw.msg</key>
		<data>
		7uw6WjeA26cXAUnHeRgHSOuGG4Y=
		</data>
		<key>Resources/_tcl_data/msgs/en_ca.msg</key>
		<data>
		v9utXAoyOjfV+Rw37Imbkj2lsPU=
		</data>
		<key>Resources/_tcl_data/msgs/en_gb.msg</key>
		<data>
		3tvbLJrKkyw3PDFftsVpHb7es0Y=
		</data>
		<key>Resources/_tcl_data/msgs/en_hk.msg</key>
		<data>
		ZxIsqo7Kgp7AdZoBR8aFGm6R6Gc=
		</data>
		<key>Resources/_tcl_data/msgs/en_ie.msg</key>
		<data>
		+odlD4QOaRZD82149zJuklaD0Kg=
		</data>
		<key>Resources/_tcl_data/msgs/en_in.msg</key>
		<data>
		cDYlk6KwTPllIT8xixDpLigPM40=
		</data>
		<key>Resources/_tcl_data/msgs/en_nz.msg</key>
		<data>
		M4ayWZx8FwoD5O7WjDnqx63QFwg=
		</data>
		<key>Resources/_tcl_data/msgs/en_ph.msg</key>
		<data>
		GXGs+qV1PSkUV33Mnr30PPicHQA=
		</data>
		<key>Resources/_tcl_data/msgs/en_sg.msg</key>
		<data>
		bJBmOWwQcEnYYc0KnJjeh1N4JXE=
		</data>
		<key>Resources/_tcl_data/msgs/en_za.msg</key>
		<data>
		pbhTo52UTbm7GkwLnVWv3vBRVUg=
		</data>
		<key>Resources/_tcl_data/msgs/en_zw.msg</key>
		<data>
		7/grKHQfoW0t/JO1Qh+FbW+QJQk=
		</data>
		<key>Resources/_tcl_data/msgs/eo.msg</key>
		<data>
		oUuaqZnAu9myHmorRKk01oWJdDA=
		</data>
		<key>Resources/_tcl_data/msgs/es.msg</key>
		<data>
		iy0L4b41TWOewzc/4goPJV4xLvY=
		</data>
		<key>Resources/_tcl_data/msgs/es_ar.msg</key>
		<data>
		PFU1NiQaXS6Vo7qQJKq0a7h/utk=
		</data>
		<key>Resources/_tcl_data/msgs/es_bo.msg</key>
		<data>
		hT/8u5oiU7fcK4LCv8OxMlAPep0=
		</data>
		<key>Resources/_tcl_data/msgs/es_cl.msg</key>
		<data>
		j+HRdpbJEM9ZRnWYIz1VJov+DZQ=
		</data>
		<key>Resources/_tcl_data/msgs/es_co.msg</key>
		<data>
		O6OMsDJYyoNON9u04xSdTNqbNTs=
		</data>
		<key>Resources/_tcl_data/msgs/es_cr.msg</key>
		<data>
		RWyQwJwqiRnclI6GFw9SMGLxNds=
		</data>
		<key>Resources/_tcl_data/msgs/es_do.msg</key>
		<data>
		GA6ThYTwpXrAw/heZXS8SCkdgg4=
		</data>
		<key>Resources/_tcl_data/msgs/es_ec.msg</key>
		<data>
		M2VIyNNhscqovfaY4Uiojkf7J6Y=
		</data>
		<key>Resources/_tcl_data/msgs/es_gt.msg</key>
		<data>
		SZ9p5mGztXRyJ7Md5FOcrzVcyqw=
		</data>
		<key>Resources/_tcl_data/msgs/es_hn.msg</key>
		<data>
		Y5qUJ5RTsAKJlUSP0uIhwb3iPO4=
		</data>
		<key>Resources/_tcl_data/msgs/es_mx.msg</key>
		<data>
		DuWjYnfqTnofTG0dnuMtkJGNolw=
		</data>
		<key>Resources/_tcl_data/msgs/es_ni.msg</key>
		<data>
		UknjFhGmcOru8QWrStLl8Us1XK4=
		</data>
		<key>Resources/_tcl_data/msgs/es_pa.msg</key>
		<data>
		fxTUb2bYqUpJNwLc3npQwdcXdLI=
		</data>
		<key>Resources/_tcl_data/msgs/es_pe.msg</key>
		<data>
		dcKDIa/tPZzaPr8/0FnN6ll7sTo=
		</data>
		<key>Resources/_tcl_data/msgs/es_pr.msg</key>
		<data>
		JNiwlt2PHPoQHW82YG0APU/Me00=
		</data>
		<key>Resources/_tcl_data/msgs/es_py.msg</key>
		<data>
		YZkObz45m4cGDlIqvN53qDIBkWc=
		</data>
		<key>Resources/_tcl_data/msgs/es_sv.msg</key>
		<data>
		mr7CLoLBY4ucjhl3YMZuNwKZu5M=
		</data>
		<key>Resources/_tcl_data/msgs/es_uy.msg</key>
		<data>
		04UyyoToD+cMaRCHEeP5p9/VIw8=
		</data>
		<key>Resources/_tcl_data/msgs/es_ve.msg</key>
		<data>
		fCrygMkLAQSrSbKlJ2AjdCVCdM4=
		</data>
		<key>Resources/_tcl_data/msgs/et.msg</key>
		<data>
		vuOeRfo6drYxtMLQ+Tf/YEHgkzI=
		</data>
		<key>Resources/_tcl_data/msgs/eu.msg</key>
		<data>
		/frCLMCDmyl5kAGDh2XrSiMv0nk=
		</data>
		<key>Resources/_tcl_data/msgs/eu_es.msg</key>
		<data>
		NQP8uUkCYbqUfonVSUmYzrsVciM=
		</data>
		<key>Resources/_tcl_data/msgs/fa.msg</key>
		<data>
		yyEHQPViCOjmIaRdVF1978rovK8=
		</data>
		<key>Resources/_tcl_data/msgs/fa_in.msg</key>
		<data>
		XkF45ygoB0dr0Nbh8uMg5C+g3nc=
		</data>
		<key>Resources/_tcl_data/msgs/fa_ir.msg</key>
		<data>
		nWQDjAAlOn7tpJIbnF40aQ4YUGE=
		</data>
		<key>Resources/_tcl_data/msgs/fi.msg</key>
		<data>
		sXNwDBdjNr0bEjwqBVpoX3O2DAc=
		</data>
		<key>Resources/_tcl_data/msgs/fo.msg</key>
		<data>
		w4IDntfSro2Wzy6lX6Morpz9L30=
		</data>
		<key>Resources/_tcl_data/msgs/fo_fo.msg</key>
		<data>
		5hBevNxUf+Li/p7dycVzu9rYWtA=
		</data>
		<key>Resources/_tcl_data/msgs/fr.msg</key>
		<data>
		G2ie3Cn4vEUXk25dd6CECD8SrjE=
		</data>
		<key>Resources/_tcl_data/msgs/fr_be.msg</key>
		<data>
		j82wHQcp6fGgysVvee23mjdzSvU=
		</data>
		<key>Resources/_tcl_data/msgs/fr_ca.msg</key>
		<data>
		MUW7VNnh5NkWYYbVtD9BHOAlBZQ=
		</data>
		<key>Resources/_tcl_data/msgs/fr_ch.msg</key>
		<data>
		yu19QzS62L5Yahru4nD7aROgNRI=
		</data>
		<key>Resources/_tcl_data/msgs/ga.msg</key>
		<data>
		wqYT3Hw2eoQdmd4Vh29eeoAnu/g=
		</data>
		<key>Resources/_tcl_data/msgs/ga_ie.msg</key>
		<data>
		ru3MIXe1kqACWh28/8DvNjTb9WI=
		</data>
		<key>Resources/_tcl_data/msgs/gl.msg</key>
		<data>
		g6KEiZeFlW7LAVu7hx5+BKfDZYU=
		</data>
		<key>Resources/_tcl_data/msgs/gl_es.msg</key>
		<data>
		aZnoIUjh0XmcOJvMbGlS1VFPSks=
		</data>
		<key>Resources/_tcl_data/msgs/gv.msg</key>
		<data>
		LQQR2i9uBEGxqGg2hxeOnrVSuDU=
		</data>
		<key>Resources/_tcl_data/msgs/gv_gb.msg</key>
		<data>
		TQ7WZoqZusmyc7D6i8dOtrud38g=
		</data>
		<key>Resources/_tcl_data/msgs/he.msg</key>
		<data>
		QIVOuB7mcAhtDQwMLw+dhAbfa0c=
		</data>
		<key>Resources/_tcl_data/msgs/hi.msg</key>
		<data>
		gU+VBvzYtZLCKkcCPnNFfEabL1M=
		</data>
		<key>Resources/_tcl_data/msgs/hi_in.msg</key>
		<data>
		Iu7HT8ARBjBxpAw4YK6O842JhYI=
		</data>
		<key>Resources/_tcl_data/msgs/hr.msg</key>
		<data>
		XgBtGsp7vaybimXvsm+vwDxun94=
		</data>
		<key>Resources/_tcl_data/msgs/hu.msg</key>
		<data>
		xiKyHA26g/lD+9EMdG5fq+ICNbI=
		</data>
		<key>Resources/_tcl_data/msgs/id.msg</key>
		<data>
		aTrMKglyFWuYQQav0HkRrxTE8Zw=
		</data>
		<key>Resources/_tcl_data/msgs/id_id.msg</key>
		<data>
		GP0BeAUVgcnwGWBEmb+RsWcSzJE=
		</data>
		<key>Resources/_tcl_data/msgs/is.msg</key>
		<data>
		BMsZdoRqeOqVk8s3BsnWEXPOAww=
		</data>
		<key>Resources/_tcl_data/msgs/it.msg</key>
		<data>
		RwmGctM5YkR06IVOsFEtVKDKSec=
		</data>
		<key>Resources/_tcl_data/msgs/it_ch.msg</key>
		<data>
		fGiMhpPHau4H+zJjfNWOR6hXYPM=
		</data>
		<key>Resources/_tcl_data/msgs/ja.msg</key>
		<data>
		gVom3CS/FnssKnS1awfBwoQm59Q=
		</data>
		<key>Resources/_tcl_data/msgs/kl.msg</key>
		<data>
		2RKgrroIvJfYDpt6Vc4UaVbJC8w=
		</data>
		<key>Resources/_tcl_data/msgs/kl_gl.msg</key>
		<data>
		6XqUj/5sjemfkZhxVd8KgaYwlQ4=
		</data>
		<key>Resources/_tcl_data/msgs/ko.msg</key>
		<data>
		oYpzYXg4lsaRvVvos6H8zMsBX0M=
		</data>
		<key>Resources/_tcl_data/msgs/ko_kr.msg</key>
		<data>
		WS+P+fq7x79IU5r3SNz8kkGu2C0=
		</data>
		<key>Resources/_tcl_data/msgs/kok.msg</key>
		<data>
		YgW9IzaFfzaMq/iWR/VNlOCTp3s=
		</data>
		<key>Resources/_tcl_data/msgs/kok_in.msg</key>
		<data>
		cA5LnDlbVAv86avcgea5t1iJPck=
		</data>
		<key>Resources/_tcl_data/msgs/kw.msg</key>
		<data>
		nC76YybGKWLc2Duo0W2JYW0sW3c=
		</data>
		<key>Resources/_tcl_data/msgs/kw_gb.msg</key>
		<data>
		emvNa+X0H4S2AN81XLAOy5tK6MA=
		</data>
		<key>Resources/_tcl_data/msgs/lt.msg</key>
		<data>
		WC6yJMlxXIM2tNH8593sDYn1rXE=
		</data>
		<key>Resources/_tcl_data/msgs/lv.msg</key>
		<data>
		lz30DQRkvOEOtZkYBtmZC2WrD4I=
		</data>
		<key>Resources/_tcl_data/msgs/mk.msg</key>
		<data>
		2VPdEj1UsCuvSxrg02CBzfyjhEQ=
		</data>
		<key>Resources/_tcl_data/msgs/mr.msg</key>
		<data>
		52CxQ6hUg44Y/7ZlAPTTEt2AY04=
		</data>
		<key>Resources/_tcl_data/msgs/mr_in.msg</key>
		<data>
		/BemdCv4foG71NXLe03O0NTdZXs=
		</data>
		<key>Resources/_tcl_data/msgs/ms.msg</key>
		<data>
		NPvpn7JaDcov2iwAisgSe6K8Jzs=
		</data>
		<key>Resources/_tcl_data/msgs/ms_my.msg</key>
		<data>
		b/ybFqBgDZvEVzIvExa8F1MJxso=
		</data>
		<key>Resources/_tcl_data/msgs/mt.msg</key>
		<data>
		PaWswPUlGFQYEOfy/ld1GVXhK9o=
		</data>
		<key>Resources/_tcl_data/msgs/nb.msg</key>
		<data>
		U6KY+78Jri4iOwQXhkQ6PYaIyes=
		</data>
		<key>Resources/_tcl_data/msgs/nl.msg</key>
		<data>
		U1cGPVaZGI5UTSROxK7933YGuSI=
		</data>
		<key>Resources/_tcl_data/msgs/nl_be.msg</key>
		<data>
		EbXpX/TYIudqG5wo7sK8XpXl42I=
		</data>
		<key>Resources/_tcl_data/msgs/nn.msg</key>
		<data>
		o4CGOoMg2rHVotYMIu1ffbXHuvc=
		</data>
		<key>Resources/_tcl_data/msgs/pl.msg</key>
		<data>
		+5elgwllcW53Vjvmt+scag6mv0A=
		</data>
		<key>Resources/_tcl_data/msgs/pt.msg</key>
		<data>
		FA1rwfbO9f0KOQs4QgU79UtUtOI=
		</data>
		<key>Resources/_tcl_data/msgs/pt_br.msg</key>
		<data>
		eNkagOJCaoS8iO6X2ijsDkvo3kU=
		</data>
		<key>Resources/_tcl_data/msgs/ro.msg</key>
		<data>
		xUyL8F6ObCwJAdPIjIndzzWiaSQ=
		</data>
		<key>Resources/_tcl_data/msgs/ru.msg</key>
		<data>
		l9/7HiJM7bVCeEHDtZ+FN2zUQjs=
		</data>
		<key>Resources/_tcl_data/msgs/ru_ua.msg</key>
		<data>
		My5MyW56Adp/s5nqFHcKXFGFufI=
		</data>
		<key>Resources/_tcl_data/msgs/sh.msg</key>
		<data>
		N80Ud6MxiDjo1ck9WWoj+ZyECfI=
		</data>
		<key>Resources/_tcl_data/msgs/sk.msg</key>
		<data>
		EEQUXBcU/UTQCLE6Mbx3jfvkeVA=
		</data>
		<key>Resources/_tcl_data/msgs/sl.msg</key>
		<data>
		vmlA7J9MXiKPBD+dRqQiNKAvSgM=
		</data>
		<key>Resources/_tcl_data/msgs/sq.msg</key>
		<data>
		RK7wH1aCUIUQmbqopTb7us0967s=
		</data>
		<key>Resources/_tcl_data/msgs/sr.msg</key>
		<data>
		oUJzMFK4fKUQuJRSVjmc6fhzeUw=
		</data>
		<key>Resources/_tcl_data/msgs/sv.msg</key>
		<data>
		2cO7SuvZv9lCWTaU55aowvuSF7g=
		</data>
		<key>Resources/_tcl_data/msgs/sw.msg</key>
		<data>
		mTWzb/Kxxt/ePsN1vEcaDpPR9+M=
		</data>
		<key>Resources/_tcl_data/msgs/ta.msg</key>
		<data>
		EhZlCBr8M9289nnXR5vwvEf+9xY=
		</data>
		<key>Resources/_tcl_data/msgs/ta_in.msg</key>
		<data>
		lN7wBWx+MILlgma85DamHAReo5Q=
		</data>
		<key>Resources/_tcl_data/msgs/te.msg</key>
		<data>
		9DoPbMvdvdXqFAx/pV6agquRCgM=
		</data>
		<key>Resources/_tcl_data/msgs/te_in.msg</key>
		<data>
		0yPAN0f+aOm3P35cHhCxaKQPKi8=
		</data>
		<key>Resources/_tcl_data/msgs/th.msg</key>
		<data>
		r9l/jozBTTBt7dePjzlXOOOKhWk=
		</data>
		<key>Resources/_tcl_data/msgs/tr.msg</key>
		<data>
		TuPi34ZhLbMU+NPnIU174kGqGjI=
		</data>
		<key>Resources/_tcl_data/msgs/uk.msg</key>
		<data>
		ziYYeHXjNMcS/atz5rUmJHxv4c8=
		</data>
		<key>Resources/_tcl_data/msgs/vi.msg</key>
		<data>
		s+XcCVlz5G2ICDJrKh/EUEa1Jn8=
		</data>
		<key>Resources/_tcl_data/msgs/zh.msg</key>
		<data>
		qH8g96Mx3vwzSW7NpQ2FXIOW4EA=
		</data>
		<key>Resources/_tcl_data/msgs/zh_cn.msg</key>
		<data>
		sFUxCL3kOqftNi4r/68avKFWdJE=
		</data>
		<key>Resources/_tcl_data/msgs/zh_hk.msg</key>
		<data>
		+9YJV25ltW7aZ/2KGAGie0PbVIY=
		</data>
		<key>Resources/_tcl_data/msgs/zh_sg.msg</key>
		<data>
		Ix/xtvhZ0CYfFdJCLfCedWzlDMs=
		</data>
		<key>Resources/_tcl_data/msgs/zh_tw.msg</key>
		<data>
		rx7lNqq7gZi6iNNHTtSfdqN+if8=
		</data>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<data>
		aqYWCRrM8STOdy3cRNKcQae3Dkw=
		</data>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<data>
		c5kyhfdvzql2gzZyK/AEpFStXGk=
		</data>
		<key>Resources/_tcl_data/package.tcl</key>
		<data>
		eWjLk4MkhYPTBZ48aealwstvUgc=
		</data>
		<key>Resources/_tcl_data/parray.tcl</key>
		<data>
		oLG9Tmjc4XaNPF4NPHsx4oAh07o=
		</data>
		<key>Resources/_tcl_data/safe.tcl</key>
		<data>
		MZeIeQKYKUvCDqrYnPDJvWUiabg=
		</data>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<data>
		3zmtgdzLgD+Zf5Nh1EIbj2d/Ql0=
		</data>
		<key>Resources/_tcl_data/tclIndex</key>
		<data>
		SfS5vlq3fmzKuAkfMV1CTXrBg/M=
		</data>
		<key>Resources/_tcl_data/tm.tcl</key>
		<data>
		AmWaJUhunU3qT/p4oe90kg2Lork=
		</data>
		<key>Resources/_tcl_data/word.tcl</key>
		<data>
		6BIqnbQBB5p29Y+mdg0vBCSSxWU=
		</data>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<data>
		ThycTe1bKIcdTJwWNWggUgaxihw=
		</data>
		<key>Resources/_tk_data/button.tcl</key>
		<data>
		PguQVCjCk/IQdBRf5DKB8i5pnrQ=
		</data>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<data>
		HtoHhwB4/Ew2kLVLtTMKcix1qgU=
		</data>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<data>
		iulx1BchtvwekS01YS2xSn/Wep4=
		</data>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<data>
		UeK1uukZTnsbvTTK/Ko9IrXhxrc=
		</data>
		<key>Resources/_tk_data/console.tcl</key>
		<data>
		P2qgohV5Wm0b29gDkkaLo8luQ6Y=
		</data>
		<key>Resources/_tk_data/dialog.tcl</key>
		<data>
		wYBuvfc08XAqPlQl+5FaAImE8Hw=
		</data>
		<key>Resources/_tk_data/entry.tcl</key>
		<data>
		v9SMNHZusfrZZqa3hNXe+Fw01MU=
		</data>
		<key>Resources/_tk_data/focus.tcl</key>
		<data>
		aDWlFehanlXVonBz2uHxpddCRRM=
		</data>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<data>
		BcC6290BULtvbvNHvruu506SnP4=
		</data>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<data>
		atj5oR5BZNmtUOiEPzrDzQu5avg=
		</data>
		<key>Resources/_tk_data/icons.tcl</key>
		<data>
		Jq4ArEnwnaDs/x2y8CGYOCO3fxI=
		</data>
		<key>Resources/_tk_data/images/README</key>
		<data>
		8boyJEjSBmI/j+c0GS84PY9/oZg=
		</data>
		<key>Resources/_tk_data/images/logo.eps</key>
		<data>
		K0mbfE68hVTswHuECGMsr0B/ttU=
		</data>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<data>
		vLc9ivJihGOhuVVYGZnHfwn4Bbg=
		</data>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<data>
		6lIhmjehQP2YrqZupUaF3YFY2bE=
		</data>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<data>
		3dEOeYryCe/OAi6XRI5e4RzrViE=
		</data>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<data>
		E0iOTyhnbx4M44P4DRNRDwcZi5k=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<data>
		ilj8GbIL/ciRNRXZsyzL+Kz5I0Q=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<data>
		vZceca6AXCwuUd1UTQBukjY7bAw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<data>
		GhWCZQ4hiwvm/97/1k0n9Lmphw8=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<data>
		jbf7RTt5uPK05nrDCkultb3evTs=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<data>
		+pig/YkQ3y77FO2uwDi045H+qzw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<data>
		5bHe1JCVMyI2Q5U47NndCx/Uk0s=
		</data>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<data>
		JnqVIMQ5AiHc5QF354mk69WQ9IQ=
		</data>
		<key>Resources/_tk_data/listbox.tcl</key>
		<data>
		qiZADGyG6c17jazOTKuAt64hqXg=
		</data>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<data>
		n4k0mIglWV9zEc/w0OGn+45GNKQ=
		</data>
		<key>Resources/_tk_data/menu.tcl</key>
		<data>
		GWlG6Y/DGGI4Z710zl17cnEQ1uU=
		</data>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<data>
		kl2XO3AlI4TR3ps4jGwgOOZG/d8=
		</data>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<data>
		RsGIL5eKTXpu0NLyIO3L2J27+z8=
		</data>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<data>
		lebHHkUlqN2R5Ii5UmZa6cX73e0=
		</data>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<data>
		UprgsMudHbx/iETzRhSeFR3go2s=
		</data>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<data>
		y0VfkQII4uVbJ6lqvYRf7tqIcRo=
		</data>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<data>
		JVMd9iYuOxFwBVc1xah0uRJP6oM=
		</data>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<data>
		V1QprquvZkBCWsG8OXszgsHtESI=
		</data>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<data>
		bC1rYiQpq4wX4HwuD1RkaYI6vlc=
		</data>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<data>
		bCNmzllhz9pTJZpD4IeoE87kGEE=
		</data>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<data>
		jDUW95+3LzKEi0AJHaZ8geQP3v4=
		</data>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<data>
		ytOAWQDoYLlJHj7lwsD1KtymcGU=
		</data>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<data>
		GhSNIwyfjXSNlqec1OJhryZNZSQ=
		</data>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<data>
		X7FjvBCG0zZiKCBAePIZ/ku2fLM=
		</data>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<data>
		j2r/aLQrdH0whw1tp+BYKUkhQGo=
		</data>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<data>
		PkXAECsoeQjXcKMdGQZnjnhQiMI=
		</data>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<data>
		lI7pX0VJ2ox9QSkR0XtLYsuiKt0=
		</data>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<data>
		QKUlRLVX8Zc26hdnv79XCKm7wxg=
		</data>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<data>
		KNnbnL7nkcCb0nLZwqbD2oDrieo=
		</data>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<data>
		MOJFld1oPkcP6fEoFNJ9bSZrUR4=
		</data>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<data>
		l0XIPuyFZWAvjXRhBCSEgAn/pnA=
		</data>
		<key>Resources/_tk_data/palette.tcl</key>
		<data>
		oyLM+zP/c+SkcwtbId5CkPnZRiI=
		</data>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<data>
		3+PcZjwZ6aUFJqUTBD0jk4adj5A=
		</data>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<data>
		140DxdcUZgCX+GhlTKi0iQgtRPs=
		</data>
		<key>Resources/_tk_data/safetk.tcl</key>
		<data>
		0ZOw5gErQuu06VsONbGpzawlIhw=
		</data>
		<key>Resources/_tk_data/scale.tcl</key>
		<data>
		zHYUyeremZlj7ni0IhV7ewc5iUw=
		</data>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<data>
		I+b3CVBm7TtlmYMkAh1mXYEOapM=
		</data>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<data>
		MXkwt1p0YcJrXLkL8VBLJu8dcUU=
		</data>
		<key>Resources/_tk_data/tclIndex</key>
		<data>
		Cco5ZHqhwU2xYBQFXkipsCN2Obo=
		</data>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<data>
		fbDzfHsLpT6walV7xDEi2k6dDyM=
		</data>
		<key>Resources/_tk_data/text.tcl</key>
		<data>
		msBvlyCiAJ4VVtWmjJm934lM5dQ=
		</data>
		<key>Resources/_tk_data/tk.tcl</key>
		<data>
		oXHftZoVSocpWwucoVYxQ6kUi54=
		</data>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<data>
		8JMX4S2O4t0KVuZHQ2za78mjZAQ=
		</data>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<data>
		TbrK7vhFTBsImT12hXxfCap1QFo=
		</data>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<data>
		KgnTJcpWyTCzr7HuQ8lE/UQWuOE=
		</data>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<data>
		eEpCBadYjDG8ovZ/KNcMD5TBWg4=
		</data>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<data>
		AXomewLff3oqIW8UuGgM/wQhQBE=
		</data>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<data>
		fTpNk8JdxUhM47CObct6RI5VRFM=
		</data>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<data>
		Sx1j1Zxyw1eTGoy78HFlRJKps3E=
		</data>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<data>
		1T76HcSytlMfTKQc5YVcF74LBS4=
		</data>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<data>
		zV76jppqgw+fOu4wrDXnutKARpY=
		</data>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<data>
		zKBkgeHR45vHiP29zywUAmGb5A4=
		</data>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<data>
		x+5WJQeXHVCcY+YXvryo2FkfZ2A=
		</data>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<data>
		cgBJKGxDMk+GhBQlkJ4qLiJaqoo=
		</data>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<data>
		DXpaljz4AQxrpaWfopkqIQ49VjQ=
		</data>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<data>
		WPz5dOApwdY3DAXkcJ6tad0BH0A=
		</data>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<data>
		lNo2fV6XAvROSE4jkAFcB8eK2z0=
		</data>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<data>
		0HCgHMWnhySbxtrRhLJJxN03OWo=
		</data>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<data>
		g2QnCoeAp1uLLC1RceoH+ianNls=
		</data>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<data>
		AYqBGx/LdQDDrWehv52PGJlLtM0=
		</data>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<data>
		6+kb5uR8t/pMLMxxBynxPVCObVU=
		</data>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<data>
		9qZnarr+F4GNITEpohU2bzkLgQs=
		</data>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<data>
		I9LaoEaT61JA5Ax0PL/wYUt8Bxk=
		</data>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<data>
		v6wUwzmcnylWfLYjQ3JuvNTOx9s=
		</data>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<data>
		lZqb+ggPBa9QENfviJAWi7m4N2E=
		</data>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<data>
		ZBxEfzDfISM6N1S/zS5c7PHmKo0=
		</data>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<data>
		25gQh/KjETYURgFPrb2LGZ2FZxY=
		</data>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<data>
		6GtMIoi6Dn+JJSka7YAn8DTbgnM=
		</data>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<data>
		gIL95QxCjSURsF9Sn8zwJlHVrJM=
		</data>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<data>
		FV5uh1L21I740yziIo4X7ljCdo4=
		</data>
		<key>Resources/base_library.zip</key>
		<data>
		D3U+14tI1bNJaOF0BynN13i/4Q4=
		</data>
		<key>Resources/certifi/cacert.pem</key>
		<data>
		fylep0/H7Qrw6SvggHH7C3bIUJ4=
		</data>
		<key>Resources/certifi/py.typed</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/tb.icns</key>
		<data>
		V9SZHQrEIxKEVVNh2CYaz8z7UTI=
		</data>
		<key>Resources/tcl8/8.4/platform-1.0.18.tm</key>
		<data>
		np6UarIi4MTsJc58OPeXW15DRVI=
		</data>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<data>
		hXY/N4poumoe7piHzc80wU061b8=
		</data>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<data>
		o0IhPhj3j/YFAzq9bHsQfJQ67HI=
		</data>
		<key>Resources/tcl8/8.5/tcltest-2.5.3.tm</key>
		<data>
		iYctwyEmkuXay/NE9IpAC/haYw8=
		</data>
		<key>Resources/tcl8/8.6/http-2.9.5.tm</key>
		<data>
		e/Z1DJ7ZTHEadOUKduCGBjOPoPc=
		</data>
		<key>Resources/tcl8/8.6/tdbc/sqlite3-1.1.3.tm</key>
		<data>
		WXtTyYAiGUe4LrECT3vTrEOHJ7E=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.11/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			wJ/8o8Zgt60insujdCtQm3O0SF0=
			</data>
			<key>requirement</key>
			<string>cdhash H"c09ffca3c660b7ad229ecba3742b509b73b4485d"</string>
		</dict>
		<key>Frameworks/_tcl_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tcl_data</string>
		</dict>
		<key>Frameworks/_tk_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tk_data</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/certifi</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/certifi</string>
		</dict>
		<key>Frameworks/charset_normalizer/md.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jGPdvsUP3X7Lpa8nep63AeKAXKg=
			</data>
			<key>requirement</key>
			<string>cdhash H"8c63ddbec50fdd7ecba5af277a9eb701e2805ca8"</string>
		</dict>
		<key>Frameworks/charset_normalizer/md__mypyc.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ra7EWnZA84YzMbJKUVrD27tlEH4=
			</data>
			<key>requirement</key>
			<string>cdhash H"45aec45a7640f3863331b24a515ac3dbbb65107e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			vXXUI2BXx5KDa1iGfVwaMr/aajs=
			</data>
			<key>requirement</key>
			<string>cdhash H"bd75d4236057c792836b58867d5c1a32bfda6a3b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qDiwXy8y8X0K9VMt1MxN2ezbuVQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"a838b05f2f32f17d0af5532dd4cc4dd9ecdbb954"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			4cELNyhrNVQHbhQa8CpARYG8SpY=
			</data>
			<key>requirement</key>
			<string>cdhash H"e1c10b37286b3554076e141af02a404581bc4a96"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DSq7Mb7/zlKbtR9zlRquyLz5hfw=
			</data>
			<key>requirement</key>
			<string>cdhash H"0d2abb31beffce529bb51f73951aaec8bcf985fc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			aPFwZwMD9/J+pbI5AGJbFWcu9Xg=
			</data>
			<key>requirement</key>
			<string>cdhash H"68f170670303f7f27ea5b23900625b15672ef578"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			SYStDTXeeBgo00X8eS4BJORqSIw=
			</data>
			<key>requirement</key>
			<string>cdhash H"4984ad0d35de781828d345fc792e0124e46a488c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			EBgc2RLwuAiVK8LbY7mSzbYdD+Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"10181cd912f0b808952bc2db63b992cdb61d0fe4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JdU1EOPVIOFxxjqOiS1rJYQ7Ihc=
			</data>
			<key>requirement</key>
			<string>cdhash H"25d53510e3d520e171c63a8e892d6b25843b2217"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pWdxiaIXOAAI3d9Kaz++OGFZ5PY=
			</data>
			<key>requirement</key>
			<string>cdhash H"a5677189a217380008dddf4a6b3fbe386159e4f6"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QdG8tB1EqqddPGj/eLh/N75n0f4=
			</data>
			<key>requirement</key>
			<string>cdhash H"41d1bcb41d44aaa75d3c68ff78b87f37be67d1fe"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			NLlsGw3244W9v19N/QCktvtCs2I=
			</data>
			<key>requirement</key>
			<string>cdhash H"34b96c1b0df6e385bdbf5f4dfd00a4b6fb42b362"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			mYW67GzGSkUxTwBdLN1xwXRQshI=
			</data>
			<key>requirement</key>
			<string>cdhash H"9985baec6cc64a45314f005d2cdd71c17450b212"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			wPkGQpn5jFZYO+TpNN5oEokQjeQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"c0f9064299f98c56583be4e934de681289108de4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			WmJ9pKxs9ODRwzLjTVkGQKz483o=
			</data>
			<key>requirement</key>
			<string>cdhash H"5a627da4ac6cf4e0d1c332e34d590640acf8f37a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			aDxsDPuP9MDik7PFAT3YiJae+34=
			</data>
			<key>requirement</key>
			<string>cdhash H"683c6c0cfb8ff4c0e293b3c5013dd888969efb7e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Iz10XFbvFPs8Pl6Cqkoy+e9hDVw=
			</data>
			<key>requirement</key>
			<string>cdhash H"233d745c56ef14fb3c3e5e82aa4a32f9ef610d5c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			P6APp0SwUWKp57SDewm8p+fIWko=
			</data>
			<key>requirement</key>
			<string>cdhash H"3fa00fa744b05162a9e7b4837b09bca7e7c85a4a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			p+sLqmOc1+SF2nb8/w+0CzgxDfM=
			</data>
			<key>requirement</key>
			<string>cdhash H"a7eb0baa639cd7e485da76fcff0fb40b38310df3"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Wu4BHgyPuCyFXXsBaKDj8CnEwBA=
			</data>
			<key>requirement</key>
			<string>cdhash H"5aee011e0c8fb82c855d7b0168a0e3f029c4c010"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			30sD8aatG5AYQwdvyZLhJeBNnw4=
			</data>
			<key>requirement</key>
			<string>cdhash H"df4b03f1a6ad1b901843076fc992e125e04d9f0e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1fLLP6EZ2Gsz6+h1YAj9ww2DFeI=
			</data>
			<key>requirement</key>
			<string>cdhash H"d5f2cb3fa119d86b33ebe8756008fdc30d8315e2"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			q2cUxYCOVnTQNinc4C+mhDDBc6o=
			</data>
			<key>requirement</key>
			<string>cdhash H"ab6714c5808e5674d03629dce02fa68430c173aa"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			TQqAsUBcwsJYkPQ39cgXJotmdi0=
			</data>
			<key>requirement</key>
			<string>cdhash H"4d0a80b1405cc2c25890f437f5c817268b66762d"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			EVoOgM0wizZAToym2Q84aRG/7K8=
			</data>
			<key>requirement</key>
			<string>cdhash H"115a0e80cd308b36404e8ca6d90f386911bfecaf"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Q2QhviSzyCmiUEn4E0IWYBwP+AU=
			</data>
			<key>requirement</key>
			<string>cdhash H"436421be24b3c829a25049f8134216601c0ff805"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			b1KDdF54eQuHzBXi/4T66LoSOOc=
			</data>
			<key>requirement</key>
			<string>cdhash H"6f5283745e78790b87cc15e2ff84fae8ba1238e7"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha256.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			h0siE5ImLMZ7n4sGPyHz0u2xisE=
			</data>
			<key>requirement</key>
			<string>cdhash H"874b221392262cc67b9f8b063f21f3d2edb18ac1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Xt+Vs22AMNnEMjfI/JMpwvqIYg4=
			</data>
			<key>requirement</key>
			<string>cdhash H"5edf95b36d8030d9c43237c8fc9329c2fa88620e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha512.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VKtc0orMeLa2G6CIMXNow5FoWZ8=
			</data>
			<key>requirement</key>
			<string>cdhash H"54ab5cd28acc78b6b61ba088317368c39168599f"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			YU184SeWoa4y2B/mSOWuLuq0Jtw=
			</data>
			<key>requirement</key>
			<string>cdhash H"614d7ce12796a1ae32d81fe648e5ae2eeab426dc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			H+9MhzkkJmkBZ9/nr/mi3tRasqQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"1fef4c87392426690167dfe7aff9a2ded45ab2a4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pnsy+iZ3cvB2E4/ZrbffOckWE9g=
			</data>
			<key>requirement</key>
			<string>cdhash H"a67b32fa267772f076138fd9adb7df39c91613d8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			sBpqBWfcA1KYYx2KyxRLyTjITOY=
			</data>
			<key>requirement</key>
			<string>cdhash H"b01a6a0567dc035298631d8acb144bc938c84ce6"</string>
		</dict>
		<key>Frameworks/lib-dynload/_tkinter.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			FsVfnSSP6JPJ3MCacw8rpaKv0jE=
			</data>
			<key>requirement</key>
			<string>cdhash H"16c55f9d248fe893c9dcc09a730f2ba5a2afd231"</string>
		</dict>
		<key>Frameworks/lib-dynload/_typing.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			8b4l8PL1imbTayRA81YC82JrppQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"f1be25f0f2f58a66d36b2440f35602f3626ba694"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jjlw/WjtY9o3/iSenDrk6Lt4/YA=
			</data>
			<key>requirement</key>
			<string>cdhash H"8e3970fd68ed63da37fe249e9c3ae4e8bb78fd80"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			G+v4pAlyNRUJFb20A8M01khrWng=
			</data>
			<key>requirement</key>
			<string>cdhash H"1bebf8a4097235150915bdb403c334d6486b5a78"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			InC4Cj8MeG+QFgUAGeL4REs1ZSk=
			</data>
			<key>requirement</key>
			<string>cdhash H"2270b80a3f0c786f9016050019e2f8444b356529"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			KYo+MhVXKf/7V9/yKkzYLEIPPvI=
			</data>
			<key>requirement</key>
			<string>cdhash H"298a3e32155729fffb57dff22a4cd82c420f3ef2"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			V4ftx3rB5vxVA8+ZUOGLP7cH4TY=
			</data>
			<key>requirement</key>
			<string>cdhash H"5787edc77ac1e6fc5503cf9950e18b3fb707e136"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			OzvAYunb2RzOp7VjnLV4QfI0yQ8=
			</data>
			<key>requirement</key>
			<string>cdhash H"3b3bc062e9dbd91ccea7b5639cb57841f234c90f"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pIXb0V7th2wNP3iA8pnAAf3U/YI=
			</data>
			<key>requirement</key>
			<string>cdhash H"a485dbd15eed876c0d3f7880f299c001fdd4fd82"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			L6Kwy5i8bmYS5E1uw1pSR5ID4bc=
			</data>
			<key>requirement</key>
			<string>cdhash H"2fa2b0cb98bc6e6612e44d6ec35a52479203e1b7"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			RPeMGbaNPh8cuG+fbPVRz3bMGj4=
			</data>
			<key>requirement</key>
			<string>cdhash H"44f78c19b68d3e1f1cb86f9f6cf551cf76cc1a3e"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-311-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5khG2yrUS/ZbQI+AiuB+MYr6siA=
			</data>
			<key>requirement</key>
			<string>cdhash H"e64846db2ad44bf65b408f808ae07e318afab220"</string>
		</dict>
		<key>Frameworks/libcrypto.1.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			LX3DtvOsWjR/RmHgl0JNzQL7RnI=
			</data>
			<key>requirement</key>
			<string>cdhash H"2d7dc3b6f3ac5a347f4661e097424dcd02fb4672"</string>
		</dict>
		<key>Frameworks/libssl.1.1.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			zUSsl1t6DvlqDQT5V3YFw2Gc7do=
			</data>
			<key>requirement</key>
			<string>cdhash H"cd44ac975b7a0ef96a0d04f9577605c3619cedda"</string>
		</dict>
		<key>Frameworks/libtcl8.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			Vgur3KayWEDctl04NSnY3SEfveY=
			</data>
			<key>requirement</key>
			<string>cdhash H"560babdca6b25840dcb65d383529d8dd211fbde6"</string>
		</dict>
		<key>Frameworks/libtk8.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			cvPlniVtpxZaGNhhqiiKL3FY6M4=
			</data>
			<key>requirement</key>
			<string>cdhash H"72f3e59e256da7165a18d861aa288a2f7158e8ce"</string>
		</dict>
		<key>Frameworks/tcl8</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/tcl8</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.11/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			KhdudGfBXxIZhmLbaLne2gcm9PdEFmyFBguJTOZ20FU=
			</data>
		</dict>
		<key>Resources/_tcl_data/clock.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			fyfUALCIoOcq3rSNFwWYkulfCKKgOXC9dM37NbEGYYs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KUyXF1/QiUCTuGbnNUiuZgru0MPMHnOGfrZuUtNMDdI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Rlri1IgLgAaxR2zWD6z2dodUOCRMHZOn2+TN4QNedF8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			CpX2f5CxzdQHp46kAyr565lvw4hkxG10tCs6f37aDIo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			G0Lffn1rD+sXywvI2X5s5omUkjBt2IDEijnRovAnkAQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			LRvtJCLhMaFACH+vGxK4pG9947ZBO66Lw5XAbw1wubA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			FN864w6B52IL5ru3qeQgg68a4E2UzxIDVl+KPAVCrOA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			IXTZThwdWtk3F7nowgVp7ZWor1Gy06srzpnxqIcEnA4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vEy+TJn9ZavqRfva8ozB1cQhGSgBJfu9XCwRiSrkYLI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			+L15rlqQ5TkNd9wxyzBlsPk8uIE8nmeszscuLbICegg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			u6zqgdT3o6fzwDYnOkU00x2/i2tcyivMTADLFZPPA9g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QVFDSnFPyCIoZ3w5sHkIxOGZUvwFjibnw+urdyTODHc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DNtZ4lXM19z0r4R8mwIK6u54zn/PXyFOvPEjMorPnyQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			1weh8DUUgG5xTwHL/LfJ+Zc6zcgMLWe71Ob4UiOlCVI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			i/yjSGmz+aOy/HGwLLrEFRKvbR+KsX0lZOZTIPiO3hA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			RPsEtccrWEtig6mbNHiWkMYntQg8XfbotberLGiQPAY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VaotE7eJsxJfXJ0NxbbjqQ15Qm07eCXc1gT1bUxuNqI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Uma28YwxRM+tvLex0n8KfqocZB/TszkF5C5FSf03N3A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4/BxxjrEOvZgYVBu8sV0w1979IVT+1FYrkHZIwwaEN8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			45mFxqI4CGtUQnR1UZyeAoV1BwfbUh0YIOY5cjwBw28=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			bbWROWJ9KavTbzjtLg3iprI0p9fmgcfbr4uIjxysSaU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			pDpbWL/Fe9cjsSu96p9uGpITYLNtLVLEIPNymXiEQtM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rqcW1JDDVDliGo8Ayn5Dl+8ccEKOIGxQNrevJfHD2C8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			E7XLSB4CFqj8KL+p0PawYM31xFez4SQ1yoJusu9SsGg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6H7AdvlQ/NWBieNi4VBd1VsMj0+n3RqTMcXBEdLOVp8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QH/A/gbSoFfpugEJ6pNWyrOPJ3VtE17zsGqFcFthb1A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			gmM2Q80yZUORWsxdKKY0tXlSdM05l005VeUdczC6kzg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			J/FuPdArIhLEmA6gm9wGjPAVhKG4u5FFbAP8q6vgkx4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			PgZzY/wHZi6+UrphfCqtNkkg8q85WzQWKXQAhZrNeLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			nTPfbhz90s8lU/XidY9FfXEMr/X4xplo8mZazNbppv0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			YZMwGSmEqA+TrG8uTl6qRj/T3dx1wfZfOXXzPg3XoLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Jry2IEckM5YnF3EtBFl6YyZMjkREWUMlZcTBE94KJAs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			H7mj1S1DLqLWzUOSfOv59Y8wmiNuGxHSD+jVpfuUTm4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O96a5+r5vnmchLKqToDXi+isusoeSG8Qub3ULjrt3LI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F6fUXzuC8qQuHTaxPbXO0HeUWj6CcAlHzR+APdKmDb8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O/tCxNNtF2NpOu/Oh/YnehGtWnVtaR3tqATZ0O3LMJM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HxrUxAebM7cG6UinNajDBC9AzGgGXEjCIND1b9BIwzs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DA3xe/7OiXodp3ZcgiRTsJhmVzAozsztE+Lv7gK8zMQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			onF64J4M8tVmwkXcXFiJ0yZmG0DbDV2abZW45rDw51M=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			7BG/1JxxXNifudOHoHz1QmHg9KHM7BqBDgLHs4rS8oU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4Sko6LV1TUnQ0+eZE13itIC6hLXbqg41DZhG+mf5Q+w=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			asDxhFpWoaU3uabZvLck3d89Ol5hh5rpJZMbHAU0+7c=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dT3aUYp+n23AMJchsfquWMlmH1RYAdqfBHKDkfcL4tA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dU72vzpWQiirC1bd45FSHcwabIPPuV1LdhFB5x0ujoc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			efZHDZvr0wgys6nKWc0f3KKMW+Y3O9AdlJ7uG6Uap6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rZOROf9xQJcL3j2RGA98TXA9f89noCxxOS3hmA3FYOQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			M8YHKgBrpOlRPXt/09CLHHRcoQebbXlsNrKlro5K4Cs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jXNygyibr4wI7x3X5Hpsd12s5IBBnF4qktbA6Fu1s4E=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			x7A3fzDkIEhJLkcQ/loKVPqYZTlbimdI99rFO5AShPk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			xAygFLiPl65irhqBbFljse1DKnfYTYnDp2S6FciiNwg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DgcySAM4oinMOtTN3gkCGgqBkC3G7ftfEiA+Kv9EZo8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			czQsJ89V9iXT25DF/I5zQP/fhaUYctv7HQqMseQ+xdo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F3Rb3SmXeekdQdsM7ibNxxMto2ZpB6lCELWRztWlWts=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vF7RZNFTIUBLvcrQ1kfDIv+rFllGIYLb05RUOdnsuuc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9QLgeuPxnM3DHkNASc/HM91d+FSHwBYLAzHkAkGtAnQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ATs7KRHGa8HKVOUQgUr0lUwxDaEHN/myokdNcUviqzk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4dIHkXqjSD2REOJKDMDNHg5YQ8i/yQHP7npthy3ZRak=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HNz1EMOEZOUoTtz67DNOP8UWI2wco7mrkcqHjCOGaRQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			SA9h0OGnXe5Zv5pm3gu3j6rk6H/WMX+TSAQSEjJ31EI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			EY6hYO8p4RtG3sV68sREBZNN2KfEnSvIuQyU6LqmE4s=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			JKnTef2jnyvMBYDKPgvS6Zriea9eKEHJ59vn+THRnMA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			cUKxEguZPWCRGXV0CQ/gS+PqZP/DrVoWekteC0LJ8GI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			u7cpuQb1/Dt+5mlLIIsgbRmp1NxXHiNbnJTc3UoyOio=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			D4tTCtDey/jdgdqCkbiw+XbGQ7WiktuEaAsx7Pvl0Ao=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jzCJ9LLKR7esTLeDdbK/rAEmgROnxn0CD4tbfywlu9o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jQtqiCt0LFzOk4JBMoYGwRHdoMuDM06+3NoXYF82Qa4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6xNaiVGfLgBCgt7SGxHDr3zLIyDJdy8t99GkobZ05JE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			j7zGPLKJr6rhW0OHUsF0b0E/O3m6WEXC71K6EQT4vaY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			97/5gije2YHsmk0dDaYiR6jSPxWJJuOsvsPM43nJmMI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Yz9ePnW/FZDJSrnL81ONDwp6MZ25AWmTkIRS2QPZxP0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KmhWKY7GKaFr3ZJHEd/j87HjqILd8EtzEHhdg+wNVmw=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			aPIrrTDaqBshWSVBbBzIM2Czu4fvw0IFiSlzGsZ4/zc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ca6ArftDe3vIjzx2/TcHREmzUm56pXdtK5/VpDwGb6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			zq1esrC0TvQAP7yy5JygUDmSuh1lQNEay7uE/bvW55o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9wO390zG9fqpWfUcdXyUYjZ34nATvK4jvvugGjkmRtk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			TJTn++GDN5gFBW2WCrYk14h55DJ4Ji5Na5ireOX+/qg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dQJYfVLngQIo8uy0WsQxnqD1wAi3rJEFO5IAENxt35Q=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			c/0rXhQwnYwDbTNPE3ue3x97MtvUVJHPkxhIGFgtBnE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			WRi14d7wl4Hv0odRtog2ZaruKfHSRPCJHt7Nqb9qS2M=
			</data>
		</dict>
		<key>Resources/_tcl_data/history.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			m25ADrhUQOxkq2a0rBEVRlhXQMnKYf0VZADXFTy62fQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			rL/5te91eQkguVAjFW+tgLGK/4yvxKbcA4k/k4jgU6I=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QgxLMIjJ2s0hvDSAEcrGHXyyg7m+54rnLu12SrCUZRw=
			</data>
		</dict>
		<key>Resources/_tcl_data/init.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kaivjb23DtKWv+QHEm+E+W8hB6/t0elptj5SYDS/E+M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/af.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KYGWW9I6k6CetbSjNKyxXQBkXWRcWWpeytuIv6C2qQg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/af_za.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			PC9fYx7TYD7w1byzHFGyNTxcJ4OcgGoDbztwB69/Peg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			v5hOx89hnnAP5+ADgf9Yq+m9L0s91iLrLtrMxeZoEFA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KacOrEOx86oYnYrk2SZY4HeDlluuQX+2buX2nPy1ZPM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_jo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			eBFufnBsfR4+dEYJRwmBn7OaUMKiMC+S1qSY4G7Uoxs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_lb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			fJcO/rVcU3WBQ99CzEUqNjL4BUh8pp21fjfB9HinVxs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ar_sy.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rvF7lKDbh44vD7SdmCBXxbZjKJ46jg4rGV3Ow36FVbE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			15DlQhekv5p+HctPM5m1hhcokY6TzT8Atj8TSb23HFc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/bg.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			5zfY3HJKo7nsBxZcE+hijGqKwegDReENx34fxipthvE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/bn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KX1NfK5umds8pu55NRlRK/9lATzyYc+Q3tTSjT1Pgm8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/bn_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			fTqVZmPFKdB8ipYQQUNW3nF/Oios6bMxsFI2cnCs6pQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ca.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			FP9WT6tYRXHpVL4g1hwvrLCW/is+82nMXst8JcLZLVo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/cs.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Sc9FLu8LiXC8Vqe44EC6CIIVUIIop3Ayy6ADVSJBL4Y=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/da.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ylj/W6qWgdkWLglOgzRwB3t1VbsJ7ujo3UGIGxCACKA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/de.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zDZylpwd0iPq3ZoibgDKxzHYJFUyQIt1q5pw6e3ShnM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/de_at.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gFE6mWmhKo+wGALW/DAVcSpO/dpkVSkRobs+p6CY0Cw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/de_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			dXNYHewn6QsMfTQFfZ9O+JcnMX1V8sTgQopHdA+x63o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/el.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			my+RvjQCT7z2RfbvkkYOX5RMpqFiaLeUeKuQSyk001c=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_au.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			uFyaNz/w8DYVFDJlLdVcGCsHBL0GJeqEvtFyfsDePdg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			WWrAIgTIRap0RR/FJ2RVSfKjMYy2MFH8rLK/lI/Xc1E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_bw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			NAgE9ztiBoaraYsiAhkdaSJ+c2sWUiccmfLP7wPXIpY=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_ca.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yf4iI8SUmsChk/Mh/A/Xw0Sp5JpUsA+KTDBAR5hlhjE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rjitVFIxSwlGxcudPInN/CrSFOFG62g7jQzj/oQHD+E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_hk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yb4smtMdUWtQjQHoW8yjdar4B9bYzXxlgIXVAHBp//0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_ie.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			54aMgP1Z0YuxU0XSn1KShW9jlVnP/ULuZJwWx5OL9Y0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ceU2f+g5r8QzjFDUUPERco4JdTjsrMwbF7ECOAAbC7E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_nz.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gS2yBOTLgmYgek6Uj7o90e/k0HG7t5P5dDpDIKHO6+M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_ph.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vhB/X64eMD6nZgdcUu8hRu8Unto3Zid24Y6TaFsXbNw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_sg.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			uNNUUZvU6xAE63sl9OI/0+5/UzpfSRpG0Z/VIO00yTA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_za.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			mM6cpLtZC6X5ItahluU4HhnGTnaCzb75FPLc5nRaczI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/en_zw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			kQiLu/WKcEGF3sE9vUISlrvScaGuu8s++FqZzs2Ej/g=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/eo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			UN8+DmaVAu0I3XeNCv7fD3GZO+OIsPyqEGXRyRvSLYM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			//LwilviAsgeRp4W1N4figwc/lVs2gY9oHEnnykxSDc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ar.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			r1MKzWlnZnjJW4A6KaRGQu0tLy0HfPD0e1P/JLrAOy4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_bo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			JLWN44zUyyq9CNHtpslFT/3n7RozNntFfXcCQ0oKVe4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_cl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Eq0VRus5GYkQXYC0GodobTswYm0MQqc3BfM7LXEZUMw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_co.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			G0l5h0w/AlMX388LBvyM7ggKKP8+jv4d6eiZ9tT00h4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_cr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			esX8NbxCKlRFYD4EMCNuYsyjVYeHgR3iIwX3LUOetLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_do.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hHwUwpfb5NhRfeuqjtVV89rt+EPWutH0EVmGMaC9NQc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ec.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			IIbujXOY1eYOXDBIhDs4hDe9byUH0ik8ohiTbjv2Hlk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_gt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			G8Iq+YJn1jXj8HYVomSnFpQKKx+qXKo6/1TUxaSjQ3A=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_hn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			otJYgMZDCVUqrO0ILe7R7gBkgqFMq5fbUk6Zg+6ErPw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_mx.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0Pqp15l9Vpa/+SOEFE4LnfsuTDg3WBdhP4GonAbsY4M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ni.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Syi0aYG7t4y9KyIGDi3QGMZvz/HO5SdVQlrUkAqQ1sM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_pa.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			aDLcWrn2EIg3hM9wJpH88WhQZRvBxqd6DvqB9DvFCaw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_pe.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zIJsk2gu8Z0pq2MEZX4HgCxwzxix5eqZw0gN9tI4OYM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_pr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GVYyJc54dWlsaqLBVuZDgpLeQ2tY+NfCMlPjEyBp+aI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_py.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			lP9kIBwnqwTzYmF91Wt9hbIjvMoHNRJBludmknDFkfA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			4yaMlem31HH1/SQ2wXMY1aeWIgujnOvrzTn7sBQaSc4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_uy.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ulV6PGVidaDIcPuEZvIjeFD1p88tABkZiWcluz0+qks=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/es_ve.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			ZPeWxePjAESKHzCaDafUNUjMQFEQNv86PgyRfjIUfWI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/et.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zMK0c42xb6+0i/x3yeL4vhe8GeQUDki2Hz7xznyfOow=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/eu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			fcxJZqXBOlK20dtiviALm1od7LrM/K8VBF3QOiw+P6o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/eu_es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			k1FkotLRSBWQa0OFYoibMROVGbOo6Ns9KsFSp37Fkdw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fa.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+cpIGejIsETX1oyX/Gfg9MzWJF4wAkFh2rJND3w6loM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fa_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Y2DODzHuWT4xGydfPB8e1CfiN/MQEKQoDvLFiqbyYzo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fa_ir.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			z0ksvXOmwjByUiXXBWa25G1XML0/Y4eXgd5EM5ZWIL4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vg0tzgjmzXhrw7B6H7GtxbLPEgU8merN2qzduIAt+5w=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			8kndFpjtFofhNlTATQi4KRkwJ6L+zCQiLshUtZNQRmo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fo_fo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			cUW1esXAdLypaFgLM3wEpxu9bvuTr68pHBNh/XANx5E=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			eofkGLbY0U2MEdY3CLONYH0o992/OWBsfY+6Ir54kso=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			mA5wPfse7efeSMlY9rUB7UJR9pyw+84PyoVVX1rPE0o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr_ca.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			8W4hLV0fboOp/E5Wh05Me48ZR+6IJhCnMZlIAxnvpSk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/fr_ch.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			qxYL/etcOt8HHgHHgxKoHuQiO79UcKuICXK79ZZSkfM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ga.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BXx1wa1wZTcz3OQ+pb8VFQDzkxTosCNu6A+NXbYjYn8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ga_ie.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			IHLkjJi0gNtWdxiINkhbRgXVqdmYcKxztb/p3MbbRvQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			xxoHFpzb6ZYmFtKPOMMtZB2id+U+Z/jjpp6zIMHiuIw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gl_es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vCsEJM8nvvZ/MJ4rbf/vTTnEbxXZHBXoPgcMf9TiDJw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			dapob/kByeZuUdNujnjlFUtX7pBFeEVo9qh5jqlokgc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/gv_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gjrwD05EYT6SnTJ3DtshQTK24hDocnUWJIJNpfC3hEg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/he.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0nra9067GNaWSILPkxJgMxuTrksoNCf5oNsUeoPeHVU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+v5l2wm9y4Y3Qv2ocFvNHDG1ng3Yo7NH6m3sJZbO4Ok=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hi_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			KcfKNY//yvlHU8fML2O1g4YjS3VVL6MnLC428lN3DD8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nhTY9/VL6VOYPxmMjVnziELF9zQZpegb5kYLNiPnMHo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/hu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MU9BgMBd5KSGD2WvZGCQD/938SwI7dco9oygBlEmua4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/id.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			HxsPXe3gJjvYF3OnjpivVR82NhrMsxW2GMiucKX+eB4=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/id_id.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			24FkO6H9EV6dVHlDqImlbfwMgbY/IbHtwZVcaITBsvU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/is.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Z2cRX/8toF9JoouteIU/rG/HFhhrmFR01tMHZOFyfEA=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/it.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			x9hAAYVVhqC6sjampYeJItnEouoXmb8YVEhpNZdQwN8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/it_ch.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			LuNW/6JJGlpgvffX/r+sQmgkkEc4YVoMHQeu9r2jt28=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ja.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			H6ORprIt26X7BDHf4FB/CwdUFAtCRwDxZ19ywnmrCgo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GwAinfWpeaBAM5u8ctRI85lo/uXMJPByQcn2EpqbU90=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kl_gl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+Z2kUTiorr/ZJ0f8KJkvDDFcbErZdxDq+UJyY7/6E5w=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ko.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9uKw0RbSyayQ3aQwtokjcdh6Ts+2lVMYl47W9unVRqY=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ko_kr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MaS3T1HFhDVJByUcVf5c6JTSyWGBVqHcb1qXm8NQ2xc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kok.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0jbVsnGEsegT5obZAUGBF/ItZwJOaUQBj8S2M9+f90Q=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kok_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vuB/FMf0/JO2KsMY+J0u0N1v8w0r8hwodGVP8CkqbEs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9J9OHHFCv3qC/CufwHUXGuRZA/5pExR4wVIZ1yu6rTM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/kw_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			eopTnIuZCu/+oGGIuY3EN/0qbon/Zkg+8zSZTnP9Dsk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/lt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			UQ2O7TBAtQr69qPIW8mIR/G01dimhcXsBqzCSRuJAQE=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/lv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			/ZWzijvr1ZRovcKJC6xZ3zHDUuF/LnfIJHHhyolGmAI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			84TdiFIxR870Kqhx0yP8TL7jOP9nzFyVrseUDA5TGuM=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			6y4rekGFSvaM71iBzx+/TTjnDS+rLD885ZAapcxW/BU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mr_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			91opuzI9tDVLDHWcscjFpP/Ddt/9dCdMpgo2mUgWp1w=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ms.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			gx9hHuhRpkvxul+aVEHsHVByL6nxW0IncH/hkn91TeQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ms_my.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0FlI11wGZprduXCLxftI5rZR1OYu8bMn74o/YF/VJxw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/mt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0RVxiBjj4zZ4R841u1/wNh0ImT2XSdQ4yRj464etiBQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vEAYid2TTEnRDZm0cUQb4rU2sXInOcewq33nYpaA9gI=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			SRKLNriOOAGIBZxLWTwxc4LzLinRrcGNWNFNFCRZors=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nl_be.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nNVOwky9vsXk/lQ92oypU5BnjUMtMyAfocMrYfj+Ilo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/nn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Xuk6jCRXIt62S2jv9QwIHyTaXeQ9mZwAahDEhOHTtO0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/pl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			w5WV3cAJXrSunmbbAu4XWzGsPaH2SeuI+mG5Efg491M=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/pt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nyv/o7TYeDss+yztnMQxms8GmI9hgpoeUpHVWxmFTog=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/pt_br.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			44OyBITukMAAVNUt1a9HOyrJ3FDBTUWaV5719EJx0lY=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ro.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0uFL4Yg1DTQ5J9U4DrVnIDn+mjfpqZV5IbQORhmzYCc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ru.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			wqOgvlvFpGpqY8TeNOMXtAK61Awi+yk24aT1PB4vYl8=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ru_ua.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rl098j8BlFXz7fwyYqrCsACYiB8Juak0wNJsCriWcAw=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sh.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			APEZcByfPronNwGmpzGtr9e4kC9rzPNOYTCJhEVuGTo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BD3s5up8g5VrMwC5X4oOkrraqPwp1sUQcGZJ0dgQZ5o=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			vUiMnXkavt9pi2a3aOK/JCUf/q8G9T+zdGyrRXcQ/3c=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sq.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yycAfhODFbBkV2wXkxKAz+bmkp78Pa/XFxcT0gTPw78=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MT6M27wCiK7ZIrmSenMx0Pqi5FHUF0sfW3bFyfrsj5s=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			TzLhUYvjJw9NuAE2+sADHDhd084TP6pTTxQc9FnGETo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/sw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			a1q4riZdtDaxXTImOohw7FXHwMB0FbP5uqw39zvHBOU=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ta.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			MKFCpI5X8ZTsw6qSQ5MPPm4bToszGozdJwXsnCgNzLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/ta_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			qlfV+1zD9Z7Go/mdelGEQDgJqjo7wC7QhCUH1CGLaD0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/te.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			QsNNAqYHnE0NaDdQs4CfNFY3vG2BRlLD+ws0S2a3DHk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/te_in.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			iL2vSyW2hLAyCi4R0/533d0l47FxQb1+0dY2mMSA5Lo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/th.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+WQabr44Rc5dNs7Uc3SfWQnJDFLkBfB0ptqBfvbzmGc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/tr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			33xLpnRXy0fu8PXKjgKP9Gas3Yd6SHaX3EjsrHNHrEc=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/uk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			zy547zMi8BIelYCY71+S2gCDRGV6c0OerGWMtr89cr0=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/vi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			nGkJTAvVLVroRIQxV06ujuS+MewuhgI2bfbGv0vImlg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			74G0HsafZ6OU7OKzmDtns9DIgTYkwr+h2KjBWyFgisk=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_cn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hfkc9uMWd0ql0MHsqFyI5ZH9U3Flu3mSnF5qHKmeVsg=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_hk.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0SPgtMJhT2gICLWMygwUC6GHSUssi8+MYEx+tznHCII=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_sg.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			JoNRd2avnaDYe3qGLemt6oLZoUVPx3Op48Gm2Sq6lHo=
			</data>
		</dict>
		<key>Resources/_tcl_data/msgs/zh_tw.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			1YJAbFGj2x6t9lB8UKH4V0D9p9qOJ/wUOP62JCkAyxI=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			iVyqFaX7+fSZv+pq1lsj8L1IgEJqccZdlaSaRDyUAs0=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/o0SKRdgQ+4Zfz23UL1srfJl17PRJ8h83C8OEOLGpUk=
			</data>
		</dict>
		<key>Resources/_tcl_data/package.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			wT3PTRZZqYdnEpBZIli0NtJtz9oGEldh7x8EDGhpi2c=
			</data>
		</dict>
		<key>Resources/_tcl_data/parray.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6+WitMu81/0/em921o14VjAdsBs1DAQJQqe4BqRuABQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/safe.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xYtv81FHG6snVQGYNT4QXTid3SLPOf0YxiM11MkpVB0=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			TfyC8Oqxao1AE3hBUYFDb1MsozpWdpulaeipvXlBngA=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			lixg634FAGFGL/cs7JdBp/GDB69KqmjXZlF0+QSELRM=
			</data>
		</dict>
		<key>Resources/_tcl_data/tm.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6IzAc2Qxh8CjvqNbrHrWmMMXq+E4eoAWPNHZhTP57vo=
			</data>
		</dict>
		<key>Resources/_tcl_data/word.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			sFkrDh/Ww8DnBBaNSvl+nVyribXPUXbhntW9X16yoXU=
			</data>
		</dict>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZESFUunddehLjfY1KeZ/ltuTUdSRPsLpb9Up/kw14Fs=
			</data>
		</dict>
		<key>Resources/_tk_data/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LwE7ZD1i8I3aqh3qOf+A1mB1acnhrMGUBjd7ZNdcz1M=
			</data>
		</dict>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			MM5jHLHMzSBXABgWLG/+8xutN471st4tmCyW5l62LvY=
			</data>
		</dict>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			RKJHJmvMUArepV9IffOEs/8WwfwHREELek7xleUaS28=
			</data>
		</dict>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9izluEUK57PsK92f32fHf8yZqhjeGV3lM3x1yZeksRw=
			</data>
		</dict>
		<key>Resources/_tk_data/console.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			JKS5GZ/7RFlJ6DE8JvrToSn/sZeHoJ/QgAW62bCHRYU=
			</data>
		</dict>
		<key>Resources/_tk_data/dialog.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			0bHcykYo9h6hUqD6aCAXX2E7w9bpK3OdATKB20huYl0=
			</data>
		</dict>
		<key>Resources/_tk_data/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			2TXLgorI3fGpbd9+I84Yo1SwMj9seE4+hd5g9Vh/XHw=
			</data>
		</dict>
		<key>Resources/_tk_data/focus.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QNThAaZLdTYfdjR5sBIHrnFTUzfnnObhYiZYQvZHHu0=
			</data>
		</dict>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CBmd/gDtgiqH2SdoQQlOBbtOEeprWtM68Y3f1sSqPfI=
			</data>
		</dict>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Ie9n9maKwGsyRyApfBfxZIo8KoymdmDZ7f9K+5YYyj0=
			</data>
		</dict>
		<key>Resources/_tk_data/icons.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			TKuArOQQSkFpyb0f2yTWl4ibmEuRopC2mkgZEsOKn9Q=
			</data>
		</dict>
		<key>Resources/_tk_data/images/README</key>
		<dict>
			<key>hash2</key>
			<data>
			JpWt/46QDDG02GQU0iuKSdbdhlyj3Zlnj6NVzcRgk6g=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			8+d/2UGY7EeDEJNVU2Y46RYvnFeUdTgwdNAkA30Xl9M=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			cvazTTyPQk/wopCnk/z780/VYwqRbNAuCl3aAUS1lX8=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			E4wkA4IwTzUDg7Au1WxpEDqUMcBUTrHsXc197HpVXdk=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			D0BHZNB6auLvnh4OjqrCeLfUiNYc8cCEFG8vM7SF8u0=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TQvTIoq0zD5RWfQze+lp7HtzNOJlyZt2M+Pa88P8+2I=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			KUTrxK8YlJUb+fElD05u34EcIYN0WVDqmoqSZxWILPc=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			vMDmRYJJQz6MumxYEit8DvqVV8vI+1+Tku7V0lefxws=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			X8JcMK7nZHfxxOkikxzIBoI98FlSVYP/VwVwXZ6RPBw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			YoZulVAcQ2symhVDI1V0PG79ZKN8+2W87ORlq2Ps8kA=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			utkRY4Y0P0pMOUvbhxRuSfZ09ofVK7hHvZ6BmP2jgsw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RiqP+P0FGoEA6MbAhvSX5AVqzlsgtEeR9Kq5ZLAQpEg=
			</data>
		</dict>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			5Tj49JNMpuHOKUFtKSFx8o5n2mxy7Z0ja6QvN0RepB4=
			</data>
		</dict>
		<key>Resources/_tk_data/listbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/f1M8xAtg7rTVW0OAIa18yD3RIS6v5BCHPUm/YufvrA=
			</data>
		</dict>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVQxBBVDj0dyKm14mqig/8z0xfaZQSwtacMs/+bRnPQ=
			</data>
		</dict>
		<key>Resources/_tk_data/menu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			q2bq3VH5lYAfzDQs9P5cE0KqvjcXr3WAqIHbUrMNY9w=
			</data>
		</dict>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CsnRHUBG702ObSGfaUG/acauRIxqHC9/w4L4S1eG9mA=
			</data>
		</dict>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			mmKB+woZJ9e4H86ev8lSNb2I3xFK2Kh6/qjqawlTM4o=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0bD+0L6lGz+vCNhjQDTHOIvnFI+bgHRgt9GFcG24QW8=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hebO5gAZJzdnJfkeqlXRez2eOGQ+F3VaQsBf5JHGO94=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Bt11cmJt9csKjTr/usm7dMsSRpB2g21m/RmuW1+rQsc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			HVbQp8B9NLuBZcukf6STUbi8Wp2yRCkLlgHFiF0WFVw=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Zzx2pIraCaFUywOFNL+Q47nAul/WsWGdszUH3mVVM2I=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+5PUVanZzz+CLJaN+yc+2THkM/JJTXHWtfjYPd5+rMI=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			XeET3Ezg3w2MVNSBLBXsMThxJ7+a/qAo0gxqWqjjq4U=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			kdxHGNyFZsNuS80MKSwB9GfKdmHv9gG4cKvN/kqU7Ls=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Cou7TR/Ye/epDd+lD0ckmUyc540fPpHPQMEXfbeUHcU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yBNOrRKeROnFBD4drYGmqQDw3nHbNGjiYDhAA4aH8dg=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			n4PdAwntYhEA8xh//NrlC3X1lzu+dK9VCnjvABBJXe0=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			09B6rXksDoP0cEswSTHqVJ0Sy7PZmlc9mBXpVKVxBwc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GZO07C3ACdLmyhhdC9Vl0/M6Tvp5uso55Pl/V01j8wU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rQ5GYTHTeJ3jIdnQWI4Z5GR7qC7eQe7m6+9GR4b4vb4=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yGPeurefloL9DVLYZOMo5zM9A/Tpp127NCwwgH79z/s=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			t26/ohvB6TegSgTlEivmS1ze4fR8cFi3HYuSPXDDsXs=
			</data>
		</dict>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9pKaXg0YvExmZiBsY6xKqmbtxLn0Vt/AgzAM+pWkS80=
			</data>
		</dict>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			dtvb+SFmeNSNFkD4/R4njnFASC4crHaAEnqaQlzGHe4=
			</data>
		</dict>
		<key>Resources/_tk_data/palette.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YLhXk2i7MGPxbSXwBzhREeDvjZe7KWsDZW3BduNR48o=
			</data>
		</dict>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			St9zixdpFInHHEudmmSxKWGtqGZ7gYVvetvGHf/q3yk=
			</data>
		</dict>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			M67mKs/JEFC0Jca2oS808TRZgHrumZ4ijzH0knFCiDM=
			</data>
		</dict>
		<key>Resources/_tk_data/safetk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			3bDNWcaPwKwhmQCgTfDMtXZEDdiBLEG8oguXRYCGCJI=
			</data>
		</dict>
		<key>Resources/_tk_data/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			WMZi3T0sZTeGsFqiyIgx9OlxuRBeSGnYZvthhug+02U=
			</data>
		</dict>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			GJ5+5LZ4YQAccUpViA2zSs99YmqBbhiwSyMq+ebjPoE=
			</data>
		</dict>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			oxkMV1c1vDxBmTQpt2Q/d5FqWk/rE7LX02wwh/vuVv4=
			</data>
		</dict>
		<key>Resources/_tk_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			jo7Oz9YEb+QT83qRkz7qCG4xlZs/vrEnr90FzZFBvpo=
			</data>
		</dict>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			rGYFSNXk937UeFjtJYi+QZMZJuR4iap+VSJ7UMfG1dw=
			</data>
		</dict>
		<key>Resources/_tk_data/text.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			OdcEKgf56iOOxPqQW+RNWTrHU9z1fuGULf5Mxa3hXzc=
			</data>
		</dict>
		<key>Resources/_tk_data/tk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			1G6LtphQhNNcLdTkDoe65Qz5wBbDrgewL9i2JN/HtcE=
			</data>
		</dict>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			2dmLyZ2Z0KmIOrUFTe1Rmrf+Rx4NHSRgpUN/I1rIyVE=
			</data>
		</dict>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			rkSN9v27pF1FCr7+8SeZ+DYhd7C5/gbzyjyw7aXmqlg=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kri+nYk0hQttJAuXBgOwrXxt1KRRNFRWlPtSlm10KGE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			vDFpQ5bs+YpgQNMohopIPB9ncJZAa1DqidWy9CWCiNY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aE7UCpYJyidc5vPlR/vymA48BA+t2vD9ObfL19WYc2Y=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			GdSa3g+4c6hvgMVp+7F+LWZ6YpIEVWP+HPFEnNG2jWE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			x7H0DXeCD7ryGV8rs/M0s4/sZT/kdlP54woBrUymO6U=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			7Fhd4oODfR9ZkvgtYyrKrSqgiy/xnwvbGaLl02Ju4Xc=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			u+Y5kDkTdxeOM33wskdHJCZ2X/mx6fkhhrDFaRnHRqA=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kl2OcknhDLnjrlqJ/d3ShCXHItIYcV8326wT6TO9AZ8=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			SCKCpbxapGRZCR+DMavGmNKsWqQ9jSQuYAZ+aQZZ+nE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			77Th1dzU9ZHDcHlpZq9YfVL+dRwpX+OPMh7I9xHfd5A=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			zKMx+xqkrmz4n49izPlD9QH3dxjDIi5zx/dQJJSvd7g=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			zPNbvc3nPf9ifGOT43ZxPxqRYgnhd+ta7yI+VM3d51s=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xfgBQUqpc9dxUOLNp6cia/LVZ4geQecB9g8C6vgkPMM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qcNPWV5UfOlO5l4nxBUZXSshBlOp/8+zlVnF4PqcBvg=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YWHEC/XWz+nINqqyNw78lRvD4UvBfdlHdFW4a1JS1pY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			7lJh/g/APlT0F6uwMuUokYX4aY1grnTbaQMv7estEqk=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CzgY33zw1v8XucWImrcwg+Z3KcOg1rj+DAYwD7bS3D4=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			UTLjkRKRZIbEkBjBv/unDzDfYHsQPiKuXUPHHavoy0A=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Qvy3Awa4rUg/omSdfJsTiRsrhB4m0pT1PaMi2kiMYlQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LViSB46jMef5Z/RqtRDxPokMnuGF1ZfZ+zvEwp5ADs8=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			VPrnVq+l3tivKH1uOTFbpEYWBaeGhcptMMgd/q59YEk=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			4kAUxXeTlednvn1DUCL28Wn1CUm7vtfnmy3mrmXgxRM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			BfrQWCgOeolHqfcRIrRCuS19V4tGGLCL8Lcbbaxaoi8=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			jktM4LNYRdmGqTrVZM0SOKHoo5AuklkAMFjwrsAOMMs=
			</data>
		</dict>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			402CjnQPFRuWAik0qux7uDQ+I9BA+1TARkGIj1F2frg=
			</data>
		</dict>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			nHipdrvJM4Y/sOTCPuYrJvjrPX8QHX0y5naFeUmeQ7E=
			</data>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			mkfPZUj+tugg19B1139ZrfDk4Wzhh3CXIJO/HOBFgiM=
			</data>
		</dict>
		<key>Resources/certifi/cacert.pem</key>
		<dict>
			<key>hash2</key>
			<data>
			LBHDzgj/xA05AxnHK8ENT5COnGNElNZe0svFUHMf1SQ=
			</data>
		</dict>
		<key>Resources/certifi/py.typed</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/charset_normalizer</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/charset_normalizer</string>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libcrypto.1.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.1.1.dylib</string>
		</dict>
		<key>Resources/libssl.1.1.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.1.1.dylib</string>
		</dict>
		<key>Resources/libtcl8.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libtcl8.6.dylib</string>
		</dict>
		<key>Resources/libtk8.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libtk8.6.dylib</string>
		</dict>
		<key>Resources/tb.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			+ZavwHb4E9NsITH0be3Nc17h6c1ZYDsjG8yaioJ0d9E=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform-1.0.18.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			JGMAZFca3o0nv+/4TK4J/+xFKg7DzNmE/zHop4IcJ+E=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			L4EqBVBxa4iTAXSoyiRWmEJ80oZoDAloVYriaatSRA0=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			1rnKuwNTnhFq7LgO/Hpp1D+TYHX2rVx336XUba193H8=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/tcltest-2.5.3.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			GE7W2tR1bvgiMgKYzrjAW+IoAYpuwltTQHe2qTU/92s=
			</data>
		</dict>
		<key>Resources/tcl8/8.6/http-2.9.5.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			IlBB/hYMB3jbsHE0y5g26D6PsoGIlYrXjrmZL1ZVdis=
			</data>
		</dict>
		<key>Resources/tcl8/8.6/tdbc/sqlite3-1.1.3.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			k/djgZdJoEMEhZYN2JqKowUd7nslIhQoF+CSPi3t4Cw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
