#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区间冲突分析器
分析Excel文件中green_interval和overInterval列的数据冲突
"""

import pandas as pd
import re
from typing import List, Tuple, Dict, Any
import sys

class IntervalConflictAnalyzer:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.data = None
        
    def load_data(self):
        """加载Excel数据"""
        try:
            self.data = pd.read_excel(self.excel_path)
            print(f"成功加载数据，共 {len(self.data)} 行")
            print("列名:", list(self.data.columns))
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def parse_green_interval(self, interval_str: str) -> List[Tuple[float, float, str]]:
        """
        解析green_interval列的区间表达式
        返回: [(min_val, max_val, operator), ...]
        """
        if pd.isna(interval_str) or not isinstance(interval_str, str):
            return []

        intervals = []
        interval_str = str(interval_str).strip()

        # 跳过包含变量的表达式（如V2, Vref等）
        if re.search(r'[A-Za-z]', interval_str):
            print(f"跳过包含变量的表达式: {interval_str}")
            return []

        # 处理各种格式
        if interval_str.startswith('≤') or interval_str.startswith('<='):
            # ≤30, ≤15 等
            match = re.match(r'[≤<=](\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((float('-inf'), val, '<='))
        elif interval_str.startswith('≥') or interval_str.startswith('>='):
            # ≥47, ≥600 等
            match = re.match(r'[≥>=](\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((val, float('inf'), '>='))
        elif interval_str.startswith('<'):
            # <30, <15 等
            match = re.match(r'<(\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((float('-inf'), val, '<'))
        elif interval_str.startswith('>'):
            # >47, >600 等
            match = re.match(r'>(\d+(?:\.\d+)?)', interval_str)
            if match:
                val = float(match.group(1))
                intervals.append((val, float('inf'), '>'))
        elif '[' in interval_str and ']' in interval_str:
            # [1-20], [3,5] 等
            match = re.match(r'\[([^,\-\]]+)[\-,]([^\]]+)\]', interval_str)
            if match:
                try:
                    min_val = float(match.group(1))
                    max_val = float(match.group(2))
                    intervals.append((min_val, max_val, '[]'))
                except ValueError:
                    print(f"无法解析区间: {interval_str}")
        elif '(' in interval_str and ')' in interval_str:
            # (-4), (3,5) 等
            match = re.match(r'\(([^,\-\)]+)(?:[\-,]([^\)]+))?\)', interval_str)
            if match:
                try:
                    if match.group(2):  # 区间
                        min_val = float(match.group(1))
                        max_val = float(match.group(2))
                        intervals.append((min_val, max_val, '()'))
                    else:  # 单值
                        val = float(match.group(1))
                        intervals.append((val, val, '='))
                except ValueError:
                    print(f"无法解析区间: {interval_str}")

        return intervals
    
    def parse_over_interval(self, interval_str: str) -> List[Tuple[float, float, str]]:
        """
        解析overInterval列的复杂区间表达式
        提取所有数值比较条件，忽略"扣多少分"的内容
        """
        if pd.isna(interval_str) or not isinstance(interval_str, str):
            return []
        
        intervals = []
        interval_str = str(interval_str).strip()
        
        # 使用正则表达式提取所有的数值比较条件
        # 匹配模式: 测量值<40.00, 测量值<=18.00, 测量值>20.00, 测量值>=47.00 等
        patterns = [
            r'测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',  # 测量值<40.00, 测量值>=47.00
            r'(\d+(?:\.\d+)?)\s*([<>]=?)\s*测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',  # -5.00<=测量值<-3.00
            r'测量值\s*([<>]=?)\s*(\d+(?:\.\d+)?)',  # 单独的测量值条件
        ]
        
        # 查找所有数值比较
        for pattern in patterns:
            matches = re.findall(pattern, interval_str)
            for match in matches:
                if len(match) == 2:  # 简单比较 测量值<40.00
                    operator, value = match
                    val = float(value)
                    if operator == '<':
                        intervals.append((float('-inf'), val, '<'))
                    elif operator == '<=':
                        intervals.append((float('-inf'), val, '<='))
                    elif operator == '>':
                        intervals.append((val, float('inf'), '>'))
                    elif operator == '>=':
                        intervals.append((val, float('inf'), '>='))
                elif len(match) == 4:  # 区间比较 -5.00<=测量值<-3.00
                    min_val, min_op, max_op, max_val = match
                    min_val = float(min_val)
                    max_val = float(max_val)
                    intervals.append((min_val, max_val, f'{min_op}..{max_op}'))
        
        return intervals
    
    def check_conflicts(self, green_intervals: List[Tuple[float, float, str]], 
                       over_intervals: List[Tuple[float, float, str]]) -> List[Dict[str, Any]]:
        """
        检查两个区间列表之间的冲突
        """
        conflicts = []
        
        for g_min, g_max, g_op in green_intervals:
            for o_min, o_max, o_op in over_intervals:
                # 检查区间是否有重叠
                overlap_start = max(g_min, o_min)
                overlap_end = min(g_max, o_max)
                
                if overlap_start < overlap_end:
                    conflicts.append({
                        'green_interval': (g_min, g_max, g_op),
                        'over_interval': (o_min, o_max, o_op),
                        'overlap_range': (overlap_start, overlap_end),
                        'conflict_type': 'range_overlap'
                    })
        
        return conflicts
    
    def analyze_conflicts(self):
        """分析所有行的冲突"""
        if self.data is None:
            print("请先加载数据")
            return
        
        print("\n=== 开始分析区间冲突 ===")
        
        total_conflicts = 0
        
        for index, row in self.data.iterrows():
            print(f"\n--- 第 {index + 1} 行分析 ---")
            
            green_col = 'green_interval'
            over_col = 'overInterval'
            
            # 检查列是否存在
            if green_col not in self.data.columns or over_col not in self.data.columns:
                print(f"列名不匹配，实际列名: {list(self.data.columns)}")
                continue
            
            green_val = row[green_col]
            over_val = row[over_col]
            
            print(f"green_interval: {green_val}")
            print(f"overInterval: {over_val}")
            
            # 解析区间
            green_intervals = self.parse_green_interval(green_val)
            over_intervals = self.parse_over_interval(over_val)
            
            print(f"解析的green区间: {green_intervals}")
            print(f"解析的over区间: {over_intervals}")
            
            # 检查冲突
            conflicts = self.check_conflicts(green_intervals, over_intervals)
            
            if conflicts:
                print(f"⚠️  发现 {len(conflicts)} 个冲突:")
                for i, conflict in enumerate(conflicts, 1):
                    g_interval = conflict['green_interval']
                    o_interval = conflict['over_interval']
                    overlap = conflict['overlap_range']
                    print(f"  冲突 {i}: green区间 {g_interval} 与 over区间 {o_interval} 在 {overlap} 范围内重叠")
                total_conflicts += len(conflicts)
            else:
                print("✅ 未发现冲突")
        
        print(f"\n=== 分析完成，总共发现 {total_conflicts} 个冲突 ===")

def main():
    excel_path = "./交集判定/111.xlsx"
    
    analyzer = IntervalConflictAnalyzer(excel_path)
    
    if analyzer.load_data():
        analyzer.analyze_conflicts()
    else:
        print("无法加载数据文件")

if __name__ == "__main__":
    main()
